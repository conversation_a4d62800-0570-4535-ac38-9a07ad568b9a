import { createRouter, createWebHistory } from 'vue-router'
import AppEditor from '../pages/AppEditor.vue'
import AppPreview from '../pages/AppPreview.vue'
import TestPage from '../pages/TestPage.vue'
import DebugPreview from '../pages/DebugPreview.vue'

const routes = [
  {
    path: '/',
    name: 'Editor',
    component: AppEditor
  },
  {
    path: '/preview',
    name: 'Preview',
    component: AppPreview
  },
  {
    path: '/debug',
    name: 'DebugPreview',
    component: DebugPreview
  },
  {
    path: '/test',
    name: 'Test',
    component: TestPage
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
