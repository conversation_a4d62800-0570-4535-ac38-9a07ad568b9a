<template>
  <div class="app-container">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <AppToolbar
        @preview="handlePreview"
        @save="handleSave"
        @clear="clearCanvas"
        @update:size="handleUpdateCanvasSize"
        :canvas-components="components"
      />
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 左侧侧边栏面板 -->
      <div class="sidebar-panel-container">
        <SidebarPanel
          :global-variables="canvasSchema.variables"
          :global-methods="canvasSchema.methods"
          :global-mounted="canvasSchema.mounted"
          :global-styles="canvasSchema.styles"
          @drag-start="handleDragStart"
          @update-variables="handleUpdateVariables"
          @update-methods="handleUpdateMethods"
          @update-mounted="handleUpdateMounted"
          @update-styles="handleUpdateStyles"
        />
      </div>

      <!-- 中间画布 -->
      <div
        ref="canvasWrapperRef"
        class="canvas-wrapper"
        :data-scale-mode="scaleMode"
        :data-scale-small="effectiveScale < 0.5"
        :data-scale-large="effectiveScale > 1.5"
        @resize="handleResize"
      >
        <EditorCanvas
          :components="components"
          :selected-component="selectedComponent"
          :selected-form-child="selectedFormChild"
          :selected-form-row="selectedFormRow"
          :selected-form-column="selectedFormColumn"
          :form-container="currentFormContainer"
          :canvas-size="canvasSize"
          :style="{
            width: `${canvasSize.width}px`,
            height: `${canvasSize.height}px`,
            ...canvasTransformStyle
          }"
          @select-component="handleSelectComponent"
          @update-component="handleUpdateComponent"
          @delete-component="handleDeleteComponent"
          @drop="handleDrop"
          @create-component="handleCreateComponent"
          @split-component="handleSplitComponent"
          @select-form-child="handleSelectFormChild"
          @update-form-child="handleUpdateFormChild"
          @delete-form-child="handleDeleteFormChild"
          @reorder-form-children="handleReorderFormChildren"
          @add-form-row="handleAddFormRow"
          @select-form-row="handleSelectFormRow"
          @update-form-row="handleUpdateFormRow"
          @delete-form-row="handleDeleteFormRow"
          @reorder-form-rows="handleReorderFormRows"
          @split-form-column="handleSplitFormColumn"
          @delete-form-column="handleDeleteFormColumn"
          @drop-to-column="handleDropToColumn"
          @delete-column-form-child="handleDeleteColumnFormChild"
          @select-form-column="handleSelectFormColumn"
          @move-form-child-between-columns="handleMoveFormChildBetweenColumns"
          @start-adjust-position="handleStartAdjustPosition"
          @column-click-for-adjust="handleColumnClickForAdjust"
          @delete-form-child-by-delete-key="handleDeleteFormChildByDeleteKey"
          @delete-form-row-by-delete-key="handleDeleteFormRowByDeleteKey"
        />

        <!-- 缩放状态显示 -->
        <div class="scale-status">
          <div class="scale-info">
            <span class="scale-percentage"
              >{{ Math.round(effectiveScale * 100) }}%</span
            >
            <span class="scale-mode-text">{{ scaleModeText }}</span>
          </div>
          <div class="canvas-size-info">
            {{ canvasSize.width }} × {{ canvasSize.height }}
          </div>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="right-panel">
        <PropertyPanel
          v-if="
            selectedComponent ||
            selectedFormChild ||
            selectedFormColumn ||
            selectedFormRow
          "
          :selected-component="selectedComponent"
          :selected-form-child="selectedFormChild"
          :selected-form-column="selectedFormColumn"
          :selected-form-row="selectedFormRow"
          :form-container="currentFormContainer"
          :canvas-size="canvasSize"
          :all-components="components"
          :global-methods="canvasSchema.methods"
          :global-variables="canvasSchema.variables"
          @update-property="handleUpdateProperty"
          @update-form-child-property="handleUpdateFormChildProperty"
          @update-form-column-property="handleUpdateFormColumnProperty"
          @update-form-row-property="handleUpdateFormRowProperty"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
  import {
    ref,
    provide,
    reactive,
    computed,
    watch,
    onMounted,
    onUnmounted
  } from 'vue'
  import AppToolbar from '../components/AppToolbar.vue'
  import SidebarPanel from '../components/SidebarPanel.vue'
  import EditorCanvas from '../components/EditorCanvas.vue'
  import PropertyPanel from '../components/PropertyPanel.vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { cloneDeep } from 'lodash-es'
  import { useEditorStore } from '../store/editor'
  import { hierarchicalSchemaExample } from '../utils/schemaExample.js'

  const editorStore = useEditorStore()
  const components = ref([])
  const selectedComponent = computed(() => editorStore.selectedComponent)
  const selectedFormChild = ref(null)
  const selectedFormRow = ref(null)
  const selectedFormColumn = ref(null)
  const draggedComponent = ref(null)

  // 获取当前选中表单子组件所属的表单容器
  const currentFormContainer = computed(() => {
    if (!selectedFormChild.value) return null

    // 查找表单容器
    return components.value.find(comp => comp.type === 'form-container')
  })

  const canvasSize = reactive({
    width: 1920,
    height: 1080,
    isResponsive: false
  })

  // 画布缩放相关状态
  const canvasScale = ref(1)
  const canvasWrapperRef = ref(null)
  const scaleMode = ref('auto') // 'auto', 'fit', 'manual'
  const minScale = 0.1
  const maxScale = 3
  const scaleStep = 0.1

  // 计算自适应缩放比例
  const calculateAutoScale = () => {
    if (!canvasWrapperRef.value) return 1

    const wrapper = canvasWrapperRef.value
    const containerWidth = wrapper.clientWidth - 40 // 减去padding
    const containerHeight = wrapper.clientHeight - 40

    const scaleX = containerWidth / canvasSize.width
    const scaleY = containerHeight / canvasSize.height

    return Math.min(scaleX, scaleY, 1) // 不超过1倍
  }

  // 计算填充缩放比例
  const calculateFitScale = () => {
    if (!canvasWrapperRef.value) return 1

    const wrapper = canvasWrapperRef.value
    const containerWidth = wrapper.clientWidth - 40
    const containerHeight = wrapper.clientHeight - 40

    const scaleX = containerWidth / canvasSize.width
    const scaleY = containerHeight / canvasSize.height

    return Math.min(scaleX, scaleY) // 可以超过1倍
  }

  // 当前有效缩放比例
  const effectiveScale = computed(() => {
    if (scaleMode.value === 'auto') {
      return calculateAutoScale()
    } else if (scaleMode.value === 'fit') {
      return calculateFitScale()
    } else {
      return canvasScale.value
    }
  })

  // 缩放模式文本
  const scaleModeText = computed(() => {
    switch (scaleMode.value) {
      case 'auto':
        return '自适应'
      case 'fit':
        return '适应屏幕'
      case 'manual':
        return '手动'
      default:
        return '自适应'
    }
  })

  // 画布变换样式
  const canvasTransformStyle = computed(() => ({
    transform: `scale(${effectiveScale.value})`,
    transformOrigin: 'center top',
    transition: 'transform 0.3s ease'
  }))

  // 缩放控制方法
  const scaleCanvas = delta => {
    scaleMode.value = 'manual'
    const newScale = Math.max(
      minScale,
      Math.min(maxScale, canvasScale.value + delta)
    )
    canvasScale.value = newScale
  }

  const zoomIn = () => {
    scaleCanvas(scaleStep)
  }

  const zoomOut = () => {
    scaleCanvas(-scaleStep)
  }

  const fitToScreen = () => {
    scaleMode.value = 'fit'
  }

  const autoScale = () => {
    scaleMode.value = 'auto'
  }

  const resetScale = () => {
    scaleMode.value = 'manual'
    canvasScale.value = 1
  }

  // 设置具体缩放比例
  const setScale = scale => {
    scaleMode.value = 'manual'
    canvasScale.value = Math.max(minScale, Math.min(maxScale, scale))
  }

  // 监听容器尺寸变化
  const handleResize = () => {
    if (scaleMode.value === 'auto' || scaleMode.value === 'fit') {
      // 触发重新计算，通过改变一个响应式属性来触发
      canvasWrapperRef.value && (canvasWrapperRef.value.style.display = 'flex')
    }
  }

  // 暴露缩放相关方法给子组件
  const scaleControls = {
    zoomIn,
    zoomOut,
    fitToScreen,
    autoScale,
    resetScale,
    setScale,
    currentScale: effectiveScale,
    scaleMode
  }

  provide('scaleControls', scaleControls)

  // 画布Schema结构
  const canvasSchema = computed(() => editorStore.schema)

  // 提供全局状态
  provide('components', components)
  provide('selectedComponent', selectedComponent)

  // localStorage 相关常量
  const STORAGE_KEY = 'bi-editor-canvas-schema'

  // 保存画布数据到localStorage
  const saveCanvasToStorage = () => {
    try {
      // 更新schema中的components
      editorStore.updateSchemaComponents(components.value)

      const canvasData = {
        ...canvasSchema.value,
        timestamp: Date.now()
      }
      localStorage.setItem(STORAGE_KEY, JSON.stringify(canvasData))
    } catch (error) {
      console.warn('保存画布数据到localStorage失败:', error)
    }
  }

  // 从localStorage加载画布数据
  const loadCanvasFromStorage = () => {
    try {
      const savedData = localStorage.getItem(STORAGE_KEY)
      if (savedData) {
        const canvasData = JSON.parse(savedData)

        // 兼容旧版本数据结构
        if (canvasData.components && Array.isArray(canvasData.components)) {
          components.value = canvasData.components

          // 如果是新版本schema结构
          if (canvasData.variables !== undefined) {
            editorStore.updateSchema({
              variables: canvasData.variables || {},
              methods: canvasData.methods || '',
              mounted: canvasData.mounted || '',
              styles: canvasData.styles || '',
              version: canvasData.version || '2.0'
            })
          }

          // 恢复画布尺寸
          if (canvasData.canvas || canvasData.canvasSize) {
            const canvas = canvasData.canvas || canvasData.canvasSize
            canvasSize.width = canvas.width
            canvasSize.height = canvas.height
          }

          console.log('已从localStorage加载画布数据')
          return true
        }
      }
    } catch (error) {
      console.warn('从localStorage加载画布数据失败:', error)
    }
    return false
  }

  // 清空画布
  const clearCanvas = () => {
    components.value = []
    editorStore.updateSchema({
      variables: {},
      methods: '',
      mounted: '',
      styles: '',
      components: []
    })
    editorStore.setSelectedComponent(null)
    selectedFormChild.value = null
    selectedFormRow.value = null
    selectedFormColumn.value = null
    // 清除localStorage
    localStorage.removeItem(STORAGE_KEY)
    ElMessage.success('画布已清空')
  }

  // 监听components变化，自动保存
  watch(
    () => components.value,
    () => {
      saveCanvasToStorage()
    },
    { deep: true }
  )

  // 监听画布尺寸变化，自动保存
  watch(
    () => canvasSize,
    () => {
      saveCanvasToStorage()
    },
    { deep: true }
  )

  // 监听canvasSchema变化，自动保存
  watch(
    () => canvasSchema,
    () => {
      saveCanvasToStorage()
    },
    { deep: true }
  )

  // 页面初始化时加载数据
  onMounted(() => {
    const hasData = loadCanvasFromStorage()

    // 如果没有加载到数据，初始化示例变量数据
    if (!hasData && Object.keys(editorStore.schema.variables).length === 0) {
      editorStore.updateSchema({
        variables: hierarchicalSchemaExample.variables || {}
      })
      console.log('已加载示例变量数据')
    }

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)

    // 初始化缩放
    setTimeout(() => {
      if (scaleMode.value === 'auto') {
        // 触发自适应缩放计算
        handleResize()
      }
    }, 100)
  })

  // 组件卸载时清理监听器
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })

  // 处理变量更新
  const handleUpdateVariables = variables => {
    editorStore.updateSchema({ variables })
  }

  // 处理方法更新
  const handleUpdateMethods = methods => {
    editorStore.updateSchema({ methods })
  }

  // 处理生命周期更新
  const handleUpdateMounted = mounted => {
    editorStore.updateSchema({ mounted })
  }

  // 处理样式更新
  const handleUpdateStyles = styles => {
    editorStore.updateSchema({ styles })
  }

  // 生成变量名称
  const generateVariableName = componentType => {
    const nameMap = {
      input: 'name',
      select: 'selection',
      datepicker: 'date',
      button: 'action'
    }
    return nameMap[componentType] || 'value'
  }

  // 根据组件类型获取默认值
  const getDefaultValueByType = componentType => {
    const defaultMap = {
      input: '',
      select: '',
      datepicker: '',
      button: false
    }
    return defaultMap[componentType] || ''
  }

  // 处理拖拽开始
  const handleDragStart = component => {
    draggedComponent.value = component
  }

  // 处理组件放置
  const handleDrop = dropData => {
    console.log('🔥 handleDrop 被调用:', {
      draggedComponent: draggedComponent.value,
      dropData,
      timestamp: Date.now()
    })

    if (!draggedComponent.value) {
      console.log('🔥 draggedComponent 为空，退出')
      return
    }

    const { x, y, targetContainer } = dropData

    if (targetContainer) {
      if (targetContainer.type === 'form-container') {
        // 拖拽到表单容器 - 检查是否为表单组件
        const formComponentTypes = ['input', 'select', 'datepicker', 'button']

        if (formComponentTypes.includes(draggedComponent.value.type)) {
          console.log('🔥 添加组件到表单容器')
          // 自动创建变量绑定（如果是需要绑定的组件类型）
          const needsBinding = ['input', 'select', 'datepicker'].includes(
            draggedComponent.value.type
          )
          let bindVariable = null

          if (needsBinding) {
            const timestamp = Date.now()
            const varName = generateVariableName(draggedComponent.value.type)
            bindVariable = `var_${timestamp}`

            // 添加变量到表单容器的variables数组
            const currentVariables = targetContainer.config?.variables || []
            const newVariable = {
              id: bindVariable,
              name: varName,
              type: 'string',
              defaultValue: getDefaultValueByType(draggedComponent.value.type),
              description: `${draggedComponent.value.name}的绑定变量`
            }

            // 更新全局变量（添加到formData中）
            const formDataKey = `formData_${targetContainer.id}`
            if (!canvasSchema.value.variables[formDataKey]) {
              canvasSchema.value.variables[formDataKey] = {}
            }
            canvasSchema.value.variables[formDataKey][varName] =
              newVariable.defaultValue

            targetContainer.config.variables = [
              ...currentVariables,
              newVariable
            ]
          }

          const childComponent = {
            id: `comp_${Date.now()}`,
            type: draggedComponent.value.type,
            config: {
              ...cloneDeep(draggedComponent.value.defaultConfig),
              // 继承表单容器的尺寸设置
              size: targetContainer.config?.size || 'default',
              // 自动绑定变量
              ...(bindVariable && { bindVariable })
            }
          }

          // 更新表单容器，添加子组件
          const containerIndex = components.value.findIndex(
            c => c.id === targetContainer.id
          )
          if (containerIndex !== -1) {
            const currentChildren = targetContainer.config?.children || []
            components.value[containerIndex] = {
              ...targetContainer,
              config: {
                ...targetContainer.config,
                children: [...currentChildren, childComponent]
              }
            }
            ElMessage.success(
              `已将${draggedComponent.value.name}添加到表单容器${bindVariable ? '并自动创建变量绑定' : ''}`
            )
          }
        } else {
          ElMessage.warning('只能将表单元素拖拽到表单容器中')
        }
      } else {
        // 拖拽到DIV容器内部 - 创建或替换插槽子组件
        console.log('🔥 添加组件到DIV容器')
        const containerPadding = targetContainer.config?.padding || 8
        const titleHeight = targetContainer.config?.showTitleBar
          ? targetContainer.config?.titleHeight || 32
          : 0

        // 如果容器已经有插槽子组件，先删除原有的
        if (
          targetContainer.config?.hasSlotChild &&
          targetContainer.config?.slotChildId
        ) {
          const existingSlotChildIndex = components.value.findIndex(
            c => c.id === targetContainer.config.slotChildId
          )
          if (existingSlotChildIndex !== -1) {
            components.value.splice(existingSlotChildIndex, 1)
            console.log('🔥 删除原有的插槽子组件')
          }
        }

        const slotComponent = {
          id: `comp_${Date.now()}`,
          type: draggedComponent.value.type,
          dataSource: draggedComponent.value.dataSource,
          position: {
            x: targetContainer.position.x + containerPadding,
            y: targetContainer.position.y + titleHeight + containerPadding
          },
          size: {
            width: targetContainer.size.width - 2 * containerPadding,
            height:
              targetContainer.size.height - titleHeight - 2 * containerPadding
          },
          config: {
            ...cloneDeep(draggedComponent.value.defaultConfig),
            isSlotChild: true,
            parentContainerId: targetContainer.id,
            fillContainer: true
          }
        }

        console.log('🔥 创建的插槽组件:', slotComponent)

        // 更新目标容器，标记其有子组件
        const containerIndex = components.value.findIndex(
          c => c.id === targetContainer.id
        )
        if (containerIndex !== -1) {
          components.value[containerIndex] = {
            ...targetContainer,
            config: {
              ...targetContainer.config,
              hasSlotChild: true,
              slotChildId: slotComponent.id
            }
          }
        }

        console.log(
          '🔥 添加插槽组件到 components 数组前，当前数组长度:',
          components.value.length
        )
        components.value.push(slotComponent)
        console.log(
          '🔥 添加插槽组件到 components 数组后，当前数组长度:',
          components.value.length
        )

        editorStore.setSelectedComponent(slotComponent)
        ElMessage.success('组件已添加到容器内部')
      }
    } else {
      // 拖拽到画布空白区域 - 正常创建组件
      console.log('🔥 添加组件到画布空白区域')
      const componentSize = draggedComponent.value.defaultSize || {
        width: 400,
        height: 300
      }

      const newComponent = {
        id: `comp_${Date.now()}`,
        type: draggedComponent.value.type,
        dataSource: draggedComponent.value.dataSource,
        position: { x, y },
        size: componentSize,
        config: cloneDeep(draggedComponent.value.defaultConfig)
      }

      console.log('🔥 创建的新组件:', newComponent)

      // 如果是表单容器，自动创建formData变量
      if (draggedComponent.value.type === 'form-container') {
        const formDataKey = `formData_${newComponent.id}`
        canvasSchema.value.variables[formDataKey] = {}
        ElMessage.success('已创建表单容器并自动生成formData变量')
      }

      console.log(
        '🔥 添加组件到 components 数组前，当前数组长度:',
        components.value.length
      )
      components.value.push(newComponent)
      console.log(
        '🔥 添加组件到 components 数组后，当前数组长度:',
        components.value.length
      )

      editorStore.setSelectedComponent(newComponent)
    }

    console.log('🔥 handleDrop 完成，设置 draggedComponent 为 null')
    draggedComponent.value = null
  }

  // 处理组件选择
  const handleSelectComponent = component => {
    editorStore.setSelectedComponent(component)
    // 选中普通组件时，清除表单子组件、表单行和表单列选中状态
    if (!component) {
      selectedFormChild.value = null
      selectedFormRow.value = null
      selectedFormColumn.value = null
    }
  }

  // 处理组件更新
  const handleUpdateComponent = updatedComponent => {
    const index = components.value.findIndex(c => c.id === updatedComponent.id)
    if (index !== -1) {
      components.value[index] = updatedComponent

      // 如果更新的是子容器，需要同步调整兄弟容器
      if (updatedComponent.config?.isChildContainer) {
        syncSiblingContainers(updatedComponent)
      }

      // 如果更新的是有插槽子组件的容器，需要同步调整子组件尺寸
      if (
        updatedComponent.config?.hasSlotChild &&
        updatedComponent.config?.slotChildId
      ) {
        syncSlotChildSize(updatedComponent)
      }

      // 如果更新的是容器组（父容器），需要同步调整所有子容器
      if (updatedComponent.config?.isContainer) {
        recalculateChildContainersLayout(updatedComponent)
      }
    }
  }

  // 同步兄弟容器的位置和尺寸
  const syncSiblingContainers = updatedChild => {
    const parentId = updatedChild.config.parentId
    const parentDirection = updatedChild.config.parentDirection
    const childIndex = updatedChild.config.childIndex

    // 找到所有兄弟容器
    const siblings = components.value.filter(
      c =>
        c.config?.isChildContainer &&
        c.config?.parentId === parentId &&
        c.id !== updatedChild.id
    )

    if (siblings.length === 0) return

    const sibling = siblings[0] // 应该只有一个兄弟容器
    const siblingIndex = components.value.findIndex(c => c.id === sibling.id)

    if (siblingIndex === -1) return

    if (parentDirection === 'horizontal') {
      // 左右布局：调整兄弟容器的位置和宽度
      if (childIndex === 0) {
        // 更新的是左侧容器，调整右侧容器
        const newRightX = updatedChild.position.x + updatedChild.size.width + 8 // 8px gap
        const newRightWidth =
          updatedChild.position.x +
          updatedChild.size.width +
          sibling.size.width +
          8 -
          newRightX

        components.value[siblingIndex] = {
          ...sibling,
          position: { ...sibling.position, x: newRightX },
          size: { ...sibling.size, width: Math.max(50, newRightWidth) }
        }
      } else {
        // 更新的是右侧容器，调整左侧容器
        const newLeftWidth = updatedChild.position.x - sibling.position.x - 8 // 8px gap

        components.value[siblingIndex] = {
          ...sibling,
          size: { ...sibling.size, width: Math.max(50, newLeftWidth) }
        }
      }
    } else {
      // 上下布局：调整兄弟容器的位置和高度
      if (childIndex === 0) {
        // 更新的是上部容器，调整下部容器
        const newBottomY =
          updatedChild.position.y + updatedChild.size.height + 8 // 8px gap
        const newBottomHeight =
          updatedChild.position.y +
          updatedChild.size.height +
          sibling.size.height +
          8 -
          newBottomY

        components.value[siblingIndex] = {
          ...sibling,
          position: { ...sibling.position, y: newBottomY },
          size: { ...sibling.size, height: Math.max(50, newBottomHeight) }
        }
      } else {
        // 更新的是下部容器，调整上部容器
        const newTopHeight = updatedChild.position.y - sibling.position.y - 8 // 8px gap

        components.value[siblingIndex] = {
          ...sibling,
          size: { ...sibling.size, height: Math.max(50, newTopHeight) }
        }
      }
    }
  }

  // 同步插槽子组件尺寸
  const syncSlotChildSize = containerComponent => {
    const slotChildIndex = components.value.findIndex(
      c => c.id === containerComponent.config.slotChildId
    )

    if (slotChildIndex === -1) return

    const containerPadding = containerComponent.config?.padding || 8
    const titleHeight = containerComponent.config?.showTitleBar
      ? containerComponent.config?.titleHeight || 32
      : 0

    const slotChild = components.value[slotChildIndex]
    components.value[slotChildIndex] = {
      ...slotChild,
      position: {
        x: containerComponent.position.x + containerPadding,
        y: containerComponent.position.y + titleHeight + containerPadding
      },
      size: {
        width: containerComponent.size.width - 2 * containerPadding,
        height:
          containerComponent.size.height - titleHeight - 2 * containerPadding
      }
    }
  }

  // 重新计算子容器布局（针对父容器尺寸、位置或配置变化）
  const recalculateChildContainersLayout = parentComponent => {
    // 找到所有子容器
    const childContainers = components.value.filter(
      c =>
        c.config?.isChildContainer && c.config?.parentId === parentComponent.id
    )

    if (childContainers.length !== 2) return // 只处理已拆分的容器

    const childGap = 8 // 子容器间的间隙
    const direction = parentComponent.config?.direction
    const parentPadding = parentComponent.config?.parentPadding || 12
    // 获取当前标题栏高度
    const titleBarHeight = parentComponent.config?.showTitleBar
      ? parentComponent.config?.titleHeight || 32
      : 0

    // 获取实际的父容器尺寸和位置 - 如果是铺满画布，使用画布尺寸和0位置
    const parentActualWidth = parentComponent.fullCanvas
      ? canvasSize.width
      : parentComponent.size.width
    const parentActualHeight = parentComponent.fullCanvas
      ? canvasSize.height
      : parentComponent.size.height
    const parentActualX = parentComponent.fullCanvas
      ? 0
      : parentComponent.position.x
    const parentActualY = parentComponent.fullCanvas
      ? 0
      : parentComponent.position.y

    childContainers.forEach(child => {
      const childIndex = components.value.findIndex(c => c.id === child.id)
      if (childIndex === -1) return

      if (direction === 'horizontal') {
        // 左右布局：重新计算子容器的位置和尺寸
        const availableWidth = parentActualWidth - 2 * parentPadding - childGap
        const childWidth = Math.floor(availableWidth / 2)
        const availableHeight =
          parentActualHeight - 2 * parentPadding - titleBarHeight

        if (child.config.childIndex === 0) {
          // 左侧容器
          components.value[childIndex] = {
            ...child,
            position: {
              x: parentActualX + parentPadding,
              y: parentActualY + parentPadding + titleBarHeight
            },
            size: {
              width: childWidth,
              height: availableHeight
            }
          }
        } else {
          // 右侧容器
          components.value[childIndex] = {
            ...child,
            position: {
              x: parentActualX + parentPadding + childWidth + childGap,
              y: parentActualY + parentPadding + titleBarHeight
            },
            size: {
              width: childWidth,
              height: availableHeight
            }
          }
        }
      } else {
        // 上下布局：重新计算子容器的位置和尺寸
        const availableWidth = parentActualWidth - 2 * parentPadding
        const availableHeight =
          parentActualHeight - 2 * parentPadding - titleBarHeight - childGap
        const childHeight = Math.floor(availableHeight / 2)

        if (child.config.childIndex === 0) {
          // 上部容器
          components.value[childIndex] = {
            ...child,
            position: {
              x: parentActualX + parentPadding,
              y: parentActualY + parentPadding + titleBarHeight
            },
            size: {
              width: availableWidth,
              height: childHeight
            }
          }
        } else {
          // 下部容器
          components.value[childIndex] = {
            ...child,
            position: {
              x: parentActualX + parentPadding,
              y:
                parentActualY +
                parentPadding +
                titleBarHeight +
                childHeight +
                childGap
            },
            size: {
              width: availableWidth,
              height: childHeight
            }
          }
        }
      }
    })
  }

  // 处理组件删除
  const handleDeleteComponent = async componentId => {
    const componentToDelete = components.value.find(c => c.id === componentId)
    const index = components.value.findIndex(c => c.id === componentId)

    if (index === -1) return

    // 如果删除的是容器组（有子容器的父容器），询问删除策略
    if (componentToDelete?.config?.isContainer) {
      const childContainers = components.value.filter(
        c => c.config?.isChildContainer && c.config?.parentId === componentId
      )

      if (childContainers.length > 0) {
        try {
          await ElMessageBox.confirm(
            '检测到该容器包含子容器，请选择删除方式：',
            '删除容器组',
            {
              confirmButtonText: '删除全部',
              cancelButtonText: '只删除父容器',
              distinguishCancelAndClose: true,
              type: 'warning',
              customClass: 'delete-container-dialog'
            }
          )

          // 用户选择"删除全部"
          deleteContainerWithChildren(componentToDelete, childContainers)
        } catch (action) {
          if (action === 'cancel') {
            // 用户选择"只删除父容器"
            deleteContainerOnly(componentToDelete, childContainers)
          }
          // 如果是 'close'，则取消删除操作
          return
        }
      } else {
        // 没有子容器的容器组，直接删除
        components.value.splice(index, 1)
        if (selectedComponent.value?.id === componentId) {
          editorStore.setSelectedComponent(null)
        }
      }
    } else {
      // 普通组件的删除逻辑
      deleteRegularComponent(componentToDelete, index)
    }
  }

  // 删除容器及其所有子容器
  const deleteContainerWithChildren = (parentContainer, childContainers) => {
    // 收集所有需要删除的插槽子组件
    const slotChildrenToDelete = []

    // 检查父容器的插槽子组件
    if (
      parentContainer.config?.hasSlotChild &&
      parentContainer.config?.slotChildId
    ) {
      const slotChild = components.value.find(
        c => c.id === parentContainer.config.slotChildId
      )
      if (slotChild) {
        slotChildrenToDelete.push(slotChild)
      }
    }

    // 检查所有子容器的插槽子组件
    childContainers.forEach(child => {
      if (child.config?.hasSlotChild && child.config?.slotChildId) {
        const slotChild = components.value.find(
          c => c.id === child.config.slotChildId
        )
        if (slotChild) {
          slotChildrenToDelete.push(slotChild)
        }
      }
    })

    // 删除所有插槽子组件
    slotChildrenToDelete.forEach(slotChild => {
      const slotChildIndex = components.value.findIndex(
        c => c.id === slotChild.id
      )
      if (slotChildIndex > -1) {
        components.value.splice(slotChildIndex, 1)
      }
    })

    // 删除所有子容器
    childContainers.forEach(child => {
      const childIndex = components.value.findIndex(c => c.id === child.id)
      if (childIndex > -1) {
        components.value.splice(childIndex, 1)
      }
    })

    // 删除父容器
    const parentIndex = components.value.findIndex(
      c => c.id === parentContainer.id
    )
    if (parentIndex > -1) {
      components.value.splice(parentIndex, 1)
    }

    if (selectedComponent.value?.id === parentContainer.id) {
      editorStore.setSelectedComponent(null)
    }

    ElMessage.success(
      `已删除容器组及${childContainers.length}个子容器${slotChildrenToDelete.length > 0 ? `和${slotChildrenToDelete.length}个内部组件` : ''}`
    )
  }

  // 只删除父容器，子容器变为独立容器
  const deleteContainerOnly = (parentContainer, childContainers) => {
    // 检查父容器是否有插槽子组件需要删除
    if (
      parentContainer.config?.hasSlotChild &&
      parentContainer.config?.slotChildId
    ) {
      const slotChildIndex = components.value.findIndex(
        c => c.id === parentContainer.config.slotChildId
      )
      if (slotChildIndex > -1) {
        components.value.splice(slotChildIndex, 1)
      }
    }

    // 将子容器转换为独立容器
    childContainers.forEach(child => {
      const childIndex = components.value.findIndex(c => c.id === child.id)
      if (childIndex > -1) {
        components.value[childIndex] = {
          ...child,
          config: {
            ...child.config,
            isChildContainer: false,
            parentId: undefined,
            childIndex: undefined,
            parentDirection: undefined,
            parentPadding: undefined,
            childGap: undefined
          }
        }
      }
    })

    // 删除父容器
    const parentIndex = components.value.findIndex(
      c => c.id === parentContainer.id
    )
    if (parentIndex > -1) {
      components.value.splice(parentIndex, 1)
    }

    if (selectedComponent.value?.id === parentContainer.id) {
      editorStore.setSelectedComponent(null)
    }

    ElMessage.success(
      `已删除父容器，${childContainers.length}个子容器已转为独立容器`
    )
  }

  // 删除普通组件
  const deleteRegularComponent = (componentToDelete, index) => {
    components.value.splice(index, 1)
    if (selectedComponent.value?.id === componentToDelete.id) {
      editorStore.setSelectedComponent(null)
    }

    // 如果删除的是插槽子组件，清理父容器配置
    if (componentToDelete?.config?.isSlotChild) {
      const parentContainerId = componentToDelete.config.parentContainerId
      const parentIndex = components.value.findIndex(
        c => c.id === parentContainerId
      )
      if (parentIndex > -1) {
        const parentComponent = components.value[parentIndex]
        components.value[parentIndex] = {
          ...parentComponent,
          config: {
            ...parentComponent.config,
            hasSlotChild: false,
            slotChildId: undefined
          }
        }
        ElMessage.info('已自动清理容器插槽配置')
      }
    }

    // 如果删除的是有插槽子组件的容器，同时删除子组件
    if (
      componentToDelete?.config?.hasSlotChild &&
      componentToDelete.config?.slotChildId
    ) {
      const slotChildIndex = components.value.findIndex(
        c => c.id === componentToDelete.config.slotChildId
      )
      if (slotChildIndex > -1) {
        components.value.splice(slotChildIndex, 1)
        ElMessage.info('已同时删除容器内的子组件')
      }
    }

    // 如果删除的是子容器，检查父容器是否需要清理
    if (componentToDelete?.config?.isChildContainer) {
      const parentId = componentToDelete.config.parentId
      const remainingChildren = components.value.filter(
        c => c.config?.isChildContainer && c.config?.parentId === parentId
      )

      // 如果没有剩余的子容器，将父容器恢复为普通容器
      if (remainingChildren.length === 0) {
        const parentIndex = components.value.findIndex(c => c.id === parentId)
        if (parentIndex > -1) {
          const parentComponent = components.value[parentIndex]
          components.value[parentIndex] = {
            ...parentComponent,
            config: {
              ...parentComponent.config,
              isContainer: false,
              direction: undefined,
              gap: undefined,
              childrenIds: undefined
            }
          }
          ElMessage.info('已自动恢复父容器为普通容器')
        }
      }
    }
  }

  // 处理创建组件
  const handleCreateComponent = newComponent => {
    components.value.push(newComponent)
    // 选中新创建的组件
    editorStore.setSelectedComponent(newComponent)
  }

  // 表单子组件选中处理
  const handleSelectFormChild = child => {
    selectedFormChild.value = child

    if (child) {
      // 同时选中父容器
      const formContainer = components.value.find(
        c =>
          c.type === 'form-container' &&
          c.config?.children?.some(ch => ch.id === child.id)
      )
      if (formContainer) {
        editorStore.setSelectedComponent(formContainer)
      }
      // 选中表单子组件时，取消表单行和表单列选中
      selectedFormRow.value = null
      selectedFormColumn.value = null
    } else {
      // 如果取消选中表单子组件，也取消表单行和表单列选中
      selectedFormRow.value = null
      selectedFormColumn.value = null
    }
  }

  // 表单子组件编辑处理
  const handleUpdateFormChild = child => {
    // 直接选中子组件以便在属性面板中编辑
    selectedFormChild.value = child
  }

  // 表单子组件删除处理
  const handleDeleteFormChild = (containerComponent, childIndex) => {
    const containerIndex = components.value.findIndex(
      c => c.id === containerComponent.id
    )
    if (containerIndex !== -1) {
      const newChildren = [...containerComponent.config.children]
      newChildren.splice(childIndex, 1)

      components.value[containerIndex] = {
        ...containerComponent,
        config: {
          ...containerComponent.config,
          children: newChildren
        }
      }

      // 清除选中状态
      selectedFormChild.value = null
      ElMessage.success('表单元素已删除')
    }
  }

  // 表单子组件重排序处理
  const handleReorderFormChildren = (
    containerComponent,
    fromIndex,
    direction
  ) => {
    const containerIndex = components.value.findIndex(
      c => c.id === containerComponent.id
    )
    if (containerIndex !== -1) {
      const newChildren = [...containerComponent.config.children]
      const toIndex = direction === 'up' ? fromIndex - 1 : fromIndex + 1

      if (toIndex >= 0 && toIndex < newChildren.length) {
        // 交换位置
        ;[newChildren[fromIndex], newChildren[toIndex]] = [
          newChildren[toIndex],
          newChildren[fromIndex]
        ]

        components.value[containerIndex] = {
          ...containerComponent,
          config: {
            ...containerComponent.config,
            children: newChildren
          }
        }

        ElMessage.success(`表单元素已${direction === 'up' ? '上移' : '下移'}`)
      }
    }
  }

  // 添加表单行处理
  const handleAddFormRow = ({ component }) => {
    const containerIndex = components.value.findIndex(
      c => c.id === component.id
    )
    if (containerIndex !== -1) {
      const newRow = {
        id: `row_${Date.now()}`,
        type: 'form-row',
        height: 60, // 默认高度
        backgroundColor: 'transparent',
        borderColor: '#e4e7ed',
        borderWidth: 0,
        borderStyle: 'solid',
        padding: 8,
        children: [] // 行内的表单元素
      }

      const currentRows = component.config?.rows || []

      components.value[containerIndex] = {
        ...component,
        config: {
          ...component.config,
          rows: [...currentRows, newRow]
        }
      }

      ElMessage.success('已添加新行')
    }
  }

  // 选择表单行
  const handleSelectFormRow = row => {
    selectedFormRow.value = row
    if (!row) {
      // 如果取消选中表单行，也取消表单子组件和表单列选中
      selectedFormChild.value = null
      selectedFormColumn.value = null
    } else {
      // 选中表单行时，取消表单列选中
      selectedFormColumn.value = null
    }
  }

  // 更新表单行
  const handleUpdateFormRow = updatedRow => {
    const container = components.value.find(c =>
      c.config?.rows?.some(row => row.id === updatedRow.id)
    )

    if (container) {
      const containerIndex = components.value.findIndex(
        c => c.id === container.id
      )
      const rowIndex = container.config.rows.findIndex(
        row => row.id === updatedRow.id
      )

      if (containerIndex !== -1 && rowIndex !== -1) {
        const updatedRows = [...container.config.rows]
        updatedRows[rowIndex] = updatedRow

        components.value[containerIndex] = {
          ...container,
          config: {
            ...container.config,
            rows: updatedRows
          }
        }

        // 同步更新selectedFormRow
        selectedFormRow.value = updatedRow
      }
    }
  }

  // 删除表单行
  const handleDeleteFormRow = rowIndex => {
    const container = components.value.find(
      c => c.config?.rows && c.config.rows.length > rowIndex
    )

    if (container) {
      const containerIndex = components.value.findIndex(
        c => c.id === container.id
      )

      if (containerIndex !== -1) {
        const updatedRows = [...container.config.rows]
        updatedRows.splice(rowIndex, 1)

        components.value[containerIndex] = {
          ...container,
          config: {
            ...container.config,
            rows: updatedRows
          }
        }

        // 清除选中状态
        selectedFormRow.value = null
        ElMessage.success('已删除行')
      }
    }
  }

  // 重排序表单行
  const handleReorderFormRows = (fromIndex, direction) => {
    // 通过选中的组件（表单容器）来处理行排序
    const selectedContainer = selectedComponent.value

    if (
      !selectedContainer ||
      selectedContainer.type !== 'form-container' ||
      !selectedContainer.config?.rows ||
      fromIndex >= selectedContainer.config.rows.length
    ) {
      ElMessage.warning('未找到有效的表单容器或行')
      return
    }

    const containerIndex = components.value.findIndex(
      c => c.id === selectedContainer.id
    )

    const toIndex = direction === 'up' ? fromIndex - 1 : fromIndex + 1

    if (
      containerIndex !== -1 &&
      toIndex >= 0 &&
      toIndex < selectedContainer.config.rows.length
    ) {
      const updatedRows = [...selectedContainer.config.rows]
      const [movedRow] = updatedRows.splice(fromIndex, 1)
      updatedRows.splice(toIndex, 0, movedRow)

      components.value[containerIndex] = {
        ...selectedContainer,
        config: {
          ...selectedContainer.config,
          rows: updatedRows
        }
      }

      ElMessage.success(`已${direction === 'up' ? '上移' : '下移'}行`)
    } else {
      ElMessage.warning(
        `无法${direction === 'up' ? '上移' : '下移'}：已在${direction === 'up' ? '顶部' : '底部'}`
      )
    }
  }

  // 拆分表单列
  const handleSplitFormColumn = ({ component, columns }) => {
    // component是表单行对象
    const container = components.value.find(c =>
      c.config?.rows?.some(row => row.id === component.id)
    )

    if (container) {
      const containerIndex = components.value.findIndex(
        c => c.id === container.id
      )
      const rowIndex = container.config.rows.findIndex(
        row => row.id === component.id
      )

      if (containerIndex !== -1 && rowIndex !== -1) {
        const updatedRows = [...container.config.rows]
        const targetRow = { ...updatedRows[rowIndex] }

        // 创建列结构
        const newColumns = []
        for (let i = 0; i < columns; i++) {
          newColumns.push({
            id: `col_${Date.now()}_${i}`,
            type: 'form-column',
            width: Math.floor(100 / columns), // 均等分配宽度
            children: [] // 列内的表单元素
          })
        }

        // 更新行的列结构
        targetRow.columns = newColumns
        updatedRows[rowIndex] = targetRow

        components.value[containerIndex] = {
          ...container,
          config: {
            ...container.config,
            rows: updatedRows
          }
        }

        ElMessage.success(`已将行拆分为${columns}列`)
      }
    }
  }

  // 处理拖拽到表单列
  const handleDropToColumn = ({ column, row, formContainer }) => {
    if (!draggedComponent.value) return

    // 检查是否为表单组件
    const formComponentTypes = ['input', 'select', 'datepicker', 'button']

    if (!formComponentTypes.includes(draggedComponent.value.type)) {
      ElMessage.warning('只能将表单元素拖拽到表单列中')
      return
    }

    // 创建表单子组件
    const childComponent = {
      id: `comp_${Date.now()}`,
      type: draggedComponent.value.type,
      config: {
        ...cloneDeep(draggedComponent.value.defaultConfig),
        // 继承表单容器的尺寸设置
        size: formContainer.config?.size || 'default'
      }
    }

    // 找到表单容器在components中的索引
    const containerIndex = components.value.findIndex(
      c => c.id === formContainer.id
    )

    if (containerIndex !== -1) {
      // 找到行在rows中的索引
      const rowIndex = components.value[containerIndex].config.rows.findIndex(
        r => r.id === row.id
      )

      if (rowIndex !== -1) {
        // 找到列在columns中的索引
        const colIndex = components.value[containerIndex].config.rows[
          rowIndex
        ].columns.findIndex(c => c.id === column.id)

        if (colIndex !== -1) {
          // 深拷贝更新数据
          const updatedContainer = { ...components.value[containerIndex] }
          const updatedRows = [...updatedContainer.config.rows]
          const updatedRow = { ...updatedRows[rowIndex] }
          const updatedColumns = [...updatedRow.columns]
          const updatedColumn = { ...updatedColumns[colIndex] }

          // 将新组件添加到列的children中
          updatedColumn.children = [
            ...(updatedColumn.children || []),
            childComponent
          ]

          // 更新数据结构
          updatedColumns[colIndex] = updatedColumn
          updatedRow.columns = updatedColumns
          updatedRows[rowIndex] = updatedRow
          updatedContainer.config = {
            ...updatedContainer.config,
            rows: updatedRows
          }

          // 更新components数组
          components.value[containerIndex] = updatedContainer

          ElMessage.success(`已将${draggedComponent.value.name}添加到表单列`)
        }
      }
    }

    // 清除拖拽状态
    draggedComponent.value = null
  }

  // 选择表单列
  const handleSelectFormColumn = ({ column, row, formContainer }) => {
    selectedFormColumn.value = {
      column: column,
      row: row,
      formContainer: formContainer
    }

    // 选中表单列时，取消其他选中状态
    selectedFormChild.value = null
    selectedFormRow.value = null

    // 同时选中父表单容器
    if (formContainer) {
      editorStore.setSelectedComponent(formContainer)
    }

    ElMessage.success('已选中表单列')
  }

  // 处理表单组件在列间拖拽移动
  const handleMoveFormChildBetweenColumns = ({
    sourceColumn,
    sourceRow,
    targetColumn,
    targetRow,
    formChild,
    formContainer
  }) => {
    // 找到表单容器在components中的索引
    const containerIndex = components.value.findIndex(
      c => c.id === formContainer.id
    )

    if (containerIndex === -1) return

    // 深拷贝容器数据
    const updatedContainer = { ...components.value[containerIndex] }
    const updatedRows = [...updatedContainer.config.rows]

    // 找到源行和目标行的索引
    const sourceRowIndex = updatedRows.findIndex(r => r.id === sourceRow.id)
    const targetRowIndex = updatedRows.findIndex(r => r.id === targetRow.id)

    if (sourceRowIndex === -1 || targetRowIndex === -1) return

    // 找到源列和目标列的索引
    const sourceColIndex = updatedRows[sourceRowIndex].columns.findIndex(
      c => c.id === sourceColumn.id
    )
    const targetColIndex = updatedRows[targetRowIndex].columns.findIndex(
      c => c.id === targetColumn.id
    )

    if (sourceColIndex === -1 || targetColIndex === -1) return

    // 获取源列和目标列
    const updatedSourceRow = { ...updatedRows[sourceRowIndex] }
    const updatedTargetRow =
      sourceRowIndex === targetRowIndex
        ? updatedSourceRow
        : { ...updatedRows[targetRowIndex] }

    const updatedSourceColumns = [...updatedSourceRow.columns]
    const updatedTargetColumns =
      sourceRowIndex === targetRowIndex
        ? updatedSourceColumns
        : [...updatedTargetRow.columns]

    const updatedSourceColumn = { ...updatedSourceColumns[sourceColIndex] }
    const updatedTargetColumn = { ...updatedTargetColumns[targetColIndex] }

    // 从源列中移除组件
    const sourceChildren = [...(updatedSourceColumn.children || [])]
    const childIndex = sourceChildren.findIndex(
      child => child.id === formChild.id
    )

    if (childIndex === -1) return

    const [movedChild] = sourceChildren.splice(childIndex, 1)
    updatedSourceColumn.children = sourceChildren

    // 如果目标列和源列相同，直接返回（无需移动）
    if (sourceColumn.id === targetColumn.id) {
      ElMessage.info('无需移动到相同位置')
      return
    }

    // 处理目标列
    const targetChildren = [...(updatedTargetColumn.children || [])]

    if (targetChildren.length > 0) {
      // 目标列有组件，进行位置互换
      const [swappedChild] = targetChildren.splice(0, 1) // 取出目标列的第一个组件
      targetChildren.unshift(movedChild) // 将移动的组件放到目标列首位
      sourceChildren.push(swappedChild) // 将原目标列的组件放到源列

      updatedSourceColumn.children = sourceChildren
      updatedTargetColumn.children = targetChildren

      ElMessage.success('组件位置已互换')
    } else {
      // 目标列为空，直接移动
      updatedTargetColumn.children = [movedChild]
      ElMessage.success('组件已移动到目标列')
    }

    // 更新列数据
    updatedSourceColumns[sourceColIndex] = updatedSourceColumn
    updatedTargetColumns[targetColIndex] = updatedTargetColumn

    // 更新行数据
    updatedSourceRow.columns = updatedSourceColumns
    if (sourceRowIndex !== targetRowIndex) {
      updatedTargetRow.columns = updatedTargetColumns
    }

    // 更新行数组
    updatedRows[sourceRowIndex] = updatedSourceRow
    if (sourceRowIndex !== targetRowIndex) {
      updatedRows[targetRowIndex] = updatedTargetRow
    }

    // 更新容器数据
    updatedContainer.config = {
      ...updatedContainer.config,
      rows: updatedRows
    }

    // 更新components数组
    components.value[containerIndex] = updatedContainer
  }

  // 处理表单列属性更新
  const handleUpdateFormColumnProperty = (property, value) => {
    if (!selectedFormColumn.value) return

    const { column, row, formContainer } = selectedFormColumn.value
    const containerIndex = components.value.findIndex(
      c => c.id === formContainer.id
    )

    if (containerIndex !== -1) {
      const rowIndex = components.value[containerIndex].config.rows.findIndex(
        r => r.id === row.id
      )

      if (rowIndex !== -1) {
        const colIndex = components.value[containerIndex].config.rows[
          rowIndex
        ].columns.findIndex(c => c.id === column.id)

        if (colIndex !== -1) {
          const updatedContainer = { ...components.value[containerIndex] }
          const updatedRows = [...updatedContainer.config.rows]
          const updatedRow = { ...updatedRows[rowIndex] }
          const updatedColumns = [...updatedRow.columns]
          const updatedColumn = { ...updatedColumns[colIndex] }

          // 处理嵌套属性
          if (property.includes('.')) {
            const keys = property.split('.')
            let target = updatedColumn
            for (let i = 0; i < keys.length - 1; i++) {
              if (!target[keys[i]] || typeof target[keys[i]] !== 'object') {
                target[keys[i]] = {}
              }
              target = target[keys[i]]
            }
            target[keys[keys.length - 1]] = value
          } else {
            updatedColumn[property] = value
          }

          updatedColumns[colIndex] = updatedColumn
          updatedRow.columns = updatedColumns
          updatedRows[rowIndex] = updatedRow
          updatedContainer.config.rows = updatedRows

          components.value[containerIndex] = updatedContainer

          // 更新选中的表单列对象
          selectedFormColumn.value.column = updatedColumn

          ElMessage.success('表单列属性已更新')
        }
      }
    }
  }

  // 处理表单行属性更新
  const handleUpdateFormRowProperty = (property, value) => {
    if (!selectedFormRow.value) return

    // 找到包含该行的表单容器
    const container = components.value.find(c =>
      c.config?.rows?.some(row => row.id === selectedFormRow.value.id)
    )

    if (!container) return

    const containerIndex = components.value.findIndex(
      c => c.id === container.id
    )
    const rowIndex = container.config.rows.findIndex(
      row => row.id === selectedFormRow.value.id
    )

    if (containerIndex !== -1 && rowIndex !== -1) {
      const updatedContainer = { ...components.value[containerIndex] }
      const updatedRows = [...updatedContainer.config.rows]
      const updatedRow = { ...updatedRows[rowIndex] }

      // 处理嵌套属性
      if (property.includes('.')) {
        const keys = property.split('.')
        let target = updatedRow
        for (let i = 0; i < keys.length - 1; i++) {
          if (!target[keys[i]] || typeof target[keys[i]] !== 'object') {
            target[keys[i]] = {}
          }
          target = target[keys[i]]
        }
        target[keys[keys.length - 1]] = value
      } else {
        updatedRow[property] = value
      }

      updatedRows[rowIndex] = updatedRow
      updatedContainer.config.rows = updatedRows
      components.value[containerIndex] = updatedContainer

      // 同步更新selectedFormRow
      selectedFormRow.value = updatedRow

      ElMessage.success('表单行属性已更新')
    }
  }

  // 处理表单列内子组件删除
  const handleDeleteColumnFormChild = ({
    column,
    row,
    childIndex,
    formContainer
  }) => {
    const containerIndex = components.value.findIndex(
      c => c.id === formContainer.id
    )

    if (containerIndex !== -1) {
      const rowIndex = components.value[containerIndex].config.rows.findIndex(
        r => r.id === row.id
      )

      if (rowIndex !== -1) {
        const colIndex = components.value[containerIndex].config.rows[
          rowIndex
        ].columns.findIndex(c => c.id === column.id)

        if (colIndex !== -1) {
          // 深拷贝更新数据
          const updatedContainer = { ...components.value[containerIndex] }
          const updatedRows = [...updatedContainer.config.rows]
          const updatedRow = { ...updatedRows[rowIndex] }
          const updatedColumns = [...updatedRow.columns]
          const updatedColumn = { ...updatedColumns[colIndex] }

          // 删除指定索引的子组件
          const updatedChildren = [...updatedColumn.children]
          updatedChildren.splice(childIndex, 1)

          updatedColumn.children = updatedChildren
          updatedColumns[colIndex] = updatedColumn
          updatedRow.columns = updatedColumns
          updatedRows[rowIndex] = updatedRow
          updatedContainer.config = {
            ...updatedContainer.config,
            rows: updatedRows
          }

          components.value[containerIndex] = updatedContainer

          // 清除选中状态
          selectedFormChild.value = null

          ElMessage.success('表单元素已删除')
        }
      }
    }
  }

  // 删除表单列
  const handleDeleteFormColumn = async ({ column, row, formContainer }) => {
    try {
      await ElMessageBox.confirm(
        '删除列将会同时删除列内的所有表单组件，此操作不可撤销。',
        '确认删除列',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      const containerIndex = components.value.findIndex(
        c => c.id === formContainer.id
      )

      if (containerIndex !== -1) {
        const rowIndex = components.value[containerIndex].config.rows.findIndex(
          r => r.id === row.id
        )

        if (rowIndex !== -1) {
          const colIndex = components.value[containerIndex].config.rows[
            rowIndex
          ].columns.findIndex(c => c.id === column.id)

          if (colIndex !== -1) {
            // 深拷贝更新数据
            const updatedContainer = { ...components.value[containerIndex] }
            const updatedRows = [...updatedContainer.config.rows]
            const updatedRow = { ...updatedRows[rowIndex] }
            const updatedColumns = [...updatedRow.columns]

            // 删除指定列
            updatedColumns.splice(colIndex, 1)

            // 如果删除后没有列了，清空列结构
            if (updatedColumns.length === 0) {
              delete updatedRow.columns
              updatedRow.children = [] // 重置为行的直接子组件
            } else {
              updatedRow.columns = updatedColumns
            }

            updatedRows[rowIndex] = updatedRow
            updatedContainer.config = {
              ...updatedContainer.config,
              rows: updatedRows
            }

            components.value[containerIndex] = updatedContainer

            // 清除选中状态
            selectedFormColumn.value = null

            const childrenCount = column.children?.length || 0
            const childrenText =
              childrenCount > 0 ? `及其${childrenCount}个表单组件` : ''
            ElMessage.success(`已删除列${childrenText}`)
          }
        }
      }
    } catch {
      // 用户取消删除
    }
  }

  // 处理拆分组件
  const handleSplitComponent = ({
    parentComponent,
    childComponents,
    transferredSlotChildId
  }) => {
    // 更新父组件
    const index = components.value.findIndex(c => c.id === parentComponent.id)
    if (index !== -1) {
      components.value[index] = parentComponent
    }

    // 添加子组件
    childComponents.forEach(child => {
      components.value.push(child)
    })

    // 如果有转移的插槽子组件，确保其引用正确
    if (transferredSlotChildId) {
      const slotChildIndex = components.value.findIndex(
        c => c.id === transferredSlotChildId
      )
      if (slotChildIndex !== -1) {
        // 确保插槽子组件知道它现在属于第一个新的子容器
        components.value[slotChildIndex] = {
          ...components.value[slotChildIndex]
          // 插槽子组件的其他属性保持不变，它依然是插槽子组件
        }
      }
    }

    // 选中第一个子组件
    if (childComponents.length > 0) {
      editorStore.setSelectedComponent(childComponents[0])
    }

    const direction =
      parentComponent.config.direction === 'horizontal' ? '水平' : '垂直'
    const message = transferredSlotChildId
      ? `DIV容器${direction}拆分成功，原有组件已转移到第一个子容器`
      : `DIV容器${direction}拆分成功`
    ElMessage.success(message)
  }

  // 处理属性更新
  const handleUpdateProperty = (property, value) => {
    if (selectedComponent.value) {
      if (property.includes('.')) {
        const keys = property.split('.')
        let target = selectedComponent.value
        for (let i = 0; i < keys.length - 1; i++) {
          // 如果对象不存在，则创建一个空对象
          if (!target[keys[i]] || typeof target[keys[i]] !== 'object') {
            target[keys[i]] = {}
          }
          target = target[keys[i]]
        }
        target[keys[keys.length - 1]] = value
      } else {
        selectedComponent.value[property] = value
      }

      // 特殊处理：当 parentPadding 更新时，重新计算子容器布局
      if (
        property === 'config.parentPadding' &&
        selectedComponent.value.config?.isContainer
      ) {
        recalculateChildContainers(selectedComponent.value, value)
      }

      // 特殊处理：当容器组的标题栏显示状态变化时，重新计算子容器布局
      if (
        (property === 'config.showTitleBar' ||
          property === 'config.titleHeight') &&
        selectedComponent.value.config?.isContainer
      ) {
        recalculateChildContainersLayout(selectedComponent.value)
      }

      // 特殊处理：当容器组的铺满画布状态变化时，重新计算子容器布局
      if (
        property === 'fullCanvas' &&
        selectedComponent.value.config?.isContainer
      ) {
        recalculateChildContainersLayout(selectedComponent.value)
      }

      // 特殊处理：当表单容器的size更新时，同步更新所有子组件的size
      if (
        property === 'config.size' &&
        selectedComponent.value.type === 'form-container'
      ) {
        syncFormChildrenSize(selectedComponent.value, value)
      }
    }
  }

  // 处理表单子组件属性更新
  const handleUpdateFormChildProperty = (property, value) => {
    if (!selectedFormChild.value || !selectedComponent.value) return

    const containerIndex = components.value.findIndex(
      c => c.id === selectedComponent.value.id
    )
    if (containerIndex === -1) return

    const container = components.value[containerIndex]
    let updated = false

    // 创建深拷贝的容器配置
    const updatedContainer = { ...container }
    updatedContainer.config = { ...container.config }

    // 函数：更新子组件属性
    const updateChildProperty = (child, property, value) => {
      const updatedChild = { ...child }

      if (property.includes('.')) {
        const keys = property.split('.')
        let target = updatedChild
        for (let i = 0; i < keys.length - 1; i++) {
          if (!target[keys[i]] || typeof target[keys[i]] !== 'object') {
            target[keys[i]] = {}
          }
          target = target[keys[i]]
        }
        target[keys[keys.length - 1]] = value
      } else {
        updatedChild[property] = value
      }

      return updatedChild
    }

    // 1. 检查主表单容器的children数组（旧格式，保持向后兼容）
    if (container.config?.children) {
      const childIndex = container.config.children.findIndex(
        child => child.id === selectedFormChild.value.id
      )

      if (childIndex !== -1) {
        const newChildren = [...container.config.children]
        const updatedChild = updateChildProperty(
          newChildren[childIndex],
          property,
          value
        )
        newChildren[childIndex] = updatedChild

        updatedContainer.config.children = newChildren
        selectedFormChild.value = updatedChild
        updated = true
      }
    }

    // 2. 检查表单行和表单列中的子组件
    if (!updated && container.config?.rows) {
      const newRows = container.config.rows.map(row => {
        const newRow = { ...row }

        // 检查行直接的children
        if (row.children) {
          const childIndex = row.children.findIndex(
            child => child.id === selectedFormChild.value.id
          )

          if (childIndex !== -1) {
            const newChildren = [...row.children]
            const updatedChild = updateChildProperty(
              newChildren[childIndex],
              property,
              value
            )
            newChildren[childIndex] = updatedChild

            newRow.children = newChildren
            selectedFormChild.value = updatedChild
            updated = true
            return newRow
          }
        }

        // 检查列中的children
        if (row.columns) {
          const newColumns = row.columns.map(column => {
            const newColumn = { ...column }

            if (column.children) {
              const childIndex = column.children.findIndex(
                child => child.id === selectedFormChild.value.id
              )

              if (childIndex !== -1) {
                const newChildren = [...column.children]
                const updatedChild = updateChildProperty(
                  newChildren[childIndex],
                  property,
                  value
                )
                newChildren[childIndex] = updatedChild

                newColumn.children = newChildren
                selectedFormChild.value = updatedChild
                updated = true
                return newColumn
              }
            }

            return newColumn
          })

          newRow.columns = newColumns
        }

        return newRow
      })

      updatedContainer.config.rows = newRows
    }

    // 如果找到并更新了子组件，则更新整个容器
    if (updated) {
      components.value[containerIndex] = updatedContainer
    }
  }

  // 重新计算子容器布局
  const recalculateChildContainers = (parentComponent, newParentPadding) => {
    // 找到所有子容器
    const childContainers = components.value.filter(
      c =>
        c.config?.isChildContainer && c.config?.parentId === parentComponent.id
    )

    if (childContainers.length !== 2) return // 只处理已拆分的容器

    const childGap = 8 // 子容器间的间隙
    const direction = parentComponent.config?.direction
    // 考虑父容器标题栏高度
    const titleBarHeight = parentComponent.config?.showTitleBar
      ? parentComponent.config?.titleHeight || 32
      : 0

    // 获取实际的父容器尺寸和位置 - 如果是铺满画布，使用画布尺寸和0位置
    const parentActualWidth = parentComponent.fullCanvas
      ? canvasSize.width
      : parentComponent.size.width
    const parentActualHeight = parentComponent.fullCanvas
      ? canvasSize.height
      : parentComponent.size.height
    const parentActualX = parentComponent.fullCanvas
      ? 0
      : parentComponent.position.x
    const parentActualY = parentComponent.fullCanvas
      ? 0
      : parentComponent.position.y

    childContainers.forEach(child => {
      const childIndex = components.value.findIndex(c => c.id === child.id)
      if (childIndex === -1) return

      if (direction === 'horizontal') {
        // 左右布局：重新计算子容器的位置和尺寸
        const availableWidth =
          parentActualWidth - 2 * newParentPadding - childGap
        const childWidth = Math.floor(availableWidth / 2)
        const availableHeight =
          parentActualHeight - 2 * newParentPadding - titleBarHeight

        if (child.config.childIndex === 0) {
          // 左侧容器
          components.value[childIndex] = {
            ...child,
            position: {
              x: parentActualX + newParentPadding,
              y: parentActualY + newParentPadding + titleBarHeight
            },
            size: {
              width: childWidth,
              height: availableHeight
            }
          }
        } else {
          // 右侧容器
          components.value[childIndex] = {
            ...child,
            position: {
              x: parentActualX + newParentPadding + childWidth + childGap,
              y: parentActualY + newParentPadding + titleBarHeight
            },
            size: {
              width: childWidth,
              height: availableHeight
            }
          }
        }
      } else {
        // 上下布局：重新计算子容器的位置和尺寸
        const availableWidth = parentActualWidth - 2 * newParentPadding
        const availableHeight =
          parentActualHeight - 2 * newParentPadding - titleBarHeight - childGap
        const childHeight = Math.floor(availableHeight / 2)

        if (child.config.childIndex === 0) {
          // 上部容器
          components.value[childIndex] = {
            ...child,
            position: {
              x: parentActualX + newParentPadding,
              y: parentActualY + newParentPadding + titleBarHeight
            },
            size: {
              width: availableWidth,
              height: childHeight
            }
          }
        } else {
          // 下部容器
          components.value[childIndex] = {
            ...child,
            position: {
              x: parentActualX + newParentPadding,
              y:
                parentActualY +
                newParentPadding +
                titleBarHeight +
                childHeight +
                childGap
            },
            size: {
              width: availableWidth,
              height: childHeight
            }
          }
        }
      }
    })
  }

  // 同步表单容器子组件的size
  const syncFormChildrenSize = (formContainer, newSize) => {
    const containerIndex = components.value.findIndex(
      c => c.id === formContainer.id
    )
    if (containerIndex !== -1 && formContainer.config?.children) {
      const updatedChildren = formContainer.config.children.map(child => ({
        ...child,
        config: {
          ...child.config,
          size: newSize
        }
      }))

      components.value[containerIndex] = {
        ...formContainer,
        config: {
          ...formContainer.config,
          children: updatedChildren
        }
      }

      // 如果当前选中的是表单子组件，也需要同步更新
      if (selectedFormChild.value) {
        const updatedChild = updatedChildren.find(
          child => child.id === selectedFormChild.value.id
        )
        if (updatedChild) {
          selectedFormChild.value = updatedChild
        }
      }
    }
  }

  // 处理预览
  const handlePreview = () => {
    // 导入schema转换工具
    import('../utils/schemaTransform.js').then(
      ({ generateHierarchicalSchema, cleanComponentConfig }) => {
        // 生成层级化的schema
        const hierarchicalSchema = generateHierarchicalSchema(
          canvasSize,
          components.value
        )

        // 清理配置并创建最终的预览schema
        const previewSchema = {
          ...hierarchicalSchema,
          components: hierarchicalSchema.components.map(cleanComponentConfig),
          // 添加全局方法等字段
          methods: editorStore.schema.methods || '',
          variables: editorStore.schema.variables || {},
          mounted: editorStore.schema.mounted || '',
          styles: editorStore.schema.styles || ''
        }

        localStorage.setItem('bi-schema-preview', JSON.stringify(previewSchema))
        window.open('/preview', '_blank')
      }
    )
  }

  // 处理保存
  const handleSave = () => {
    // 导入schema转换工具
    import('../utils/schemaTransform.js').then(
      ({ generateHierarchicalSchema, cleanComponentConfig }) => {
        // 生成层级化的schema
        const hierarchicalSchema = generateHierarchicalSchema(
          canvasSize,
          components.value
        )

        // 清理配置并创建最终的schema
        const finalSchema = {
          ...hierarchicalSchema,
          components: hierarchicalSchema.components.map(cleanComponentConfig),
          // 添加全局方法等字段
          methods: editorStore.schema.methods || '',
          variables: editorStore.schema.variables || {},
          mounted: editorStore.schema.mounted || '',
          styles: editorStore.schema.styles || ''
        }

        // 保存层级化的schema
        localStorage.setItem('bi-schema', JSON.stringify(finalSchema))

        ElMessage.success('保存成功')
      }
    )
  }

  const handleUpdateCanvasSize = size => {
    canvasSize.width = size.width
    canvasSize.height = size.height
    canvasSize.isResponsive = size.isResponsive || false
  }

  // 调整位置模式相关状态
  const adjustPositionMode = ref(false)
  const adjustingComponent = ref(null)

  // 处理开始调整位置
  const handleStartAdjustPosition = ({ component, column, row }) => {
    adjustPositionMode.value = true
    adjustingComponent.value = { component, column, row }
    ElMessage.info('请点击目标列来调整组件位置，或按Esc键取消')
  }

  // 处理列点击用于调整位置
  const handleColumnClickForAdjust = ({
    sourceComponent,
    sourceColumn,
    sourceRow,
    targetColumn,
    targetRow
  }) => {
    if (!adjustPositionMode.value || !adjustingComponent.value) {
      return
    }

    // 检查是否点击的是同一列
    if (sourceColumn.id === targetColumn.id && sourceRow.id === targetRow.id) {
      ElMessage.info('已取消位置调整')
      adjustPositionMode.value = false
      adjustingComponent.value = null
      return
    }

    // 获取当前选中的表单容器
    const formContainer = selectedComponent.value
    if (!formContainer || formContainer.type !== 'form-container') {
      ElMessage.error('未找到表单容器')
      adjustPositionMode.value = false
      adjustingComponent.value = null
      return
    }

    // 检查目标列是否有组件
    const hasTargetComponent =
      targetColumn.children &&
      Array.isArray(targetColumn.children) &&
      targetColumn.children.length > 0

    if (hasTargetComponent) {
      // 目标列有组件，询问用户是否互换
      const targetComponentName =
        targetColumn.children[0].config?.label || '未命名组件'
      const sourceComponentName = sourceComponent.config?.label || '未命名组件'

      ElMessageBox.confirm(
        `目标列已有组件"${targetComponentName}"，是否与"${sourceComponentName}"互换位置？`,
        '位置调整确认',
        {
          confirmButtonText: '互换位置',
          cancelButtonText: '仅移动',
          distinguishCancelAndClose: true,
          type: 'question'
        }
      )
        .then(() => {
          // 用户选择互换位置
          performPositionAdjustment({
            sourceComponent,
            sourceColumn,
            sourceRow,
            targetColumn,
            targetRow,
            formContainer,
            action: 'swap'
          })
        })
        .catch(action => {
          if (action === 'cancel') {
            // 用户选择仅移动（将目标列组件移到源列）
            performPositionAdjustment({
              sourceComponent,
              sourceColumn,
              sourceRow,
              targetColumn,
              targetRow,
              formContainer,
              action: 'move'
            })
          }
          // close时什么都不做
        })
    } else {
      // 目标列为空，直接移动
      const sourceComponentName = sourceComponent.config?.label || '未命名组件'
      ElMessageBox.confirm(
        `是否将"${sourceComponentName}"移动到目标列？`,
        '位置调整确认',
        {
          confirmButtonText: '确认移动',
          cancelButtonText: '取消',
          type: 'info'
        }
      )
        .then(() => {
          performPositionAdjustment({
            sourceComponent,
            sourceColumn,
            sourceRow,
            targetColumn,
            targetRow,
            formContainer,
            action: 'move'
          })
        })
        .catch(() => {
          // 用户取消
        })
    }

    // 重置调整模式
    adjustPositionMode.value = false
    adjustingComponent.value = null
  }

  // 执行位置调整
  const performPositionAdjustment = ({
    sourceComponent,
    sourceColumn,
    sourceRow,
    targetColumn,
    targetRow,
    formContainer,
    action
  }) => {
    try {
      // 在components中找到对应的表单容器
      const containerIndex = components.value.findIndex(
        comp => comp.id === formContainer.id
      )
      if (containerIndex === -1) {
        ElMessage.error('表单容器未找到')
        return
      }

      const container = components.value[containerIndex]

      // 获取源行和目标行的索引
      const sourceRowIndex = container.config?.rows.findIndex(
        row => row.id === sourceRow.id
      )
      const targetRowIndex = container.config?.rows.findIndex(
        row => row.id === targetRow.id
      )

      if (sourceRowIndex === -1 || targetRowIndex === -1) {
        ElMessage.error('源行或目标行未找到')
        return
      }

      // 获取源列和目标列的索引
      const actualSourceRow = container.config.rows[sourceRowIndex]
      const actualTargetRow = container.config.rows[targetRowIndex]

      const sourceColumnIndex = actualSourceRow.columns.findIndex(
        col => col.id === sourceColumn.id
      )
      const targetColumnIndex = actualTargetRow.columns.findIndex(
        col => col.id === targetColumn.id
      )

      if (sourceColumnIndex === -1 || targetColumnIndex === -1) {
        ElMessage.error('源列或目标列未找到')
        return
      }

      // 深度克隆避免引用问题
      const newContainer = JSON.parse(JSON.stringify(container))
      const newSourceColumn =
        newContainer.config.rows[sourceRowIndex].columns[sourceColumnIndex]
      const newTargetColumn =
        newContainer.config.rows[targetRowIndex].columns[targetColumnIndex]

      // 从源列中找到并移除要调整的组件
      const sourceChildIndex = newSourceColumn.children.findIndex(
        child => child.id === sourceComponent.id
      )
      if (sourceChildIndex === -1) {
        ElMessage.error('要调整的组件在源列中未找到')
        return
      }

      const childToMove = newSourceColumn.children.splice(
        sourceChildIndex,
        1
      )[0]

      if (
        action === 'swap' &&
        newTargetColumn.children &&
        newTargetColumn.children.length > 0
      ) {
        // 互换操作
        const targetChild = newTargetColumn.children[0]
        newTargetColumn.children = [childToMove]
        newSourceColumn.children.push(targetChild)
        ElMessage.success('组件位置已互换')
      } else {
        // 移动操作
        if (!newTargetColumn.children) {
          newTargetColumn.children = []
        }

        if (action === 'move' && newTargetColumn.children.length > 0) {
          // 如果目标列有组件且选择仅移动，将目标列组件移到源列
          const targetChild = newTargetColumn.children[0]
          newSourceColumn.children.push(targetChild)
          newTargetColumn.children = [childToMove]
          ElMessage.success('组件已移动，原目标列组件已移至源列')
        } else {
          // 目标列为空，直接移动
          newTargetColumn.children.push(childToMove)
          ElMessage.success('组件已移动到目标列')
        }
      }

      // 更新组件数组
      components.value[containerIndex] = newContainer
    } catch (error) {
      console.error('调整组件位置时出错:', error)
      ElMessage.error('位置调整失败，请重试')
    }
  }

  // 处理通过Delete键删除表单子组件
  const handleDeleteFormChildByDeleteKey = ({ formChild, formContainer }) => {
    try {
      // 在components中找到对应的表单容器
      const containerIndex = components.value.findIndex(
        comp => comp.id === formContainer.id
      )
      if (containerIndex === -1) {
        ElMessage.error('表单容器未找到')
        return
      }

      const container = components.value[containerIndex]
      let updated = false

      // 创建深拷贝的容器配置
      const updatedContainer = { ...container }
      updatedContainer.config = { ...container.config }

      // 1. 检查主表单容器的children数组（旧格式，保持向后兼容）
      if (container.config?.children) {
        const childIndex = container.config.children.findIndex(
          child => child.id === formChild.id
        )

        if (childIndex !== -1) {
          const newChildren = [...container.config.children]
          newChildren.splice(childIndex, 1)
          updatedContainer.config.children = newChildren
          updated = true
        }
      }

      // 2. 检查表单行和表单列中的子组件
      if (!updated && container.config?.rows) {
        const newRows = container.config.rows.map(row => {
          const newRow = { ...row }

          // 检查行直接的children
          if (row.children) {
            const childIndex = row.children.findIndex(
              child => child.id === formChild.id
            )

            if (childIndex !== -1) {
              const newChildren = [...row.children]
              newChildren.splice(childIndex, 1)
              newRow.children = newChildren
              updated = true
              return newRow
            }
          }

          // 检查列中的children
          if (row.columns) {
            const newColumns = row.columns.map(column => {
              const newColumn = { ...column }

              if (column.children) {
                const childIndex = column.children.findIndex(
                  child => child.id === formChild.id
                )

                if (childIndex !== -1) {
                  const newChildren = [...column.children]
                  newChildren.splice(childIndex, 1)
                  newColumn.children = newChildren
                  updated = true
                  return newColumn
                }
              }

              return newColumn
            })

            newRow.columns = newColumns
          }

          return newRow
        })

        updatedContainer.config.rows = newRows
      }

      // 如果找到并删除了子组件，则更新整个容器
      if (updated) {
        components.value[containerIndex] = updatedContainer
        // 清除选中状态
        selectedFormChild.value = null
        ElMessage.success('表单组件已删除')
      } else {
        ElMessage.error('未找到要删除的表单组件')
      }
    } catch (error) {
      console.error('删除表单子组件时出错:', error)
      ElMessage.error('删除失败，请重试')
    }
  }

  // 处理通过Delete键删除表单行
  const handleDeleteFormRowByDeleteKey = formRow => {
    try {
      // 找到包含该行的表单容器
      const container = components.value.find(c =>
        c.config?.rows?.some(row => row.id === formRow.id)
      )

      if (!container) {
        ElMessage.error('未找到包含该行的表单容器')
        return
      }

      const containerIndex = components.value.findIndex(
        c => c.id === container.id
      )
      const rowIndex = container.config.rows.findIndex(
        row => row.id === formRow.id
      )

      if (containerIndex !== -1 && rowIndex !== -1) {
        const updatedRows = [...container.config.rows]
        updatedRows.splice(rowIndex, 1)

        components.value[containerIndex] = {
          ...container,
          config: {
            ...container.config,
            rows: updatedRows
          }
        }

        // 清除选中状态
        selectedFormRow.value = null
        selectedFormColumn.value = null
        selectedFormChild.value = null

        ElMessage.success('表单行已删除')
      } else {
        ElMessage.error('未找到要删除的表单行')
      }
    } catch (error) {
      console.error('删除表单行时出错:', error)
      ElMessage.error('删除失败，请重试')
    }
  }

  // 页面初始化时清理旧的存储键
  import('../utils/cleanStorage.js').then(({ cleanOldStorageKeys }) => {
    cleanOldStorageKeys()
  })
</script>

<style scoped>
  .app-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f5f5f5;
  }

  .toolbar {
    height: 50px;
    background: #fff;
    border-bottom: 1px solid #e4e7ed;
    flex-shrink: 0;
  }

  .main-content {
    flex: 1;
    display: flex;
    overflow: hidden;
  }

  .sidebar-panel-container {
    width: auto;
    min-width: 60px;
    max-width: 380px;
    background: #fff;
    border-right: 1px solid #e4e7ed;
    flex-shrink: 0;
  }

  .canvas-wrapper {
    flex: 1;
    background-color: #f0f2f5;
    padding: 60px; /* 增加padding确保删除按钮等元素不被裁剪 */
    overflow: auto;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    /* 优化缩放显示 */
    min-height: 100%;
    position: relative;
  }

  .canvas-wrapper :deep(.canvas-container) {
    /* 缩放变换效果 */
    transform-origin: center top;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* 确保缩放时不会被裁剪 */
    margin: 40px; /* 增加margin为删除按钮等元素留出空间 */

    /* 添加阴影效果 */
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    overflow: visible; /* 关键修复：确保删除按钮等元素不会被裁剪 */
    position: relative;
  }

  /* 缩放状态下的样式调整 */
  .canvas-wrapper[data-scale-mode='fit'] :deep(.canvas-container) {
    margin: 60px; /* 适应屏幕模式下增加更多空间 */
  }

  .canvas-wrapper[data-scale-mode='manual'] :deep(.canvas-container) {
    margin: 80px; /* 手动模式下增加更多空间 */
  }

  /* 小缩放比例时的优化 */
  .canvas-wrapper :deep(.canvas-container[data-scale-small='true']) {
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }

  /* 大缩放比例时的优化 */
  .canvas-wrapper :deep(.canvas-container[data-scale-large='true']) {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  }

  /* 响应式调整 */
  @media (max-width: 1200px) {
    .canvas-wrapper {
      padding: 16px;
    }

    .canvas-wrapper :deep(.canvas-container) {
      margin: 16px;
    }

    .scale-status {
      bottom: 16px;
      right: 16px;
      font-size: 11px;
    }

    .scale-info {
      flex-direction: column;
      gap: 2px;
    }

    .scale-percentage {
      font-size: 12px;
    }

    .scale-mode-text {
      font-size: 10px;
    }

    .canvas-size-info {
      font-size: 10px;
    }
  }

  @media (max-width: 768px) {
    .canvas-wrapper {
      padding: 12px;
    }

    .canvas-wrapper :deep(.canvas-container) {
      margin: 12px;
    }

    .scale-status {
      bottom: 12px;
      right: 12px;
      padding: 4px 8px;
    }

    .scale-info {
      margin-bottom: 3px;
    }

    .scale-percentage {
      font-size: 11px;
    }

    .scale-mode-text {
      font-size: 9px;
    }

    .canvas-size-info {
      font-size: 9px;
    }
  }

  @media (max-width: 480px) {
    .canvas-wrapper {
      padding: 8px;
    }

    .canvas-wrapper :deep(.canvas-container) {
      margin: 8px;
    }

    .scale-status {
      bottom: 8px;
      right: 8px;
      padding: 3px 6px;
    }

    .scale-info {
      margin-bottom: 2px;
    }
  }

  /* 高分辨率屏幕优化 */
  @media (min-width: 1920px) {
    .canvas-wrapper {
      padding: 32px;
    }

    .canvas-wrapper :deep(.canvas-container) {
      margin: 32px;
    }

    .scale-status {
      bottom: 32px;
      right: 32px;
      padding: 8px 12px;
    }

    .scale-percentage {
      font-size: 16px;
    }

    .scale-mode-text {
      font-size: 14px;
    }

    .canvas-size-info {
      font-size: 14px;
    }
  }

  .right-panel {
    width: 320px;
    background: #fff;
    border-left: 1px solid #e4e7ed;
    flex-shrink: 0;
  }

  .scale-status {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.8);
    padding: 5px 10px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }

  .scale-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
  }

  .scale-percentage {
    font-size: 14px;
    font-weight: bold;
  }

  .scale-mode-text {
    font-size: 12px;
    color: #606266;
  }

  .canvas-size-info {
    font-size: 12px;
    color: #909399;
  }
</style>
