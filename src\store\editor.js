import { defineStore } from 'pinia'
import { set } from 'lodash-es'

export const useEditorStore = defineStore('editor', {
  state: () => ({
    selectedComponent: null,
    schema: {
      canvas: { width: 1920, height: 1080 },
      version: '2.0',
      variables: {},
      methods: '',
      mounted: '',
      styles: '',
      components: []
    }
  }),
  actions: {
    setSelectedComponent(component) {
      this.selectedComponent = component
    },
    updateSelectedComponentProperty({ key, value }) {
      if (this.selectedComponent) {
        set(this.selectedComponent, key, value)
      }
    },
    setFullCanvas(isFull) {
      if (!this.selectedComponent) return

      if (isFull) {
        // Backup original size
        this.updateSelectedComponentProperty({
          key: 'originalSize',
          value: { ...this.selectedComponent.size }
        })
        // Set to full canvas size
        this.updateSelectedComponentProperty({
          key: 'size',
          value: { width: '100%', height: '100%' }
        })
      } else {
        // Restore original size or a default
        const originalSize = this.selectedComponent.originalSize || {
          width: 400,
          height: 300
        }
        this.updateSelectedComponentProperty({
          key: 'size',
          value: originalSize
        })
      }
      this.updateSelectedComponentProperty({ key: 'fullCanvas', value: isFull })
    },
    // Schema相关方法
    updateSchema(newSchema) {
      this.schema = { ...this.schema, ...newSchema }
    },
    updateSchemaStyles(styles) {
      this.schema.styles = styles
    },
    updateSchemaVariables(variables) {
      this.schema.variables = variables
    },
    updateSchemaMethods(methods) {
      this.schema.methods = methods
    },
    updateSchemaMounted(mounted) {
      this.schema.mounted = mounted
    },
    updateSchemaComponents(components) {
      this.schema.components = components
    }
  }
})
