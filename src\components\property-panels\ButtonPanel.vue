<template>
  <div class="button-panel">
    <ElFormItem label="按钮文本">
      <ElInput
        :model-value="localConfig.text || '按钮'"
        @input="updateProperty('text', $event)"
        size="small"
        placeholder="请输入按钮文本"
      />
    </ElFormItem>

    <ElFormItem label="按钮类型">
      <ElSelect
        :model-value="localConfig.type || 'primary'"
        @change="updateProperty('type', $event)"
        size="small"
      >
        <ElOption label="主要按钮" value="primary" />
        <ElOption label="成功按钮" value="success" />
        <ElOption label="警告按钮" value="warning" />
        <ElOption label="危险按钮" value="danger" />
        <ElOption label="信息按钮" value="info" />
        <ElOption label="默认按钮" value="default" />
        <ElOption label="文字按钮" value="text" />
      </ElSelect>
    </ElFormItem>

    <ElFormItem label="尺寸大小">
      <ElSelect
        :model-value="localConfig.size || 'default'"
        @change="updateProperty('size', $event)"
        size="small"
      >
        <ElOption label="大" value="large" />
        <ElOption label="默认" value="default" />
        <ElOption label="小" value="small" />
      </ElSelect>
    </ElFormItem>

    <ElFormItem label="自定义颜色">
      <ElColorPicker
        :model-value="localConfig.color || ''"
        @change="updateProperty('color', $event)"
        size="small"
        show-alpha
      />
    </ElFormItem>

    <ElFormItem label="图标">
      <ElInput
        :model-value="localConfig.icon || ''"
        @input="updateProperty('icon', $event)"
        size="small"
        placeholder="图标名称"
      />
    </ElFormItem>

    <ElFormItem label="禁用">
      <ElSwitch
        :model-value="localConfig.disabled || false"
        @change="updateProperty('disabled', $event)"
      />
    </ElFormItem>

    <ElFormItem label="加载状态">
      <ElSwitch
        :model-value="localConfig.loading || false"
        @change="updateProperty('loading', $event)"
      />
    </ElFormItem>

    <ElFormItem label="朴素按钮">
      <ElSwitch
        :model-value="localConfig.plain || false"
        @change="updateProperty('plain', $event)"
      />
    </ElFormItem>

    <ElFormItem label="圆角按钮">
      <ElSwitch
        :model-value="localConfig.round || false"
        @change="updateProperty('round', $event)"
      />
    </ElFormItem>

    <ElFormItem label="圆形按钮">
      <ElSwitch
        :model-value="localConfig.circle || false"
        @change="updateProperty('circle', $event)"
      />
    </ElFormItem>

    <ElFormItem label="事件绑定">
      <ElSelect
        :model-value="localConfig.eventBinding?.type || 'none'"
        @change="updateProperty('eventBinding.type', $event)"
        size="small"
        placeholder="请选择事件类型"
      >
        <ElOption label="无事件" value="none" />
        <ElOption label="选择方法" value="method" />
        <ElOption label="自定义事件" value="custom" />
      </ElSelect>
    </ElFormItem>

    <!-- 选择方法配置 -->
    <template v-if="localConfig.eventBinding?.type === 'method'">
      <ElFormItem label="选择方法">
        <ElSelect
          :model-value="localConfig.eventBinding?.methodName || ''"
          @change="updateProperty('eventBinding.methodName', $event)"
          size="small"
          placeholder="请选择方法"
          clearable
          filterable
        >
          <ElOption
            v-for="method in availableMethods"
            :key="method.value"
            :label="method.label"
            :value="method.value"
          />
        </ElSelect>
      </ElFormItem>

      <!-- 显示选中方法的信息 -->
      <ElFormItem v-if="selectedMethodInfo" label="方法信息">
        <div class="method-info">
          <div class="method-info-item">
            <span class="info-label">方法名:</span>
            <span class="info-value">{{ selectedMethodInfo.value }}</span>
          </div>
          <div class="method-info-item">
            <span class="info-label">参数:</span>
            <span class="info-value">{{
              selectedMethodInfo.params || '无参数'
            }}</span>
          </div>
          <div class="method-info-item">
            <span class="info-label">描述:</span>
            <span class="info-value">{{
              selectedMethodInfo.description || '无描述'
            }}</span>
          </div>
        </div>
      </ElFormItem>

      <!-- 方法参数配置 -->
      <ElFormItem
        v-if="selectedMethodInfo && selectedMethodInfo.params"
        label="方法参数"
      >
        <ElInput
          :model-value="localConfig.eventBinding?.methodParams || ''"
          @input="updateProperty('eventBinding.methodParams', $event)"
          type="textarea"
          :rows="3"
          size="small"
          placeholder="请输入方法参数，多个参数用逗号分隔，支持变量引用"
        />
        <div class="param-hint">
          <p>示例：'参数1', variable1, 123</p>
          <p>支持字符串、变量引用、数字等</p>
        </div>
      </ElFormItem>
    </template>

    <!-- 自定义事件配置 -->
    <template v-if="localConfig.eventBinding?.type === 'custom'">
      <ElFormItem label="事件名称">
        <ElInput
          :model-value="localConfig.eventBinding?.eventName || ''"
          @input="updateProperty('eventBinding.eventName', $event)"
          size="small"
          placeholder="请输入自定义事件名称"
        />
      </ElFormItem>

      <ElFormItem label="事件代码">
        <div class="event-editor-container">
          <ElButton
            type="primary"
            size="small"
            @click="openEventEditor"
            :disabled="!localConfig.eventBinding?.eventName"
          >
            编辑事件代码
          </ElButton>
          <div v-if="localConfig.eventBinding?.eventCode" class="event-preview">
            <ElTag size="small" type="success">已配置事件代码</ElTag>
          </div>
        </div>
      </ElFormItem>
    </template>

    <!-- 事件编辑器对话框 -->
    <EventEditor
      v-if="localConfig.eventBinding?.type === 'custom'"
      v-model:visible="eventEditorVisible"
      :event-name="localConfig.eventBinding?.eventName || ''"
      :event-code="localConfig.eventBinding?.eventCode || ''"
      :button-config="localConfig"
      :form-variables="formVariables"
      @save="onEventSave"
    />
  </div>
</template>

<script setup>
  import { computed, ref } from 'vue'
  import {
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElSwitch,
    ElColorPicker,
    ElButton,
    ElTag
  } from 'element-plus'
  import EventEditor from '../EventEditor.vue'

  const props = defineProps({
    buttonConfig: {
      type: Object,
      required: true
    },
    formVariables: {
      type: Array,
      default: () => []
    },
    availableMethods: {
      type: Array,
      default: () => []
    }
  })

  const emit = defineEmits(['update-property'])

  const eventEditorVisible = ref(false)

  const localConfig = computed({
    get: () => props.buttonConfig,
    set: () => {}
  })

  // 获取选中方法的信息
  const selectedMethodInfo = computed(() => {
    if (!localConfig.value.eventBinding?.methodName) return null
    return props.availableMethods.find(
      method => method.value === localConfig.value.eventBinding.methodName
    )
  })

  function updateProperty(key, value) {
    console.log(`ButtonPanel更新属性: ${key} = `, value)
    emit('update-property', `config.${key}`, value)
  }

  function openEventEditor() {
    console.log('打开事件编辑器，当前配置:', localConfig.value.eventBinding)
    eventEditorVisible.value = true
  }

  function onEventSave(eventData) {
    updateProperty('eventBinding.eventCode', eventData.code)
    updateProperty('eventBinding.eventName', eventData.name)
    eventEditorVisible.value = false
  }
</script>

<style scoped>
  .button-panel {
    padding: 8px 0;
  }

  .el-select {
    width: 100%;
  }

  .event-editor-container {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .event-preview {
    flex: 1;
  }

  .method-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    background: #f5f7fa;
    padding: 12px;
    border-radius: 6px;
    font-size: 12px;
  }

  .method-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .info-label {
    color: #606266;
    font-weight: 500;
    min-width: 60px;
  }

  .info-value {
    color: #303133;
    font-family: 'Monaco', 'Courier New', monospace;
    text-align: right;
    flex: 1;
    margin-left: 8px;
  }

  .param-hint {
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
  }

  .param-hint p {
    margin: 4px 0;
    line-height: 1.4;
  }
</style>
