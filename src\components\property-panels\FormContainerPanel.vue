<template>
  <div class="form-container-panel">
    <ElFormItem label="容器标题">
      <ElInput
        :model-value="localConfig.titleText || '表单标题'"
        @input="updateProperty('titleText', $event)"
        size="small"
        placeholder="请输入容器标题"
      />
    </ElFormItem>

    <ElFormItem label="显示标题">
      <ElSwitch
        :model-value="localConfig.showTitle !== false"
        @change="updateProperty('showTitle', $event)"
      />
    </ElFormItem>

    <ElFormItem label="布局方向">
      <ElSelect
        :model-value="localConfig.layout || 'vertical'"
        @change="updateProperty('layout', $event)"
        size="small"
      >
        <ElOption label="垂直布局" value="vertical" />
        <ElOption label="水平布局" value="horizontal" />
        <ElOption label="网格布局" value="grid" />
      </ElSelect>
    </ElFormItem>

    <ElFormItem label="标签位置">
      <ElSelect
        :model-value="localConfig.labelPosition || 'left'"
        @change="updateProperty('labelPosition', $event)"
        size="small"
      >
        <ElOption label="左侧" value="left" />
        <ElOption label="右侧" value="right" />
        <ElOption label="顶部" value="top" />
      </ElSelect>
    </ElFormItem>

    <ElFormItem label="标签宽度">
      <ElInput
        :model-value="localConfig.labelWidth || '80px'"
        @input="updateProperty('labelWidth', $event)"
        size="small"
        placeholder="例: 80px"
      />
    </ElFormItem>

    <ElFormItem label="组件尺寸">
      <ElSelect
        :model-value="localConfig.size || 'default'"
        @change="updateProperty('size', $event)"
        size="small"
      >
        <ElOption label="大" value="large" />
        <ElOption label="默认" value="default" />
        <ElOption label="小" value="small" />
      </ElSelect>
    </ElFormItem>

    <ElFormItem label="组件间距">
      <ElInputNumber
        :model-value="localConfig.gap || 16"
        @change="updateProperty('gap', $event)"
        :min="0"
        :max="50"
        size="small"
        controls-position="right"
      />
    </ElFormItem>

    <ElFormItem label="内部边距">
      <ElInputNumber
        :model-value="localConfig.padding || 16"
        @change="updateProperty('padding', $event)"
        :min="0"
        :max="50"
        size="small"
        controls-position="right"
      />
    </ElFormItem>

    <ElFormItem label="背景颜色">
      <ElColorPicker
        :model-value="localConfig.backgroundColor || '#ffffff'"
        @change="updateProperty('backgroundColor', $event)"
        size="small"
        show-alpha
      />
    </ElFormItem>

    <ElFormItem label="边框颜色">
      <ElColorPicker
        :model-value="localConfig.borderColor || '#e4e7ed'"
        @change="updateProperty('borderColor', $event)"
        size="small"
        show-alpha
      />
    </ElFormItem>

    <ElFormItem label="边框宽度">
      <ElInputNumber
        :model-value="localConfig.borderWidth || 1"
        @change="updateProperty('borderWidth', $event)"
        :min="0"
        :max="10"
        size="small"
        controls-position="right"
      />
    </ElFormItem>

    <ElFormItem label="边框样式">
      <ElSelect
        :model-value="localConfig.borderStyle || 'solid'"
        @change="updateProperty('borderStyle', $event)"
        size="small"
      >
        <ElOption label="实线" value="solid" />
        <ElOption label="虚线" value="dashed" />
        <ElOption label="点线" value="dotted" />
        <ElOption label="双线" value="double" />
        <ElOption label="无边框" value="none" />
      </ElSelect>
    </ElFormItem>

    <ElFormItem label="圆角半径">
      <ElInputNumber
        :model-value="localConfig.borderRadius || 4"
        @change="updateProperty('borderRadius', $event)"
        :min="0"
        :max="20"
        size="small"
        controls-position="right"
      />
    </ElFormItem>

    <!-- 网格布局特有配置 -->
    <template v-if="localConfig.layout === 'grid'">
      <ElFormItem label="列数">
        <ElInputNumber
          :model-value="localConfig.gridColumns || 2"
          @change="updateProperty('gridColumns', $event)"
          :min="1"
          :max="6"
          size="small"
          controls-position="right"
        />
      </ElFormItem>

      <ElFormItem label="列间距">
        <ElInputNumber
          :model-value="localConfig.gridGap || 16"
          @change="updateProperty('gridGap', $event)"
          :min="0"
          :max="50"
          size="small"
          controls-position="right"
        />
      </ElFormItem>
    </template>
  </div>
</template>

<script setup>
  import { computed } from 'vue'
  import {
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElSwitch,
    ElInputNumber,
    ElColorPicker
  } from 'element-plus'

  const props = defineProps({
    formContainerConfig: {
      type: Object,
      required: true
    }
  })

  const emit = defineEmits(['update-property'])

  const localConfig = computed({
    get: () => props.formContainerConfig,
    set: () => {}
  })

  function updateProperty(key, value) {
    emit('update-property', `config.${key}`, value)
  }
</script>

<style scoped>
  .form-container-panel {
    padding: 8px 0;
  }

  .el-select {
    width: 100%;
  }
</style>
