<template>
  <div class="style-panel">
    <div class="panel-header">
      <h3>样式管理</h3>
    </div>

    <div class="panel-content">
      <div class="style-section">
        <h4>SCSS样式代码</h4>
        <div class="editor-actions">
          <ElButton type="primary" size="small" @click="openStylesEditor">
            <ElIcon><EditPen /></ElIcon>
            编辑样式
          </ElButton>
        </div>
        <div v-if="stylesCode.trim()" class="code-preview">
          <pre>{{ stylesCode }}</pre>
        </div>
        <div v-else class="empty-styles">
          <p>暂无样式代码</p>
        </div>
      </div>
    </div>

    <!-- 样式代码编辑对话框 -->
    <ElDialog
      v-model="editorDialogVisible"
      title="SCSS 样式代码编辑"
      width="90%"
      :before-close="handleEditorClose"
      class="styles-editor-dialog"
    >
      <div class="editor-toolbar">
        <div class="toolbar-left">
          <span class="editor-info"
            >编写SCSS样式代码，支持语法高亮和智能提示</span
          >
        </div>
        <div class="toolbar-right">
          <ElButton size="small" @click="formatCode"> 格式化代码 </ElButton>
        </div>
      </div>

      <div class="editor-container">
        <MonacoEditor
          ref="monacoEditor"
          v-model="stylesCode"
          language="scss"
          theme="vs-dark"
          height="500px"
          :options="editorOptions"
          @change="onCodeChange"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <div class="footer-left">
            <span class="code-info">支持SCSS语法，自动编译为CSS</span>
          </div>
          <div class="footer-right">
            <ElButton @click="cancelEditor">取消</ElButton>
            <ElButton type="primary" @click="saveStylesCode">保存</ElButton>
          </div>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<script setup>
  import { ref, watch } from 'vue'
  import {
    ElButton,
    ElIcon,
    ElDialog,
    ElMessage,
    ElMessageBox
  } from 'element-plus'
  import { EditPen } from '@element-plus/icons-vue'
  import MonacoEditor from './MonacoEditor.vue'
  import { useEditorStore } from '../store/editor'

  const editorStore = useEditorStore()

  const props = defineProps({
    styles: {
      type: String,
      default: ''
    }
  })

  const emit = defineEmits(['update-styles'])

  const stylesCode = ref(props.styles)
  const editorDialogVisible = ref(false)
  const monacoEditor = ref(null)
  const originalCode = ref('')

  const editorOptions = {
    fontSize: 14,
    tabSize: 2,
    insertSpaces: true,
    wordWrap: 'on',
    lineNumbers: 'on',
    minimap: { enabled: true },
    scrollBeyondLastLine: false,
    folding: true,
    contextmenu: true,
    suggest: {
      insertMode: 'replace',
      filterGraceful: true,
      showKeywords: true,
      showSnippets: true,
      showFunctions: true,
      showVariables: true
    }
  }

  // 监听props变化
  watch(
    () => props.styles,
    newValue => {
      stylesCode.value = newValue
      // 同步更新store中的styles
      editorStore.updateSchemaStyles(newValue)
    }
  )

  const openStylesEditor = () => {
    originalCode.value = stylesCode.value
    editorDialogVisible.value = true
  }

  const handleEditorClose = () => {
    if (stylesCode.value !== originalCode.value) {
      ElMessageBox.confirm(
        '样式代码已修改但未保存，确定要关闭吗？',
        '确认关闭',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          stylesCode.value = originalCode.value
          editorDialogVisible.value = false
        })
        .catch(() => {
          // 用户取消，不关闭对话框
        })
    } else {
      editorDialogVisible.value = false
    }
  }

  const cancelEditor = () => {
    stylesCode.value = originalCode.value
    editorDialogVisible.value = false
  }

  const saveStylesCode = () => {
    // 处理样式代码，确保它是一个有效的SCSS代码块
    let processedCode = stylesCode.value.trim()

    // 如果代码不是以<style>标签开头，添加适当的标签
    if (!processedCode.startsWith('<style')) {
      // 检查是否包含SCSS语法
      const hasScss =
        processedCode.includes('@import') ||
        processedCode.includes('@mixin') ||
        processedCode.includes('@include') ||
        processedCode.includes('@extend') ||
        processedCode.includes('$') ||
        processedCode.includes('@each') ||
        processedCode.includes('@for') ||
        processedCode.includes('@if') ||
        processedCode.includes('@else') ||
        processedCode.includes('&') ||
        /#{[^}]*}/g.test(processedCode)

      const langAttr = hasScss ? ' lang="scss"' : ''
      processedCode = `<style${langAttr} scoped>\n${processedCode}\n</style>`
    }

    emit('update-styles', processedCode)
    // 更新schema中的styles
    editorStore.updateSchemaStyles(processedCode)
    editorDialogVisible.value = false
    ElMessage.success('样式代码已保存')
  }

  const formatCode = () => {
    if (monacoEditor.value) {
      monacoEditor.value.formatDocument()
    }
  }

  const onCodeChange = value => {
    stylesCode.value = value
  }
</script>

<style scoped>
  .style-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .panel-header {
    padding: 16px;
    border-bottom: 1px solid #e4e7ed;
    background: #fff;
  }

  .panel-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }

  .panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 16px;
  }

  .style-section {
    margin-bottom: 24px;
    padding: 8px 0;
  }

  .style-section h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: #606266;
  }

  .editor-actions {
    margin-bottom: 16px;
  }

  .code-preview {
    background: #f5f5f5;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 12px;
    max-height: 300px;
    overflow-y: auto;
  }

  .code-preview pre {
    margin: 0;
    font-family: 'Monaco', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    color: #303133;
  }

  .empty-styles {
    text-align: center;
    padding: 20px;
    color: #909399;
    border: 1px dashed #e4e7ed;
    border-radius: 4px;
  }

  .editor-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e4e7ed;
    margin-bottom: 16px;
  }

  .toolbar-left .editor-info {
    color: #606266;
    font-size: 14px;
  }

  .toolbar-right {
    display: flex;
    gap: 8px;
  }

  .editor-container {
    margin-bottom: 16px;
  }

  .dialog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .footer-left .code-info {
    color: #606266;
    font-size: 14px;
  }

  .footer-right {
    display: flex;
    gap: 8px;
  }

  :deep(.styles-editor-dialog) {
    .el-dialog__body {
      padding: 20px;
    }
  }
</style>
