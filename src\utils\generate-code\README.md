# 代码生成功能说明

## 功能概述

"出码"功能可以将当前的BI布局系统中的组件配置转换为可运行的Vue代码，支持Vue2和Vue3两种版本。

## 使用方法

1. 在编辑器中布局完成后，点击工具栏中的"出码"按钮
2. 在弹出的对话框中选择要生成的Vue版本：
   - **Vue 2**: 生成基于Vue 2 + Element UI的代码
   - **Vue 3**: 生成基于Vue 3 + Element Plus的代码
3. 点击"生成代码"按钮，系统会自动下载生成的.vue文件

## 支持的组件类型

### 布局组件

- `div-container`: 容器组件，支持嵌套布局

### 图表组件

- `bar-chart`: 柱状图
- `line-chart`: 折线图
- `pie-chart`: 饼图

### 表单组件

- `button`: 按钮组件
- `input`: 输入框组件
- `select`: 选择器组件
- `date-picker`: 日期选择器组件

## 生成的代码结构

### Vue2版本

```vue
<template>
  <!-- 组件模板 -->
</template>

<script>
  import axios from 'axios'
  import dayjs from 'dayjs'
  import * as echarts from 'echarts'
  import {
    Button,
    Input,
    Select,
    Option,
    DatePicker,
    Message
  } from 'element-ui'

  export default {
    name: 'GeneratedDashboard',
    data() {
      return {
        // 响应式数据
      }
    },
    computed: {
      // 计算属性（样式绑定等）
    },
    methods: {
      // 事件处理方法
      // 数据获取方法
      // 图表初始化方法
    },
    mounted() {
      // 生命周期钩子
    }
  }
</script>

<style scoped>
  /* 组件样式 */
</style>
```

### Vue3版本

```vue
<template>
  <!-- 组件模板 -->
</template>

<script setup>
  import { ref, reactive, computed, onMounted, nextTick } from 'vue'
  import axios from 'axios'
  import dayjs from 'dayjs'
  import * as echarts from 'echarts'
  import {
    ElButton,
    ElInput,
    ElSelect,
    ElOption,
    ElDatePicker,
    ElMessage
  } from 'element-plus'

  // 响应式数据
  // 计算属性
  // 方法定义
  // 生命周期钩子
</script>

<style scoped>
  /* 组件样式 */
</style>
```

## 内置功能

### 数据获取

生成的代码包含axios封装的数据获取方法：

```javascript
// Vue2
async fetchData(url) {
  try {
    const response = await axios.get(url)
    return response.data
  } catch (error) {
    Message.error('数据获取失败: ' + error.message)
    return null
  }
}

// Vue3
const fetchData = async (url) => {
  try {
    const response = await axios.get(url)
    return response.data
  } catch (error) {
    ElMessage.error('数据获取失败: ' + error.message)
    return null
  }
}
```

### 图表初始化

自动生成ECharts图表的初始化代码，包括：

- 基础配置选项
- 响应式调整
- 示例数据

### 事件处理

为按钮等交互组件生成事件处理方法模板。

## 依赖要求

### Vue2项目依赖

```json
{
  "vue": "^2.6.0",
  "element-ui": "^2.15.0",
  "axios": "^0.24.0",
  "dayjs": "^1.10.0",
  "echarts": "^5.0.0"
}
```

### Vue3项目依赖

```json
{
  "vue": "^3.2.0",
  "element-plus": "^2.0.0",
  "axios": "^0.24.0",
  "dayjs": "^1.10.0",
  "echarts": "^5.0.0"
}
```

## 开发测试

在浏览器控制台中可以使用以下命令进行测试：

```javascript
// 测试Vue2代码生成
window.testCodeGeneration.testVue2()

// 测试Vue3代码生成
window.testCodeGeneration.testVue3()
```

## 注意事项

1. 生成的代码需要项目中安装相应的依赖包
2. 图表组件需要确保容器有足够的尺寸
3. 可以根据实际需求修改生成的代码
4. 建议在使用前先在测试环境中验证生成的代码
