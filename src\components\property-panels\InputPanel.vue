<template>
  <div class="input-panel">
    <ElFormItem label="标签文本">
      <ElInput
        :model-value="localConfig.label || '输入框'"
        @input="updateProperty('label', $event)"
        size="small"
        placeholder="请输入标签文本"
      />
    </ElFormItem>

    <ElFormItem label="占位符">
      <ElInput
        :model-value="localConfig.placeholder || '请输入内容'"
        @input="updateProperty('placeholder', $event)"
        size="small"
        placeholder="请输入占位符文本"
      />
    </ElFormItem>

    <ElFormItem label="默认值">
      <ElInput
        :model-value="localConfig.value || ''"
        @input="updateProperty('value', $event)"
        size="small"
        placeholder="请输入默认值"
      />
    </ElFormItem>

    <ElFormItem label="变量绑定">
      <ElSelect
        :model-value="localConfig.bindVariable || ''"
        @change="updateProperty('bindVariable', $event)"
        size="small"
        placeholder="选择绑定的变量"
        clearable
      >
        <ElOption label="不绑定变量" value="" />
        <ElOption
          v-for="variable in availableVariables"
          :key="variable.id"
          :label="`${variable.name} (${variable.type})`"
          :value="variable.id"
        />
      </ElSelect>
    </ElFormItem>

    <ElFormItem
      v-if="localConfig.bindVariable && selectedVariable"
      label="绑定信息"
    >
      <div class="variable-info">
        <div class="variable-info-item">
          <span class="info-label">变量名:</span>
          <span class="info-value">{{ selectedVariable.name }}</span>
        </div>
        <div class="variable-info-item">
          <span class="info-label">类型:</span>
          <span class="info-value">{{ selectedVariable.type }}</span>
        </div>
        <div class="variable-info-item">
          <span class="info-label">描述:</span>
          <span class="info-value">{{
            selectedVariable.description || '无'
          }}</span>
        </div>
      </div>
    </ElFormItem>

    <ElFormItem label="最大长度">
      <ElInputNumber
        :model-value="localConfig.maxlength"
        @change="updateProperty('maxlength', $event)"
        :min="1"
        :max="1000"
        size="small"
        controls-position="right"
        placeholder="不限制"
      />
    </ElFormItem>

    <ElFormItem label="尺寸大小">
      <ElSelect
        :model-value="localConfig.size || 'default'"
        @change="updateProperty('size', $event)"
        size="small"
      >
        <ElOption label="大" value="large" />
        <ElOption label="默认" value="default" />
        <ElOption label="小" value="small" />
      </ElSelect>
    </ElFormItem>

    <ElFormItem label="标签宽度">
      <ElInput
        :model-value="localConfig.labelWidth || '80px'"
        @input="updateProperty('labelWidth', $event)"
        size="small"
        placeholder="例: 80px"
      />
    </ElFormItem>

    <ElFormItem label="标签位置">
      <ElSelect
        :model-value="localConfig.labelPosition || 'left'"
        @change="updateProperty('labelPosition', $event)"
        size="small"
      >
        <ElOption label="左侧" value="left" />
        <ElOption label="右侧" value="right" />
        <ElOption label="顶部" value="top" />
      </ElSelect>
    </ElFormItem>

    <ElFormItem label="必填">
      <ElSwitch
        :model-value="localConfig.required || false"
        @change="updateProperty('required', $event)"
      />
    </ElFormItem>

    <ElFormItem label="禁用">
      <ElSwitch
        :model-value="localConfig.disabled || false"
        @change="updateProperty('disabled', $event)"
      />
    </ElFormItem>

    <ElFormItem label="可清空">
      <ElSwitch
        :model-value="localConfig.clearable !== false"
        @change="updateProperty('clearable', $event)"
      />
    </ElFormItem>

    <ElFormItem label="显示字数">
      <ElSwitch
        :model-value="localConfig.showWordLimit || false"
        @change="updateProperty('showWordLimit', $event)"
      />
    </ElFormItem>

    <ElFormItem label="密码框">
      <ElSwitch
        :model-value="localConfig.showPassword || false"
        @change="updateProperty('showPassword', $event)"
      />
    </ElFormItem>
  </div>
</template>

<script setup>
  import { computed } from 'vue'
  import {
    ElFormItem,
    ElInput,
    ElInputNumber,
    ElSelect,
    ElOption,
    ElSwitch
  } from 'element-plus'

  const props = defineProps({
    inputConfig: {
      type: Object,
      required: true
    },
    formVariables: {
      type: Array,
      default: () => []
    }
  })

  const emit = defineEmits(['update-property'])

  const localConfig = computed({
    get: () => props.inputConfig,
    set: () => {
      // We don't use the setter, updates are handled via events
    }
  })

  // 获取可用的变量列表（只显示适合输入框的变量类型）
  const availableVariables = computed(() => {
    const suitableTypes = ['string', 'number', 'date']
    return (props.formVariables || []).filter(variable =>
      suitableTypes.includes(variable.type)
    )
  })

  // 获取当前绑定的变量信息
  const selectedVariable = computed(() => {
    if (!localConfig.value.bindVariable) return null
    return availableVariables.value.find(
      variable => variable.id === localConfig.value.bindVariable
    )
  })

  function updateProperty(key, value) {
    emit('update-property', `config.${key}`, value)
  }
</script>

<style scoped>
  .input-panel {
    padding: 8px 0;
  }

  .el-select {
    width: 100%;
  }

  .variable-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    background: #f5f7fa;
    padding: 8px;
    border-radius: 4px;
    font-size: 12px;
  }

  .variable-info-item {
    display: flex;
    justify-content: space-between;
  }

  .info-label {
    color: #606266;
    font-weight: 500;
  }

  .info-value {
    color: #303133;
  }
</style>
