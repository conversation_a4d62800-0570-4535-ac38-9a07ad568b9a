/**
 * Vue2样式生成器
 */

// 生成Vue2的样式部分
export function generateStyle(schema) {
  const baseStyles = generateBaseStyles()
  const componentStyles = generateComponentStyles(schema.components)
  const customStyles = schema.styles || ''

  // 调试信息
  console.log('=== Vue2 样式生成器调试信息 ===')
  console.log('原始自定义样式:', customStyles)
  console.log('Schema:', schema)
  console.log('================================')

  // 处理自定义样式，确保它们被正确应用到组件
  const processedCustomStyles = customStyles
    .replace(/^<style.*?>/, '') // 移除开头的style标签
    .replace(/<\/style>$/, '') // 移除结尾的style标签
    .trim()

  console.log('处理后的自定义样式:', processedCustomStyles)

  // 合并所有样式，确保自定义样式在最后，这样可以覆盖默认样式
  const allStyles = [baseStyles, componentStyles, processedCustomStyles]
    .filter(style => style.trim())
    .join('\n\n')

  // 检查是否包含SCSS语法
  const hasScss =
    processedCustomStyles.includes('@import') ||
    processedCustomStyles.includes('@mixin') ||
    processedCustomStyles.includes('@include') ||
    processedCustomStyles.includes('@extend') ||
    processedCustomStyles.includes('$') ||
    processedCustomStyles.includes('@each') ||
    processedCustomStyles.includes('@for') ||
    processedCustomStyles.includes('@if') ||
    processedCustomStyles.includes('@else') ||
    processedCustomStyles.includes('&') ||
    /#{[^}]*}/g.test(processedCustomStyles)

  const langAttr = hasScss ? ' lang="scss"' : ''

  return `<style${langAttr} scoped>
${allStyles}
</style>`
}

// 生成基础样式
function generateBaseStyles() {
  return `.bi-dashboard {
  position: relative;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.component-container {
  box-sizing: border-box;
}

.chart-container {
  box-sizing: border-box;
}

/* 表单容器样式 */
.form-container {
  box-sizing: border-box;
  background: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.form-title {
  background: #f5f7fa;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 500;
  color: #303133;
  margin: 0;
}

.form-content {
  padding: 16px;
}

.form-row {
  margin-bottom: 16px;
}

.form-row:last-child {
  margin-bottom: 0;
}

/* Element UI 组件样式重置 */
.el-button {
  margin: 0;
}

.el-input {
  width: 100%;
}

.el-select {
  width: 100%;
}

.el-date-editor {
  width: 100%;
}

.el-form-item {
  margin-bottom: 16px;
}

.el-form-item:last-child {
  margin-bottom: 0;
}

.el-form-item__label {
  font-weight: 500;
  color: #606266;
}

.el-row {
  display: flex;
  flex-wrap: wrap;
}

.el-col {
  position: relative;
  box-sizing: border-box;
}`
}

// 生成组件特定样式
function generateComponentStyles(components) {
  if (!components || !Array.isArray(components)) {
    return ''
  }

  let styleLines = []

  components.forEach(component => {
    const { id, type, config, children } = component

    // 根据组件类型生成特定样式
    if (type === 'div-container' && config) {
      const containerStyle = generateContainerStyle(id, config)
      if (containerStyle) {
        styleLines.push(containerStyle)
      }
    }

    // 递归处理子组件
    if (children && children.length > 0) {
      const childStyles = generateComponentStyles(children)
      if (childStyles) {
        styleLines.push(childStyles)
      }
    }
  })

  return styleLines.join('\n\n')
}

// 生成容器样式
function generateContainerStyle(id, config) {
  let styles = []

  if (config.showTitleBar) {
    styles.push(`#${id} .title-bar {
  height: ${config.titleHeight || 32}px;
  background-color: ${config.titleBackgroundColor || '#f5f5f5'};
  color: ${config.titleTextColor || '#303133'};
  display: flex;
  align-items: center;
  padding: 0 12px;
  font-weight: 500;
  border-bottom: 1px solid #e4e7ed;
}`)
  }

  if (config.padding) {
    styles.push(`#${id} .container-content {
  padding: ${config.padding}px;
}`)
  }

  return styles.join('\n\n')
}
