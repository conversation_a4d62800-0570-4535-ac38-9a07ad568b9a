import { generateTemplate } from './template.js'
import { generateScript } from './script.js'
import { generateStyle } from './style.js'

/**
 * 生成Vue2完整代码
 * @param {Object} schema - 组件schema
 * @returns {string} 完整的Vue2代码
 */
export function generateVue2Code(schema) {
  // 确保schema有必要的字段
  const normalizedSchema = {
    canvas: schema.canvas || { width: 1920, height: 1080 },
    version: schema.version || '2.0',
    variables: schema.variables || {},
    methods: schema.methods || '',
    mounted: schema.mounted || '',
    styles: schema.styles || '',
    components: schema.components || []
  }

  const template = generateTemplate(normalizedSchema)
  const script = generateScript(normalizedSchema)
  const style = generateStyle(normalizedSchema)

  return `${template}

${script}

${style}`
}
