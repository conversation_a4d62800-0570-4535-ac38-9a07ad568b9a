<template>
  <ElDialog
    v-model="dialogVisible"
    title="ECharts Options 配置"
    :width="800"
    :before-close="handleClose"
    destroy-on-close
    class="options-editor-dialog"
  >
    <div class="editor-container">
      <div class="editor-header">
        <div class="tips">
          <ElIcon><InfoFilled /></ElIcon>
          <span
            >支持粘贴 ECharts 官网的 JavaScript 对象格式，将自动转换为
            JSON</span
          >
        </div>
        <div class="actions">
          <ElButton size="small" @click="formatJson">格式化</ElButton>
          <ElButton size="small" @click="convertJsToJson">转换JS对象</ElButton>
          <ElButton size="small" @click="validateJson">验证</ElButton>
          <ElButton size="small" @click="resetToDefault">重置</ElButton>
        </div>
      </div>

      <div class="editor-content">
        <vue-monaco-editor
          v-model:value="jsonString"
          language="json"
          theme="vs"
          :height="400"
          :options="editorOptions"
          @mount="handleEditorMount"
          @change="handleEditorChange"
          class="monaco-editor"
        />

        <div v-if="hasError" class="error-message">
          <ElIcon><WarningFilled /></ElIcon>
          <span>{{ errorMessage }}</span>
        </div>

        <div v-if="validationMessage" class="validation-message">
          <ElIcon><SuccessFilled /></ElIcon>
          <span>{{ validationMessage }}</span>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleClose">取消</ElButton>
        <ElButton type="primary" @click="handleSave" :disabled="hasError">
          确定
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup>
  import { ref, computed, watch } from 'vue'
  import { ElDialog, ElButton, ElIcon, ElMessage } from 'element-plus'
  import {
    InfoFilled,
    WarningFilled,
    SuccessFilled
  } from '@element-plus/icons-vue'
  import { VueMonacoEditor } from '@guolao/vue-monaco-editor'
  import { getChartTemplate } from '../utils/chartTemplates.js'

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false
    },
    options: {
      type: Object,
      default: () => ({})
    },
    chartType: {
      type: String,
      default: 'custom-chart'
    }
  })

  const emit = defineEmits(['update:modelValue', 'save'])

  // 响应式数据
  const jsonString = ref('')
  const hasError = ref(false)
  const errorMessage = ref('')
  const validationMessage = ref('')
  const editor = ref(null)

  // Monaco Editor配置
  const editorOptions = {
    selectOnLineNumbers: true,
    roundedSelection: false,
    readOnly: false,
    cursorStyle: 'line',
    automaticLayout: true,
    formatOnPaste: true,
    formatOnType: true,
    minimap: {
      enabled: false
    },
    fontSize: 14,
    lineNumbers: 'on',
    scrollBeyondLastLine: false,
    wordWrap: 'on',
    tabSize: 2,
    insertSpaces: true,
    folding: true,
    foldingStrategy: 'auto',
    showFoldingControls: 'always',
    lineDecorationsWidth: 20,
    lineNumbersMinChars: 3,
    glyphMargin: false,
    contextmenu: true,
    mouseWheelZoom: true,
    quickSuggestions: {
      other: true,
      comments: false,
      strings: true
    },
    suggest: {
      showKeywords: true,
      showSnippets: true,
      showColors: false,
      showFiles: false,
      showReferences: false,
      showFolders: false,
      showTypeParameters: false,
      showIssues: false,
      showUsers: false,
      showValues: true,
      showMethods: true,
      showFunctions: true,
      showConstructors: true,
      showFields: true,
      showVariables: true,
      showClasses: true,
      showStructs: true,
      showInterfaces: true,
      showModules: true,
      showProperties: true,
      showEvents: true,
      showOperators: true,
      showUnits: false
    }
  }

  // 计算属性
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: value => emit('update:modelValue', value)
  })

  // 编辑器挂载事件
  const handleEditorMount = editorInstance => {
    editor.value = editorInstance

    // 设置JSON Schema验证
    import('monaco-editor').then(monaco => {
      monaco.languages.json.jsonDefaults.setDiagnosticsOptions({
        validate: true,
        allowComments: false,
        schemas: [],
        enableSchemaRequest: false
      })
    })
  }

  // 编辑器内容变化事件
  const handleEditorChange = value => {
    // 防抖处理
    if (handleEditorChange.timer) {
      clearTimeout(handleEditorChange.timer)
    }

    handleEditorChange.timer = setTimeout(() => {
      validateJsonString(value)
    }, 500)
  }

  // 监听props变化
  watch(
    () => props.options,
    newOptions => {
      if (newOptions && Object.keys(newOptions).length > 0) {
        try {
          jsonString.value = JSON.stringify(newOptions, null, 2)
          hasError.value = false
          errorMessage.value = ''
        } catch {
          jsonString.value = ''
          hasError.value = true
          errorMessage.value = '无法解析当前配置'
        }
      }
    },
    { immediate: true, deep: true }
  )

  // 尝试修复JavaScript对象字面量为JSON格式
  const fixJavaScriptToJson = jsStr => {
    try {
      // 移除注释
      let cleaned = jsStr
        .replace(/\/\*[\s\S]*?\*\//g, '')
        .replace(/\/\/.*$/gm, '')

      // 处理ECharts构造函数
      // 1. LinearGradient 转换
      cleaned = cleaned.replace(
        /new\s+echarts\.graphic\.LinearGradient\s*\(\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*(\[[^\]]*\])\s*\)/g,
        (match, x, y, x2, y2, colorStops) => {
          return `{
            "type": "linear",
            "x": ${x.trim()},
            "y": ${y.trim()},
            "x2": ${x2.trim()},
            "y2": ${y2.trim()},
            "colorStops": ${colorStops}
          }`
        }
      )

      // 2. RadialGradient 转换
      cleaned = cleaned.replace(
        /new\s+echarts\.graphic\.RadialGradient\s*\(\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*(\[[^\]]*\])\s*\)/g,
        (match, x, y, r, colorStops) => {
          return `{
            "type": "radial",
            "x": ${x.trim()},
            "y": ${y.trim()},
            "r": ${r.trim()},
            "colorStops": ${colorStops}
          }`
        }
      )

      // 替换JavaScript对象字面量为JSON格式
      // 3. 给没有引号的属性名加上引号
      cleaned = cleaned.replace(
        /([{,]\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:/g,
        '$1"$2":'
      )

      // 4. 处理单引号字符串
      cleaned = cleaned.replace(/'([^']*)'/g, '"$1"')

      // 5. 处理undefined值
      cleaned = cleaned.replace(/:\s*undefined\s*([,}])/g, ': null$1')

      // 6. 处理函数（移除函数定义）
      cleaned = cleaned.replace(
        /:\s*function\s*\([^)]*\)\s*\{[^}]*\}\s*([,}])/g,
        ': null$1'
      )

      return cleaned
    } catch {
      return jsStr
    }
  }

  // 验证JSON字符串
  const validateJsonString = jsonStr => {
    if (!jsonStr || !jsonStr.trim()) {
      hasError.value = false
      errorMessage.value = ''
      validationMessage.value = ''
      return null
    }

    try {
      // 首先尝试直接解析
      const parsed = JSON.parse(jsonStr)
      hasError.value = false
      errorMessage.value = ''
      validationMessage.value = 'JSON 格式正确'

      // 清除验证消息，3秒后消失
      setTimeout(() => {
        validationMessage.value = ''
      }, 3000)

      return parsed
    } catch {
      // 如果直接解析失败，尝试修复JavaScript对象字面量
      try {
        const fixedJson = fixJavaScriptToJson(jsonStr)
        const parsed = JSON.parse(fixedJson)

        hasError.value = false
        errorMessage.value = ''
        validationMessage.value = 'JavaScript 对象已自动转换为 JSON 格式'

        // 自动格式化修复后的JSON
        jsonString.value = JSON.stringify(parsed, null, 2)

        // 清除验证消息，5秒后消失
        setTimeout(() => {
          validationMessage.value = ''
        }, 5000)

        return parsed
      } catch {
        hasError.value = true
        errorMessage.value = '格式错误，请检查输入的配置'
        validationMessage.value = ''
        return null
      }
    }
  }

  // 格式化JSON
  const formatJson = () => {
    if (!jsonString.value.trim()) {
      ElMessage.warning('请先输入 JSON 内容')
      return
    }

    try {
      const parsed = JSON.parse(jsonString.value)
      jsonString.value = JSON.stringify(parsed, null, 2)

      // 如果编辑器实例存在，使用编辑器的格式化功能
      if (editor.value) {
        editor.value.getAction('editor.action.formatDocument').run()
      }

      ElMessage.success('JSON 格式化成功')
    } catch {
      ElMessage.error('JSON 格式错误，无法格式化')
    }
  }

  // 验证JSON
  const validateJson = () => {
    if (!jsonString.value.trim()) {
      ElMessage.warning('请先输入 JSON 内容')
      return
    }

    const result = validateJsonString(jsonString.value)
    if (result) {
      ElMessage.success('JSON 验证通过')
    } else {
      ElMessage.error('JSON 验证失败')
    }
  }

  // 转换JavaScript对象为JSON
  const convertJsToJson = () => {
    if (!jsonString.value.trim()) {
      ElMessage.warning('请先输入内容')
      return
    }

    try {
      const fixedJson = fixJavaScriptToJson(jsonString.value)
      const parsed = JSON.parse(fixedJson)
      jsonString.value = JSON.stringify(parsed, null, 2)
      ElMessage.success('JavaScript 对象已转换为 JSON 格式')
    } catch {
      ElMessage.error('转换失败，请检查格式')
    }
  }

  // 重置为默认配置
  const resetToDefault = () => {
    try {
      const defaultOptions = getChartTemplate(props.chartType)
      jsonString.value = JSON.stringify(defaultOptions, null, 2)
      hasError.value = false
      errorMessage.value = ''
      ElMessage.success('已重置为默认配置')
    } catch {
      ElMessage.error('重置失败')
    }
  }

  // 处理保存
  const handleSave = () => {
    if (hasError.value) {
      ElMessage.error('请修复 JSON 格式错误后再保存')
      return
    }

    try {
      const options = jsonString.value.trim()
        ? JSON.parse(jsonString.value)
        : {}
      emit('save', options)
      dialogVisible.value = false
      ElMessage.success('配置保存成功')
    } catch {
      ElMessage.error('保存失败，请检查配置格式')
    }
  }

  // 处理关闭
  const handleClose = () => {
    dialogVisible.value = false
  }
</script>

<style scoped>
  .options-editor-dialog {
    .editor-container {
      display: flex;
      flex-direction: column;
      height: 600px;
    }

    .editor-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #e4e7ed;
    }

    .tips {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #606266;
      font-size: 14px;

      .el-icon {
        color: #409eff;
      }
    }

    .actions {
      display: flex;
      gap: 8px;
    }

    .editor-content {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .monaco-editor {
      flex: 1;
      border: 1px solid #dcdfe6;
      border-radius: 6px;
      overflow: hidden;

      &.error {
        border-color: #f56c6c;
        box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
      }
    }

    /* Monaco Editor 全局样式 */
    :deep(.monaco-editor) {
      .monaco-editor-background {
        background-color: #ffffff;
      }

      .line-numbers {
        color: #999;
      }

      .current-line {
        background-color: #f5f7fa;
      }

      .margin {
        background-color: #fafafa;
      }
    }

    .error-message {
      margin-top: 8px;
      padding: 8px 12px;
      background-color: #fef0f0;
      border: 1px solid #fbc4c4;
      border-radius: 4px;
      color: #f56c6c;
      font-size: 12px;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .validation-message {
      margin-top: 8px;
      padding: 8px 12px;
      background-color: #f0f9ff;
      border: 1px solid #b3d8ff;
      border-radius: 4px;
      color: #67c23a;
      font-size: 12px;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .dialog-footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }
  }

  :deep(.el-dialog__body) {
    padding: 20px;
  }

  :deep(.el-dialog__header) {
    padding: 20px 20px 0;
  }

  :deep(.el-dialog__footer) {
    padding: 0 20px 20px;
  }
</style>
