<template>
  <div class="schema-renderer">
    <div
      class="renderer-container"
      :class="{ 'is-preview': isPreview }"
      :style="containerStyle"
    >
      <!-- 渲染组件 -->
      <div
        v-for="component in topLevelComponents"
        :key="component.id"
        class="rendered-component"
        :class="{
          'is-preview': isPreview,
          'full-canvas': component.fullCanvas
        }"
        :style="getComponentStyle(component)"
      >
        <RenderComponent
          :component="component"
          :is-preview="isPreview"
          :all-components="schema.components"
          :schema="schema"
          @update-component="emit('update-component', $event)"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
  import { computed } from 'vue'
  import RenderComponent from './RenderComponent.vue'

  // 定义props
  const props = defineProps({
    schema: {
      type: Object,
      default: () => ({
        canvas: { width: 1920, height: 1080 },
        components: []
      })
    },
    isPreview: {
      type: Boolean,
      default: false
    }
  })

  // 定义emits
  const emit = defineEmits(['update-component'])

  const containerStyle = computed(() => {
    if (props.schema && props.schema.canvas) {
      const canvas = props.schema.canvas

      // 响应式模式：使用100vw和100vh
      if (canvas.isResponsive) {
        return {
          width: '100vw',
          height: '100vh',
          margin: '0',
          padding: '0',
          boxSizing: 'border-box',
          overflow: 'hidden'
        }
      }

      // 固定尺寸模式
      return {
        width: `${canvas.width}px`,
        height: `${canvas.height}px`
      }
    }
    return {}
  })

  // 过滤掉子容器，只渲染顶级组件
  const topLevelComponents = computed(() => {
    if (!props.schema || !props.schema.components) return []

    return props.schema.components.filter(
      component => !component.config?.isChildContainer
    )
  })

  // 计算组件样式
  const getComponentStyle = component => {
    const style = {
      position: 'absolute',
      zIndex: 1
    }

    // 如果是子容器，不使用绝对定位（它们在父容器中通过flex布局）
    if (component.config?.isChildContainer) {
      return {
        width: '100%',
        height: '100%',
        position: 'relative'
      }
    }

    // 支持100vw和100vh：如果宽度或高度设置为100vw/100vh，则铺满画布
    if (
      component.size?.width === '100vw' &&
      component.size?.height === '100vh'
    ) {
      return {
        ...style,
        left: '0',
        top: '0',
        width: '100%',
        height: '100%'
      }
    }

    // 如果组件设置了铺满画布，使用100%铺满画布容器
    if (component.fullCanvas) {
      return {
        ...style,
        left: '0',
        top: '0',
        width: '100%',
        height: '100%'
      }
    }

    // 获取实际位置和尺寸 - 确保数值类型
    let actualX = Number(component.position?.x) || 0
    let actualY = Number(component.position?.y) || 0
    let actualWidth = Number(component.size?.width) || 100
    let actualHeight = Number(component.size?.height) || 100

    // 兼容旧的百分比格式
    if (
      typeof component.position?.x === 'string' &&
      component.position.x.endsWith('%')
    ) {
      actualX =
        (parseFloat(component.position.x) * props.schema.canvas.width) / 100
    }
    if (
      typeof component.position?.y === 'string' &&
      component.position.y.endsWith('%')
    ) {
      actualY =
        (parseFloat(component.position.y) * props.schema.canvas.height) / 100
    }
    if (
      typeof component.size?.width === 'string' &&
      component.size.width.endsWith('%')
    ) {
      actualWidth =
        (parseFloat(component.size.width) * props.schema.canvas.width) / 100
    }
    if (
      typeof component.size?.height === 'string' &&
      component.size.height.endsWith('%')
    ) {
      actualHeight =
        (parseFloat(component.size.height) * props.schema.canvas.height) / 100
    }

    // 在响应式模式下，使用百分比来确保正确的缩放显示
    if (props.schema.canvas.isResponsive) {
      const leftPercent = (actualX / props.schema.canvas.width) * 100
      const topPercent = (actualY / props.schema.canvas.height) * 100
      let widthPercent = (actualWidth / props.schema.canvas.width) * 100
      const heightPercent = (actualHeight / props.schema.canvas.height) * 100

      // 特殊处理：检测DIV容器是否应该铺满宽度
      // 只有当宽度不是精确的画布宽度时才进行自动调整
      // 如果用户手动设置了精确的画布宽度，则尊重用户的设置
      if (
        component.type === 'div-container' &&
        leftPercent === 0 &&
        widthPercent > 40 &&
        Math.abs(actualWidth - props.schema.canvas.width) > 1 // 不是精确的画布宽度
      ) {
        widthPercent = 100
        console.log('🔍 检测到DIV容器可能想要铺满宽度，自动调整为100%', {
          componentId: component.id,
          originalWidth: actualWidth,
          canvasWidth: props.schema.canvas.width,
          originalPercent: (actualWidth / props.schema.canvas.width) * 100,
          reason: '从左边缘开始且宽度超过40%，但不是精确画布宽度'
        })
      } else if (
        component.type === 'div-container' &&
        Math.abs(actualWidth - props.schema.canvas.width) <= 1
      ) {
        // 如果是精确的画布宽度，保持100%显示但记录日志
        widthPercent = 100
        console.log('🔍 DIV容器使用精确画布宽度，保持100%显示', {
          componentId: component.id,
          originalWidth: actualWidth,
          canvasWidth: props.schema.canvas.width,
          reason: '手动设置为精确画布宽度'
        })
      }

      // 调试信息
      console.log('🔍 响应式模式组件样式计算:', {
        componentId: component.id,
        componentType: component.type,
        canvasSize: {
          width: props.schema.canvas.width,
          height: props.schema.canvas.height
        },
        actualSize: {
          x: actualX,
          y: actualY,
          width: actualWidth,
          height: actualHeight
        },
        percentSize: {
          left: leftPercent,
          top: topPercent,
          width: widthPercent,
          height: heightPercent
        }
      })

      return {
        ...style,
        left: `${leftPercent}%`,
        top: `${topPercent}%`,
        width: `${widthPercent}%`,
        height: `${heightPercent}%`
      }
    }

    // 固定尺寸模式下使用像素值
    console.log('🔍 固定尺寸模式组件样式计算:', {
      componentId: component.id,
      componentType: component.type,
      originalPosition: component.position,
      originalSize: component.size,
      actualSize: {
        x: actualX,
        y: actualY,
        width: actualWidth,
        height: actualHeight
      },
      finalStyle: {
        left: `${actualX}px`,
        top: `${actualY}px`,
        width: `${actualWidth}px`,
        height: `${actualHeight}px`
      }
    })

    return {
      ...style,
      left: `${actualX}px`,
      top: `${actualY}px`,
      width: `${actualWidth}px`,
      height: `${actualHeight}px`
    }
  }
</script>

<style scoped>
  .schema-renderer {
    width: 100%;
    height: 100%;
  }

  .renderer-container {
    position: relative;
    background-color: white;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    background-image:
      linear-gradient(to right, #f0f0f0 1px, transparent 1px),
      linear-gradient(to bottom, #f0f0f0 1px, transparent 1px);
    background-size: 20px 20px;
  }

  .renderer-container.is-preview {
    background-image: none;
    box-shadow: none;
  }

  .rendered-component {
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    background: white;
    overflow: hidden;
    min-width: 50px;
    min-height: 50px;
  }

  .rendered-component.is-preview {
    box-shadow: none;
  }

  .rendered-component.full-canvas {
    border-radius: 0;
    min-width: 0 !important;
    min-height: 0 !important;
    box-shadow: none;
  }

  .rendered-component.is-preview.full-canvas {
    box-shadow: none;
    background: transparent;
  }
</style>
