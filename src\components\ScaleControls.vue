<template>
  <div class="scale-controls">
    <div class="scale-buttons">
      <el-button-group size="small">
        <el-button
          :icon="ZoomOut"
          @click="handleZoomOut"
          :disabled="isMinScale"
          title="缩小"
        />
        <el-button
          :icon="ZoomIn"
          @click="handleZoomIn"
          :disabled="isMaxScale"
          title="放大"
        />
        <el-button
          :icon="FullScreen"
          @click="handleFitToScreen"
          :type="scaleMode === 'fit' ? 'primary' : 'default'"
          title="适应屏幕"
        />
        <el-button
          :icon="Refresh"
          @click="handleAutoScale"
          :type="scaleMode === 'auto' ? 'primary' : 'default'"
          title="自适应"
        />
        <el-button @click="handleReset" title="重置 (100%)" size="small">
          1:1
        </el-button>
      </el-button-group>
    </div>

    <div class="scale-display">
      <el-dropdown @command="handleScaleCommand" trigger="click">
        <el-button size="small" text>
          {{ scalePercentage }}%
          <el-icon class="el-icon--right">
            <arrow-down />
          </el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item
              v-for="preset in scalePresets"
              :key="preset"
              :command="preset"
              :class="{ active: Math.abs(currentScale - preset / 100) < 0.01 }"
            >
              {{ preset }}%
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <div class="scale-info" v-if="showInfo">
      <span class="scale-mode">{{ scaleModeText }}</span>
    </div>
  </div>
</template>

<script setup>
  import { computed, inject } from 'vue'
  import {
    ElButton,
    ElButtonGroup,
    ElDropdown,
    ElDropdownMenu,
    ElDropdownItem,
    ElIcon
  } from 'element-plus'
  import {
    ZoomIn,
    ZoomOut,
    FullScreen,
    Refresh,
    ArrowDown
  } from '@element-plus/icons-vue'

  defineProps({
    showInfo: {
      type: Boolean,
      default: false
    }
  })

  // 注入缩放控制器
  const scaleControls = inject('scaleControls')

  if (!scaleControls) {
    console.warn('ScaleControls: 未找到缩放控制器')
  }

  // 缩放预设选项
  const scalePresets = [25, 50, 75, 100, 125, 150, 200, 300]

  // 当前缩放比例
  const currentScale = computed(() => scaleControls?.currentScale?.value || 1)

  // 缩放百分比显示
  const scalePercentage = computed(() => Math.round(currentScale.value * 100))

  // 缩放模式
  const scaleMode = computed(() => scaleControls?.scaleMode?.value || 'auto')

  // 缩放模式文本
  const scaleModeText = computed(() => {
    switch (scaleMode.value) {
      case 'auto':
        return '自适应'
      case 'fit':
        return '适应屏幕'
      case 'manual':
        return '手动'
      default:
        return '未知'
    }
  })

  // 是否达到最小缩放
  const isMinScale = computed(() => currentScale.value <= 0.1)

  // 是否达到最大缩放
  const isMaxScale = computed(() => currentScale.value >= 3)

  // 事件处理
  const handleZoomIn = () => {
    scaleControls?.zoomIn?.()
  }

  const handleZoomOut = () => {
    scaleControls?.zoomOut?.()
  }

  const handleFitToScreen = () => {
    scaleControls?.fitToScreen?.()
  }

  const handleAutoScale = () => {
    scaleControls?.autoScale?.()
  }

  const handleReset = () => {
    scaleControls?.resetScale?.()
  }

  const handleScaleCommand = scale => {
    scaleControls?.setScale?.(scale / 100)
  }
</script>

<style scoped>
  .scale-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .scale-buttons {
    display: flex;
    align-items: center;
  }

  .scale-display {
    border-left: 1px solid #e4e7ed;
    padding-left: 12px;
  }

  .scale-info {
    font-size: 12px;
    color: #909399;
    border-left: 1px solid #e4e7ed;
    padding-left: 12px;
  }

  .scale-mode {
    font-weight: 500;
  }

  :deep(.el-dropdown-menu__item.active) {
    color: #409eff;
    background-color: #f0f9ff;
  }

  /* 按钮样式优化 */
  .scale-controls .el-button {
    border: none;
    background: transparent;
    color: #606266;
    transition: all 0.3s;
  }

  .scale-controls .el-button:hover {
    background: #f5f7fa;
    color: #409eff;
  }

  .scale-controls .el-button.is-disabled {
    background: transparent;
    color: #c0c4cc;
  }

  .scale-controls .el-button--primary {
    background: #409eff;
    color: #fff;
  }

  .scale-controls .el-button--primary:hover {
    background: #66b1ff;
  }

  /* 小屏幕适配 */
  @media (max-width: 768px) {
    .scale-controls {
      flex-wrap: wrap;
      gap: 8px;
    }

    .scale-info {
      display: none;
    }
  }
</style>
