<template>
  <div class="lifecycle-panel">
    <div class="panel-header">
      <h3>生命周期管理</h3>
    </div>

    <div class="panel-content">
      <div class="lifecycle-section">
        <h4>mounted 钩子</h4>
        <div class="lifecycle-actions">
          <ElButton type="primary" size="small" @click="openMountedEditor">
            <ElIcon><EditPen /></ElIcon>
            编辑代码
          </ElButton>
        </div>
        <div v-if="mountedCode.trim()" class="code-preview">
          <pre>{{ mountedCode }}</pre>
        </div>
        <div v-else class="empty-code">
          <p>暂无mounted代码</p>
        </div>
      </div>
    </div>

    <!-- mounted代码编辑对话框 -->
    <ElDialog
      v-model="editorDialogVisible"
      title="mounted 生命周期代码编辑"
      width="90%"
      :before-close="handleEditorClose"
      class="lifecycle-editor-dialog"
    >
      <div class="editor-toolbar">
        <div class="toolbar-left">
          <span class="editor-info"
            >编写mounted生命周期代码，支持语法高亮和智能提示</span
          >
        </div>
        <div class="toolbar-right">
          <ElButton size="small" @click="formatCode"> 格式化代码 </ElButton>
        </div>
      </div>

      <div class="editor-container">
        <MonacoEditor
          ref="monacoEditor"
          v-model="mountedCode"
          language="javascript"
          theme="vs-dark"
          height="400px"
          :options="editorOptions"
          @change="onCodeChange"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <div class="footer-left">
            <span class="code-info">mounted生命周期在组件挂载后执行</span>
          </div>
          <div class="footer-right">
            <ElButton @click="cancelEditor">取消</ElButton>
            <ElButton type="primary" @click="saveMountedCode">保存</ElButton>
          </div>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<script setup>
  import { ref, watch } from 'vue'
  import {
    ElButton,
    ElIcon,
    ElDialog,
    ElMessage,
    ElMessageBox
  } from 'element-plus'
  import { EditPen } from '@element-plus/icons-vue'
  import MonacoEditor from './MonacoEditor.vue'

  const props = defineProps({
    mounted: {
      type: String,
      default: ''
    },
    availableMethods: {
      type: Array,
      default: () => []
    }
  })

  const emit = defineEmits(['update-mounted'])

  const mountedCode = ref(props.mounted)
  const editorDialogVisible = ref(false)
  const monacoEditor = ref(null)
  const originalCode = ref('')

  const editorOptions = {
    fontSize: 14,
    tabSize: 2,
    insertSpaces: true,
    wordWrap: 'on',
    lineNumbers: 'on',
    minimap: { enabled: true },
    scrollBeyondLastLine: false,
    folding: true,
    contextmenu: true,
    suggest: {
      insertMode: 'replace',
      filterGraceful: true,
      showKeywords: true,
      showSnippets: true,
      showFunctions: true,
      showVariables: true
    }
  }

  // 监听props变化
  watch(
    () => props.mounted,
    newValue => {
      mountedCode.value = newValue
    }
  )

  const openMountedEditor = () => {
    originalCode.value = mountedCode.value
    editorDialogVisible.value = true
  }

  const handleEditorClose = () => {
    if (mountedCode.value !== originalCode.value) {
      ElMessageBox.confirm('代码已修改但未保存，确定要关闭吗？', '确认关闭', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          mountedCode.value = originalCode.value
          editorDialogVisible.value = false
        })
        .catch(() => {
          // 用户取消，不关闭对话框
        })
    } else {
      editorDialogVisible.value = false
    }
  }

  const cancelEditor = () => {
    mountedCode.value = originalCode.value
    editorDialogVisible.value = false
  }

  const saveMountedCode = () => {
    emit('update-mounted', mountedCode.value)
    editorDialogVisible.value = false
    ElMessage.success('mounted代码已保存')
  }

  const formatCode = () => {
    if (monacoEditor.value) {
      monacoEditor.value.formatDocument()
    }
  }

  const onCodeChange = value => {
    mountedCode.value = value
  }
</script>

<style scoped>
  .lifecycle-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .panel-header {
    padding: 16px;
    border-bottom: 1px solid #e4e7ed;
    background: #fff;
  }

  .panel-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }

  .panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 16px;
  }

  .lifecycle-section {
    margin-bottom: 24px;
    padding: 8px 0;
  }

  .lifecycle-section h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: #606266;
  }

  .lifecycle-actions {
    margin-bottom: 16px;
  }

  .code-preview {
    background: #f5f5f5;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 12px;
    max-height: 200px;
    overflow-y: auto;
  }

  .code-preview pre {
    margin: 0;
    font-family: 'Monaco', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    color: #303133;
  }

  .empty-code {
    text-align: center;
    padding: 20px;
    color: #909399;
    border: 1px dashed #e4e7ed;
    border-radius: 4px;
  }

  .editor-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e4e7ed;
    margin-bottom: 16px;
  }

  .toolbar-left .editor-info {
    color: #606266;
    font-size: 14px;
  }

  .toolbar-right {
    display: flex;
    gap: 8px;
  }

  .editor-container {
    margin-bottom: 16px;
  }

  .dialog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .footer-left .code-info {
    color: #606266;
    font-size: 14px;
  }

  .footer-right {
    display: flex;
    gap: 8px;
  }

  :deep(.lifecycle-editor-dialog) {
    .el-dialog__body {
      padding: 20px;
    }
  }
</style>
