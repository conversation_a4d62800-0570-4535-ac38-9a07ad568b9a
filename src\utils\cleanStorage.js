/**
 * 清理旧版本的localStorage键
 */
export function cleanOldStorageKeys() {
  const oldKeys = ['bi-schema-flat', 'bi-schema-hierarchical-preview']

  oldKeys.forEach(key => {
    if (localStorage.getItem(key)) {
      localStorage.removeItem(key)
      console.log(`已清理旧的存储键: ${key}`)
    }
  })

  console.log('旧存储键清理完成')
}

/**
 * 检查当前schema版本
 */
export function checkSchemaVersion() {
  const schema = localStorage.getItem('bi-schema')
  const previewSchema = localStorage.getItem('bi-schema-preview')

  if (schema) {
    try {
      const parsed = JSON.parse(schema)
      console.log('主Schema版本:', parsed.version || '1.0 (旧版本)')
    } catch {
      console.error('无法解析主Schema')
    }
  }

  if (previewSchema) {
    try {
      const parsed = JSON.parse(previewSchema)
      console.log('预览Schema版本:', parsed.version || '1.0 (旧版本)')
    } catch {
      console.error('无法解析预览Schema')
    }
  }
}
