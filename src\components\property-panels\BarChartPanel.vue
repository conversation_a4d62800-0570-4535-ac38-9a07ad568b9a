<template>
  <ElFormItem label="图表类型">
    <ElSelect
      :model-value="localConfig.type || 'basic'"
      @change="updateProperty('type', $event)"
      size="small"
      placeholder="请选择"
    >
      <ElOption label="基础柱状图" value="basic" />
      <ElOption label="堆叠柱状图" value="stack" />
      <ElOption label="条形图" value="horizontal" />
      <ElOption label="堆叠条形图" value="horizontal-stack" />
    </ElSelect>
  </ElFormItem>

  <ElFormItem label="柱子宽度">
    <ElInputNumber
      :model-value="localConfig.barWidth || 20"
      @change="updateProperty('barWidth', $event)"
      :min="1"
      :max="100"
      size="small"
      controls-position="right"
    />
  </ElFormItem>

  <ElFormItem label="柱子间距">
    <ElInputNumber
      :model-value="localConfig.barGap || 30"
      @change="updateProperty('barGap', $event)"
      :min="0"
      :max="200"
      size="small"
      controls-position="right"
    />
  </ElFormItem>

  <ElFormItem label="显示数值">
    <ElSwitch
      :model-value="localConfig.showValue"
      @change="updateProperty('showValue', $event)"
    />
  </ElFormItem>

  <!-- 数值位置设置 - 只有在显示数值时才显示 -->
  <ElFormItem v-if="localConfig.showValue" label="数值位置">
    <ElSelect
      :model-value="localConfig.valuePosition || 'inside'"
      @change="updateProperty('valuePosition', $event)"
      size="small"
      placeholder="请选择位置"
    >
      <ElOption label="顶部" value="top" />
      <ElOption label="内部" value="inside" />
      <ElOption label="内部顶部" value="insideTop" />
      <ElOption label="内部底部" value="insideBottom" />
      <ElOption label="内部左侧" value="insideLeft" />
      <ElOption label="内部右侧" value="insideRight" />
      <ElOption label="左侧" value="left" />
      <ElOption label="右侧" value="right" />
      <ElOption label="底部" value="bottom" />
    </ElSelect>
  </ElFormItem>

  <!-- 数值样式设置 - 只有在显示数值时才显示 -->
  <template v-if="localConfig.showValue">
    <ElFormItem label="数值大小">
      <ElInputNumber
        :model-value="localConfig.valueFontSize || 12"
        @change="updateProperty('valueFontSize', $event)"
        :min="8"
        :max="24"
        size="small"
        controls-position="right"
      />
    </ElFormItem>

    <ElFormItem label="数值颜色">
      <ElColorPicker
        :model-value="localConfig.valueColor || '#333333'"
        @change="updateProperty('valueColor', $event)"
        size="small"
        show-alpha
      />
    </ElFormItem>
  </template>

  <ElFormItem label="数值范围">
    <div class="range-inputs">
      <ElInputNumber
        :model-value="localConfig.min"
        @change="updateProperty('min', $event)"
        size="small"
        controls-position="right"
        placeholder="Min"
      />
      <ElInputNumber
        :model-value="localConfig.max"
        @change="updateProperty('max', $event)"
        size="small"
        controls-position="right"
        placeholder="Max"
      />
    </div>
  </ElFormItem>

  <!-- 变量绑定组件 -->
  <VariableBinding
    :config="componentConfig"
    :variables="variables"
    @update-property="handleVariableBindingUpdate"
  />
</template>

<script setup>
  import { computed } from 'vue'
  import {
    ElFormItem,
    ElSelect,
    ElOption,
    ElInputNumber,
    ElSwitch,
    ElColorPicker
  } from 'element-plus'
  import VariableBinding from './VariableBinding.vue'

  const props = defineProps({
    barConfig: {
      type: Object,
      required: true
    },
    componentConfig: {
      type: Object,
      required: true
    },
    variables: {
      type: Object,
      default: () => ({})
    }
  })

  const emit = defineEmits(['update-property', 'update:type'])

  const localConfig = computed({
    get: () => props.barConfig,
    set: () => {
      // We don't use the setter, updates are handled via events
    }
  })

  function updateProperty(key, value) {
    if (key === 'type') {
      emit('update:type', value)
    } else {
      emit('update-property', `config.barConfig.${key}`, value)
    }
  }

  // 处理变量绑定更新
  function handleVariableBindingUpdate(key, value) {
    emit('update-property', key, value)
  }
</script>

<style scoped>
  .range-inputs {
    display: flex;
    gap: 8px;
    width: 100%;
  }
  .el-select {
    width: 100%;
  }
</style>
