<template>
  <ElDialog
    v-model="dialogVisible"
    title="事件编辑器"
    width="90%"
    :show-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    custom-class="event-editor-dialog"
    style="height: 85vh"
  >
    <div class="event-editor-container">
      <!-- 左侧代码编辑器 -->
      <div class="editor-panel">
        <div class="editor-header">
          <h4>事件方法体</h4>
          <div class="editor-actions">
            <ElButton size="small" @click="toggleTheme">
              {{ currentTheme === 'vs-dark' ? '浅色主题' : '深色主题' }}
            </ElButton>
            <ElButton size="small" @click="formatCode">格式化代码</ElButton>
            <ElButton size="small" @click="resetCode">重置</ElButton>
          </div>
        </div>
        <div class="code-editor" ref="editorContainer">
          <VueMonacoEditor
            :value="localEventCode"
            language="javascript"
            :theme="currentTheme"
            height="100%"
            :options="editorOptions"
            @mount="onEditorMount"
            @error="onEditorError"
          />
        </div>
        <div class="editor-tips">
          <ElAlert
            type="info"
            :closable="false"
            show-icon
            title="使用提示"
            description="使用Monaco编辑器，支持语法高亮和代码格式化。点击右侧的变量、工具或代码片段可以插入到光标位置。"
          />
        </div>
      </div>

      <!-- 右侧面板 -->
      <div class="sidebar-panel">
        <!-- 表单变量 -->
        <div class="variable-section">
          <h4>表单变量</h4>
          <div class="variable-list">
            <div
              v-for="variable in formVariables"
              :key="variable.id"
              class="variable-item"
              @click="insertVariable(variable)"
            >
              <div class="variable-name">{{ variable.name }}</div>
              <div class="variable-type">{{ variable.type }}</div>
              <div class="variable-desc">{{ variable.description }}</div>
            </div>
            <div v-if="formVariables.length === 0" class="empty-message">
              暂无表单变量
            </div>
          </div>
        </div>

        <!-- 预装工具类 -->
        <div class="tools-section">
          <h4>预装工具类</h4>
          <div class="tools-list">
            <div
              v-for="tool in availableTools"
              :key="tool.name"
              class="tool-item"
              @click="insertTool(tool)"
            >
              <div class="tool-name">{{ tool.name }}</div>
              <div class="tool-desc">{{ tool.description }}</div>
              <div class="tool-example">{{ tool.example }}</div>
            </div>
          </div>
        </div>

        <!-- 常用代码片段 -->
        <div class="snippets-section">
          <h4>代码片段</h4>
          <div class="snippets-list">
            <div
              v-for="snippet in codeSnippets"
              :key="snippet.name"
              class="snippet-item"
              @click="insertSnippet(snippet)"
            >
              <div class="snippet-name">{{ snippet.name }}</div>
              <div class="snippet-desc">{{ snippet.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="cancelEdit">取消</ElButton>
        <ElButton type="primary" @click="saveEvent">保存事件</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup>
  import { ref, computed, watch, shallowRef } from 'vue'
  import { ElDialog, ElButton, ElAlert, ElMessage } from 'element-plus'
  import { VueMonacoEditor } from '@guolao/vue-monaco-editor'

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    eventName: {
      type: String,
      default: ''
    },
    eventCode: {
      type: String,
      default: ''
    },
    buttonConfig: {
      type: Object,
      default: () => ({})
    },
    formVariables: {
      type: Array,
      default: () => []
    }
  })

  const emit = defineEmits(['update:visible', 'save'])

  const dialogVisible = computed({
    get: () => props.visible,
    set: value => emit('update:visible', value)
  })

  const localEventCode = ref('')
  const localEventName = ref('')
  const editorContainer = ref(null)
  const monacoEditor = shallowRef(null)
  const currentTheme = ref('vs-dark')

  // Monaco Editor 配置选项
  const editorOptions = ref({
    fontSize: 14,
    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
    lineHeight: 22,
    minimap: { enabled: false },
    scrollBeyondLastLine: false,
    automaticLayout: true,
    tabSize: 2,
    insertSpaces: true,
    wordWrap: 'on',
    lineNumbers: 'on',
    glyphMargin: false,
    folding: true,
    lineDecorationsWidth: 0,
    lineNumbersMinChars: 3,
    renderLineHighlight: 'line',
    contextmenu: true,
    selectOnLineNumbers: true,
    roundedSelection: false,
    readOnly: false,
    cursorStyle: 'line'
  })

  // 预装工具类
  const availableTools = ref([
    {
      name: 'axios',
      description: 'HTTP请求库',
      example: 'axios.get("/api/data")',
      code: 'axios'
    },
    {
      name: 'dayjs',
      description: '日期时间处理库',
      example: 'dayjs().format("YYYY-MM-DD")',
      code: 'dayjs'
    },
    {
      name: 'ElMessage',
      description: 'Element Plus 消息提示',
      example: 'ElMessage.success("操作成功")',
      code: 'ElMessage'
    },
    {
      name: 'ElMessageBox',
      description: 'Element Plus 确认对话框',
      example: 'ElMessageBox.confirm("确定删除?")',
      code: 'ElMessageBox'
    },
    {
      name: 'console',
      description: '控制台输出',
      example: 'console.log("调试信息")',
      code: 'console'
    }
  ])

  // 代码片段
  const codeSnippets = ref([
    {
      name: '异步请求',
      description: '发送HTTP请求',
      code: `try {
  const response = await axios.get('/api/data')
  console.log('请求成功:', response.data)
  ElMessage.success('操作成功')
} catch (error) {
  console.error('请求失败:', error)
  ElMessage.error('操作失败')
}`
    },
    {
      name: '表单验证',
      description: '验证表单数据',
      code: `// 获取表单变量值
const formData = this.variables.formData

// 检查必填项
if (!formData.name || formData.name.trim() === '') {
  ElMessage.warning('请输入姓名')
  return
}

console.log('表单验证通过')`
    },
    {
      name: '确认对话框',
      description: '显示确认对话框',
      code: `const result = await ElMessageBox.confirm(
  '确定要执行此操作吗?',
  '提示',
  {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }
)
if (result === 'confirm') {
  console.log('用户确认操作')
}`
    },
    {
      name: '日期格式化',
      description: '格式化当前日期',
      code: `const now = dayjs()
const formatted = now.format('YYYY-MM-DD HH:mm:ss')
console.log('当前时间:', formatted)`
    }
  ])

  // 插入变量
  const insertVariable = variable => {
    const variableCode = `this.variables.formData.${variable.name}`
    insertTextAtCursor(variableCode)
  }

  // 插入工具类
  const insertTool = tool => {
    insertTextAtCursor(tool.example)
  }

  // 插入代码片段
  const insertSnippet = snippet => {
    insertTextAtCursor(snippet.code)
  }

  // Monaco Editor 挂载事件
  const onEditorMount = editor => {
    console.log('Monaco Editor 挂载成功')
    monacoEditor.value = editor

    // 设置编辑器主题
    try {
      if (window.monaco?.editor) {
        window.monaco.editor.setTheme(currentTheme.value)
      }
    } catch (error) {
      console.log('设置主题失败:', error)
    }

    // 其他配置可以延迟加载，避免阻塞UI
    setTimeout(() => {
      try {
        // 配置JavaScript语言特性
        if (window.monaco?.languages?.typescript?.javascriptDefaults) {
          window.monaco.languages.typescript.javascriptDefaults.setDiagnosticsOptions(
            {
              noSemanticValidation: false,
              noSyntaxValidation: false
            }
          )
        }
      } catch (error) {
        console.log('配置语言特性失败:', error)
      }
    }, 100)
  }

  // 编辑器错误处理
  const onEditorError = error => {
    console.error('Monaco Editor 加载或运行错误:', error)
    ElMessage.warning('Monaco Editor 出现问题，但功能仍可用')
  }

  // 在光标位置智能插入文本
  const insertTextAtCursor = text => {
    if (!monacoEditor.value) {
      ElMessage.warning('编辑器未准备好')
      return
    }

    try {
      const editor = monacoEditor.value
      const model = editor.getModel()
      const position = editor.getPosition()

      if (!model || !position) {
        ElMessage.warning('无法获取光标位置')
        return
      }

      // 获取当前行的内容
      const currentLineContent = model.getLineContent(position.lineNumber)
      const lineStartToPosition = currentLineContent.substring(
        0,
        position.column - 1
      )
      const positionToLineEnd = currentLineContent.substring(
        position.column - 1
      )

      // 智能决定如何插入文本
      let textToInsert = text
      let insertRange = {
        startLineNumber: position.lineNumber,
        startColumn: position.column,
        endLineNumber: position.lineNumber,
        endColumn: position.column
      }

      // 情况1: 光标在空行
      if (currentLineContent.trim() === '') {
        textToInsert = text
      }
      // 情况2: 光标在行尾且当前行有内容
      else if (position.column > currentLineContent.length) {
        textToInsert = '\n\n' + text
      }
      // 情况3: 光标在行首且当前行有内容
      else if (position.column === 1 && currentLineContent.trim() !== '') {
        textToInsert = text + '\n\n'
      }
      // 情况4: 光标前有内容，后面也有内容（行中间）
      else if (
        lineStartToPosition.trim() !== '' &&
        positionToLineEnd.trim() !== ''
      ) {
        textToInsert = '\n\n' + text + '\n\n'
      }
      // 情况5: 光标前只有空白，后面有内容
      else if (
        lineStartToPosition.trim() === '' &&
        positionToLineEnd.trim() !== ''
      ) {
        textToInsert = text + '\n\n'
      }
      // 情况6: 光标前有内容，后面只有空白
      else if (
        lineStartToPosition.trim() !== '' &&
        positionToLineEnd.trim() === ''
      ) {
        textToInsert = '\n\n' + text
      }
      // 默认情况：直接插入
      else {
        textToInsert = text
      }

      // 执行插入
      const operation = {
        range: insertRange,
        text: textToInsert,
        forceMoveMarkers: true
      }

      editor.executeEdits('insert-snippet', [operation])

      // 计算新的光标位置（插入文本的末尾）
      const insertedLines = textToInsert.split('\n')
      const newLineNumber = position.lineNumber + insertedLines.length - 1
      const newColumn =
        insertedLines.length === 1
          ? position.column + insertedLines[0].length
          : insertedLines[insertedLines.length - 1].length + 1

      // 设置光标到插入文本的末尾
      editor.setPosition({
        lineNumber: newLineNumber,
        column: newColumn
      })

      editor.focus()
      ElMessage.success('代码片段插入成功')
    } catch (error) {
      console.error('插入代码片段失败:', error)
      ElMessage.error('代码片段插入失败')
    }
  }

  // 格式化代码
  const formatCode = async () => {
    try {
      if (monacoEditor.value) {
        const editor = monacoEditor.value
        await editor.getAction('editor.action.formatDocument').run()
        ElMessage.success('代码格式化成功')
      }
    } catch (error) {
      console.error('代码格式化失败:', error)
      ElMessage.error('代码格式化失败')
    }
  }

  // 重置代码
  const resetCode = () => {
    localEventCode.value = ''
    if (monacoEditor.value) {
      try {
        const editor = monacoEditor.value
        editor.setValue('')
        editor.focus()
      } catch (error) {
        console.log('清空编辑器失败:', error)
      }
    }
    ElMessage.success('代码已重置')
  }

  // 切换编辑器主题
  const toggleTheme = () => {
    const newTheme = currentTheme.value === 'vs-dark' ? 'vs' : 'vs-dark'
    currentTheme.value = newTheme

    if (window.monaco?.editor && monacoEditor.value) {
      window.monaco.editor.setTheme(newTheme)
    }
  }

  // 保存事件
  const saveEvent = () => {
    if (!localEventName.value.trim()) {
      ElMessage.warning('请输入事件名称')
      return
    }

    // 直接从编辑器获取最新内容
    let currentCode = localEventCode.value
    if (monacoEditor.value) {
      try {
        currentCode = monacoEditor.value.getValue()
      } catch (error) {
        console.log('获取编辑器内容失败，使用响应式变量:', error)
      }
    }

    if (!currentCode.trim()) {
      ElMessage.warning('请编写事件代码')
      return
    }

    const eventData = {
      name: localEventName.value,
      code: currentCode
    }

    emit('save', eventData)
    ElMessage.success('事件保存成功')
  }

  // 取消编辑
  const cancelEdit = () => {
    dialogVisible.value = false
  }

  // 监听对话框显示状态
  watch(dialogVisible, newVal => {
    if (newVal) {
      localEventCode.value = props.eventCode || getDefaultEventCode()
      localEventName.value = props.eventName
    }
  })

  // 获取默认事件代码模板
  const getDefaultEventCode = () => {
    return `// 自定义事件处理函数
// 可以使用 this.variables.formData 访问表单变量
// 可以使用预装的工具类：axios, dayjs, ElMessage, ElMessageBox

try {
  // 在这里编写您的事件逻辑
  console.log('按钮被点击了!')
  console.log('当前表单数据:', this.variables.formData)
  
  // 示例：显示消息提示
  ElMessage.success('自定义事件执行成功!')
  
} catch (error) {
  console.error('事件执行失败:', error)
  ElMessage.error('事件执行失败: ' + error.message)
}`
  }

  // 监听props变化
  watch(
    () => props.eventCode,
    newVal => {
      localEventCode.value = newVal
    }
  )

  watch(
    () => props.eventName,
    newVal => {
      localEventName.value = newVal
    }
  )
</script>

<style scoped>
  .event-editor-container {
    display: flex;
    flex: 1;
    gap: 16px;
    min-height: 0;
  }

  .editor-panel {
    flex: 2;
    display: flex;
    flex-direction: column;
  }

  .editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e4e7ed;
    flex-shrink: 0;
  }

  .editor-header h4 {
    margin: 0;
    color: #303133;
  }

  .editor-actions {
    display: flex;
    gap: 8px;
  }

  .code-editor {
    flex: 1;
    margin-bottom: 12px;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    overflow: hidden;
    min-height: 0;
    display: flex;
    flex-direction: column;
  }

  .code-editor :deep(.monaco-editor) {
    border-radius: 6px;
  }

  .code-editor :deep(.monaco-editor .monaco-editor-background) {
    background-color: #1e1e1e;
  }

  .editor-tips {
    margin-top: 12px;
    flex-shrink: 0;
  }

  .sidebar-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
    max-height: 100%;
    overflow-y: auto;
  }

  .variable-section,
  .tools-section,
  .snippets-section {
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    padding: 12px;
    background: #f8f9fa;
  }

  .variable-section h4,
  .tools-section h4,
  .snippets-section h4 {
    margin: 0 0 12px 0;
    color: #303133;
    font-size: 14px;
    font-weight: 500;
  }

  .variable-list,
  .tools-list,
  .snippets-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
  }

  .variable-item,
  .tool-item,
  .snippet-item {
    padding: 8px;
    background: white;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .variable-item:hover,
  .tool-item:hover,
  .snippet-item:hover {
    border-color: #409eff;
    background: #ecf5ff;
  }

  .variable-name,
  .tool-name,
  .snippet-name {
    font-weight: 500;
    color: #303133;
    font-size: 13px;
  }

  .variable-type {
    font-size: 11px;
    color: #909399;
    margin-top: 2px;
  }

  .variable-desc,
  .tool-desc,
  .snippet-desc {
    font-size: 12px;
    color: #606266;
    margin-top: 4px;
  }

  .tool-example {
    font-size: 11px;
    color: #909399;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    margin-top: 4px;
    background: #f5f7fa;
    padding: 2px 4px;
    border-radius: 2px;
  }

  .empty-message {
    text-align: center;
    color: #909399;
    font-size: 12px;
    padding: 20px;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  :deep(.event-editor-dialog) {
    .el-dialog {
      height: 85vh;
      display: flex;
      flex-direction: column;
    }

    .el-dialog__header {
      flex-shrink: 0;
    }

    .el-dialog__body {
      flex: 1;
      padding: 20px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    .el-dialog__footer {
      flex-shrink: 0;
    }
  }
</style>
