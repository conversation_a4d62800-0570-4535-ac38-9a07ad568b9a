<template>
  <div class="datepicker-panel">
    <ElFormItem label="标签文本">
      <ElInput
        :model-value="localConfig.label || '时间控件'"
        @input="updateProperty('label', $event)"
        size="small"
        placeholder="请输入标签文本"
      />
    </ElFormItem>

    <ElFormItem label="占位符">
      <ElInput
        :model-value="localConfig.placeholder || '请选择日期'"
        @input="updateProperty('placeholder', $event)"
        size="small"
        placeholder="请输入占位符文本"
      />
    </ElFormItem>

    <ElFormItem label="默认值">
      <ElInput
        :model-value="localConfig.value || ''"
        @input="updateProperty('value', $event)"
        size="small"
        placeholder="请输入默认值"
      />
    </ElFormItem>

    <!-- 变量绑定设置 -->
    <ElFormItem label="变量绑定">
      <ElSelect
        :model-value="localConfig.bindVariable || ''"
        @change="updateProperty('bindVariable', $event)"
        size="small"
        placeholder="选择绑定的变量"
        clearable
      >
        <ElOption label="不绑定变量" value="" />
        <ElOption
          v-for="variable in availableVariables"
          :key="variable.id"
          :label="`${variable.name} (${variable.type})`"
          :value="variable.id"
        />
      </ElSelect>
    </ElFormItem>

    <!-- 变量绑定提示 -->
    <ElFormItem
      v-if="localConfig.bindVariable && selectedVariable"
      label="绑定信息"
    >
      <div class="variable-info">
        <div class="variable-info-item">
          <span class="info-label">变量名:</span>
          <span class="info-value">{{ selectedVariable.name }}</span>
        </div>
        <div class="variable-info-item">
          <span class="info-label">类型:</span>
          <span class="info-value">{{ selectedVariable.type }}</span>
        </div>
        <div class="variable-info-item">
          <span class="info-label">描述:</span>
          <span class="info-value">{{
            selectedVariable.description || '无'
          }}</span>
        </div>
      </div>
    </ElFormItem>

    <ElFormItem label="选择器类型">
      <ElSelect
        :model-value="localConfig.type || 'date'"
        @change="updateProperty('type', $event)"
        size="small"
      >
        <ElOption label="日期" value="date" />
        <ElOption label="日期时间" value="datetime" />
        <ElOption label="年份" value="year" />
        <ElOption label="月份" value="month" />
        <ElOption label="多个日期" value="dates" />
        <ElOption label="周" value="week" />
        <ElOption label="日期范围" value="daterange" />
        <ElOption label="日期时间范围" value="datetimerange" />
        <ElOption label="月份范围" value="monthrange" />
      </ElSelect>
    </ElFormItem>

    <ElFormItem label="显示格式">
      <ElSelect
        :model-value="localConfig.format || 'YYYY-MM-DD'"
        @change="updateProperty('format', $event)"
        size="small"
        filterable
        allow-create
      >
        <ElOption label="YYYY-MM-DD" value="YYYY-MM-DD" />
        <ElOption label="YYYY/MM/DD" value="YYYY/MM/DD" />
        <ElOption label="DD/MM/YYYY" value="DD/MM/YYYY" />
        <ElOption label="MM/DD/YYYY" value="MM/DD/YYYY" />
        <ElOption label="YYYY-MM-DD HH:mm:ss" value="YYYY-MM-DD HH:mm:ss" />
        <ElOption label="YYYY/MM/DD HH:mm:ss" value="YYYY/MM/DD HH:mm:ss" />
        <ElOption label="YYYY年MM月DD日" value="YYYY年MM月DD日" />
      </ElSelect>
    </ElFormItem>

    <ElFormItem label="值格式">
      <ElSelect
        :model-value="localConfig.valueFormat || 'YYYY-MM-DD'"
        @change="updateProperty('valueFormat', $event)"
        size="small"
        filterable
        allow-create
      >
        <ElOption label="YYYY-MM-DD" value="YYYY-MM-DD" />
        <ElOption label="YYYY/MM/DD" value="YYYY/MM/DD" />
        <ElOption label="YYYY-MM-DD HH:mm:ss" value="YYYY-MM-DD HH:mm:ss" />
        <ElOption label="时间戳" value="x" />
      </ElSelect>
    </ElFormItem>

    <!-- 范围选择器的特殊配置 -->
    <template v-if="isRangeType">
      <ElFormItem label="范围分隔符">
        <ElInput
          :model-value="localConfig.rangeSeparator || '至'"
          @input="updateProperty('rangeSeparator', $event)"
          size="small"
          placeholder="至"
        />
      </ElFormItem>

      <ElFormItem label="开始占位符">
        <ElInput
          :model-value="localConfig.startPlaceholder || '开始日期'"
          @input="updateProperty('startPlaceholder', $event)"
          size="small"
          placeholder="开始日期"
        />
      </ElFormItem>

      <ElFormItem label="结束占位符">
        <ElInput
          :model-value="localConfig.endPlaceholder || '结束日期'"
          @input="updateProperty('endPlaceholder', $event)"
          size="small"
          placeholder="结束日期"
        />
      </ElFormItem>
    </template>

    <ElFormItem label="尺寸大小">
      <ElSelect
        :model-value="localConfig.size || 'default'"
        @change="updateProperty('size', $event)"
        size="small"
      >
        <ElOption label="大" value="large" />
        <ElOption label="默认" value="default" />
        <ElOption label="小" value="small" />
      </ElSelect>
    </ElFormItem>

    <ElFormItem label="标签宽度">
      <ElInput
        :model-value="localConfig.labelWidth || '80px'"
        @input="updateProperty('labelWidth', $event)"
        size="small"
        placeholder="例: 80px"
      />
    </ElFormItem>

    <ElFormItem label="标签位置">
      <ElSelect
        :model-value="localConfig.labelPosition || 'left'"
        @change="updateProperty('labelPosition', $event)"
        size="small"
      >
        <ElOption label="左侧" value="left" />
        <ElOption label="右侧" value="right" />
        <ElOption label="顶部" value="top" />
      </ElSelect>
    </ElFormItem>

    <ElFormItem label="可清空">
      <ElSwitch
        :model-value="localConfig.clearable !== false"
        @change="updateProperty('clearable', $event)"
      />
    </ElFormItem>

    <ElFormItem label="禁用">
      <ElSwitch
        :model-value="localConfig.disabled || false"
        @change="updateProperty('disabled', $event)"
      />
    </ElFormItem>
  </div>
</template>

<script setup>
  import { computed } from 'vue'
  import {
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElSwitch
  } from 'element-plus'

  const props = defineProps({
    datePickerConfig: {
      type: Object,
      required: true
    },
    formVariables: {
      type: Array,
      default: () => []
    }
  })

  const emit = defineEmits(['update-property'])

  const localConfig = computed({
    get: () => props.datePickerConfig,
    set: () => {
      // We don't use the setter, updates are handled via events
    }
  })

  // 获取可用的变量列表（适合日期选择器的变量类型）
  const availableVariables = computed(() => {
    const suitableTypes = ['date', 'daterange', 'string']
    return (props.formVariables || []).filter(variable =>
      suitableTypes.includes(variable.type)
    )
  })

  // 获取当前绑定的变量信息
  const selectedVariable = computed(() => {
    if (!localConfig.value.bindVariable) return null
    return availableVariables.value.find(
      variable => variable.id === localConfig.value.bindVariable
    )
  })

  const isRangeType = computed(() => {
    const rangeTypes = ['daterange', 'datetimerange', 'monthrange']
    return rangeTypes.includes(localConfig.value.type)
  })

  function updateProperty(key, value) {
    emit('update-property', `config.${key}`, value)
  }
</script>

<style scoped>
  .datepicker-panel {
    padding: 8px 0;
  }

  .el-select {
    width: 100%;
  }

  .variable-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    background: #f5f7fa;
    padding: 8px;
    border-radius: 4px;
    font-size: 12px;
  }

  .variable-info-item {
    display: flex;
    justify-content: space-between;
  }

  .info-label {
    color: #606266;
    font-weight: 500;
  }

  .info-value {
    color: #303133;
  }
</style>
