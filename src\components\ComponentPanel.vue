<template>
  <div class="component-panel">
    <div class="panel-header">
      <h3>组件库</h3>
    </div>

    <ElTabs v-model="activeTab" class="panel-tabs">
      <!-- 表单组件 -->
      <ElTabPane label="表单组件" name="forms">
        <div class="component-list">
          <div
            v-for="component in formComponents"
            :key="component.type"
            class="component-item"
            draggable="true"
            @dragstart="handleDragStart(component, $event)"
          >
            <div class="component-icon">
              <component :is="component.icon" />
            </div>
            <div class="component-info">
              <div class="component-name">{{ component.name }}</div>
              <div class="component-desc">{{ component.description }}</div>
            </div>
          </div>
        </div>
      </ElTabPane>

      <!-- 图表组件 -->
      <ElTabPane label="图表组件" name="charts">
        <div class="component-list">
          <div
            v-for="component in chartComponents"
            :key="component.type"
            class="component-item"
            draggable="true"
            @dragstart="handleDragStart(component, $event)"
          >
            <div class="component-icon">
              <component :is="component.icon" />
            </div>
            <div class="component-info">
              <div class="component-name">{{ component.name }}</div>
              <div class="component-desc">{{ component.description }}</div>
            </div>
          </div>
        </div>
      </ElTabPane>
    </ElTabs>
  </div>
</template>

<script setup>
  import { ref, markRaw } from 'vue'
  import { ElTabs, ElTabPane } from 'element-plus'
  import {
    PieChart,
    Histogram,
    TrendCharts,
    Setting,
    EditPen,
    Select,
    Calendar,
    Mouse,
    Grid
  } from '@element-plus/icons-vue'
  import { getChartTemplate } from '../utils/chartTemplates.js'

  // 定义事件
  const emit = defineEmits(['drag-start'])

  // 当前活动标签页
  const activeTab = ref('forms')

  // 图表组件配置
  const chartComponents = ref([
    {
      type: 'pie-chart',
      name: '饼图',
      description: '展示数据比例关系',
      icon: markRaw(PieChart),
      defaultConfig: {
        title: '饼图标题',
        showTitle: true,
        showLegend: true,
        showTooltip: true,
        options: getChartTemplate('pie-chart'),
        colors: {
          theme: 'default',
          customColors: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'],
          gradientMode: false,
          gradientDirection: 'vertical',
          customGradients: [
            { startColor: '#409EFF', endColor: '#67c23a' },
            { startColor: '#e6a23c', endColor: '#f56c6c' }
          ],
          titleColor: '#333333',
          xAxisColor: '#666666',
          yAxisColor: '#666666',
          gridColor: '#e0e0e0',
          legendColor: '#333333',
          backgroundColor: 'transparent',
          tooltipBg: '#ffffff',
          tooltipText: '#333333'
        },
        legendPosition: 'bottom',
        legendOrient: 'horizontal',
        pieConfig: {
          radius: '75%',
          center: ['50%', '50%']
        }
      }
    },
    {
      type: 'bar-chart',
      name: '柱状图',
      description: '展示分类数据对比',
      icon: markRaw(Histogram),
      defaultConfig: {
        title: '柱状图标题',
        showTitle: true,
        showLegend: true,
        showTooltip: true,
        options: getChartTemplate('bar-chart'),
        colors: {
          theme: 'default',
          customColors: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'],
          gradientMode: false,
          gradientDirection: 'vertical',
          customGradients: [
            { startColor: '#409EFF', endColor: '#67c23a' },
            { startColor: '#e6a23c', endColor: '#f56c6c' }
          ],
          titleColor: '#333333',
          xAxisColor: '#666666',
          yAxisColor: '#666666',
          gridColor: '#e0e0e0',
          legendColor: '#333333',
          backgroundColor: 'transparent',
          tooltipBg: '#ffffff',
          tooltipText: '#333333'
        },
        legendPosition: 'bottom',
        legendOrient: 'horizontal',
        barConfig: {
          type: 'basic',
          barWidth: 20,
          barGap: 30,
          showValue: false,
          valuePosition: 'inside',
          valueFontSize: 12,
          valueColor: '#333333'
        }
      }
    },
    {
      type: 'line-chart',
      name: '折线图',
      description: '展示数据趋势变化',
      icon: markRaw(TrendCharts),
      defaultConfig: {
        title: '折线图标题',
        showTitle: true,
        showLegend: true,
        showTooltip: true,
        options: getChartTemplate('line-chart'),
        colors: {
          theme: 'default',
          customColors: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'],
          gradientMode: false,
          gradientDirection: 'vertical',
          customGradients: [
            { startColor: '#409EFF', endColor: '#67c23a' },
            { startColor: '#e6a23c', endColor: '#f56c6c' }
          ],
          titleColor: '#333333',
          xAxisColor: '#666666',
          yAxisColor: '#666666',
          gridColor: '#e0e0e0',
          legendColor: '#333333',
          backgroundColor: 'transparent',
          tooltipBg: '#ffffff',
          tooltipText: '#333333'
        },
        legendPosition: 'bottom',
        legendOrient: 'horizontal',
        lineConfig: {
          type: 'basic'
        }
      }
    },
    {
      type: 'custom-chart',
      name: '自定义图表',
      description: '通过 Options 配置的自定义图表',
      icon: markRaw(Setting),
      defaultConfig: {
        title: '自定义图表',
        showTitle: true,
        showLegend: true,
        showTooltip: true,
        options: getChartTemplate('custom-chart'),
        customConfig: {
          dataSourceType: 'static',
          apiUrl: '',
          apiMethod: 'GET',
          polling: {
            enabled: false,
            interval: 10
          },
          customOptions: null
        },
        colors: {
          theme: 'default',
          customColors: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'],
          gradientMode: false,
          gradientDirection: 'vertical',
          customGradients: [
            { startColor: '#409EFF', endColor: '#67c23a' },
            { startColor: '#e6a23c', endColor: '#f56c6c' }
          ],
          titleColor: '#333333',
          xAxisColor: '#666666',
          yAxisColor: '#666666',
          gridColor: '#e0e0e0',
          legendColor: '#333333',
          backgroundColor: 'transparent',
          tooltipBg: '#ffffff',
          tooltipText: '#333333'
        },
        legendPosition: 'bottom',
        legendOrient: 'horizontal'
      }
    }
  ])

  // 表单组件配置
  const formComponents = ref([
    {
      type: 'form-container',
      name: '表单容器',
      description: '用于表单元素的容器',
      icon: markRaw(Grid),
      defaultSize: { width: 400, height: 300 },
      defaultConfig: {
        label: '表单容器',
        showTitle: true,
        titleText: '表单标题',
        layout: 'vertical',
        labelPosition: 'left',
        labelWidth: '80px',
        size: 'default',
        children: [],
        rows: [],
        gap: 16,
        padding: 16,
        backgroundColor: '#ffffff',
        borderColor: '#e4e7ed',
        borderWidth: 1,
        borderStyle: 'solid',
        borderRadius: 4
      }
    },
    {
      type: 'input',
      name: '输入框',
      description: '文本输入控件',
      icon: markRaw(EditPen),
      defaultSize: { width: 300, height: 40 },
      defaultConfig: {
        label: '输入框',
        placeholder: '请输入内容',
        value: '',
        required: false,
        disabled: false,
        maxlength: null,
        showWordLimit: false,
        clearable: true,
        showPassword: false,
        size: 'default',
        labelWidth: '80px',
        labelPosition: 'left'
      }
    },
    {
      type: 'select',
      name: '下拉选择',
      description: '下拉选择控件',
      icon: markRaw(Select),
      defaultSize: { width: 300, height: 40 },
      defaultConfig: {
        label: '下拉选择',
        placeholder: '请选择',
        value: '',
        options: [
          { label: '选项1', value: 'option1' },
          { label: '选项2', value: 'option2' },
          { label: '选项3', value: 'option3' }
        ],
        multiple: false,
        clearable: true,
        filterable: false,
        disabled: false,
        size: 'default',
        labelWidth: '80px',
        labelPosition: 'left'
      }
    },
    {
      type: 'datepicker',
      name: '时间控件',
      description: '日期时间选择器',
      icon: markRaw(Calendar),
      defaultSize: { width: 300, height: 40 },
      defaultConfig: {
        label: '时间控件',
        placeholder: '请选择日期',
        value: '',
        type: 'date',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        clearable: true,
        disabled: false,
        size: 'default',
        labelWidth: '80px',
        labelPosition: 'left',
        rangeSeparator: '至',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期'
      }
    },
    {
      type: 'button',
      name: '按钮',
      description: '点击触发操作',
      icon: markRaw(Mouse),
      defaultSize: { width: 120, height: 50 },
      defaultConfig: {
        text: '按钮',
        type: 'primary',
        size: 'default',
        disabled: false,
        loading: false,
        plain: false,
        round: false,
        circle: false,
        color: '',
        icon: '',
        eventBinding: {
          type: 'none',
          methodName: '',
          methodParams: '',
          eventName: '',
          eventCode: ''
        }
      }
    }
  ])

  // 处理拖拽开始
  const handleDragStart = (component, event) => {
    console.log('🚀 ComponentPanel handleDragStart:', component)

    // 设置拖拽数据
    if (event && event.dataTransfer) {
      event.dataTransfer.setData('application/json', JSON.stringify(component))
      event.dataTransfer.effectAllowed = 'copy'
    }

    emit('drag-start', component)
  }
</script>

<style scoped>
  .component-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .panel-header {
    padding: 16px;
    border-bottom: 1px solid #e4e7ed;
  }

  .panel-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }

  .panel-tabs {
    flex: 1;
    padding: 0 16px;
  }

  .component-list {
    padding: 8px 0;
  }

  .component-item {
    padding: 12px;
    margin-bottom: 8px;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    cursor: grab;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .component-item:hover {
    border-color: #409eff;
    background-color: #f0f9ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
  }

  .component-item:active {
    cursor: grabbing;
  }

  .component-icon {
    font-size: 20px;
    color: #409eff;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: #f0f9ff;
    border-radius: 4px;
  }

  .component-info {
    flex: 1;
  }

  .component-name {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 2px;
  }

  .component-desc {
    font-size: 12px;
    color: #909399;
  }
</style>
