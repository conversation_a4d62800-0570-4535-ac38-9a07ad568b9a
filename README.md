# Vue 3 + Vite 项目

这是一个使用 Vite + Vue 3 的项目，配置了 ESLint 和 Prettier 进行代码规范和格式化。

### 可用的命令

```bash
# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview

# 检查代码问题（不自动修复）
npm run lint:check

# 检查并自动修复代码问题
npm run lint

# 检查代码格式（不自动格式化）
npm run format:check

# 格式化代码
npm run format
```

# BI-Web 可视化布局系统

一个基于 Vue 3 的拖拽式 BI 可视化布局系统，支持静态图表的可视化配置和布局设计。

## 🚀 当前功能特性

### 核心功能

- **三栏布局设计**：左侧组件面板、中间画布、右侧属性面板
- **拖拽式操作**：从组件库拖拽组件到画布进行布局
- **自由定位**：支持组件的自由定位和尺寸调整
- **实时预览**：支持实时预览和保存功能
- **Schema 导出**：生成完整的 JSON Schema 配置

### 画布功能

- **画布尺寸配置**：支持预设尺寸（1920x1080、1366x768等）和自定义尺寸
- **组件操作**：选择、拖拽、调整大小、删除组件
- **右键菜单**：支持画布和组件的右键菜单操作
- **参考线功能**：拖拽时显示参考线辅助对齐
- **铺满画布**：组件可设置为铺满整个画布

### 组件类型

#### 静态图表

- **饼图 (pie-chart)**：展示数据比例关系
- **柱状图 (bar-chart)**：展示分类数据对比
- **折线图 (line-chart)**：展示数据趋势变化

#### 布局组件

- **DIV 容器 (div-container)**：可自定义样式的容器组件
  - 支持背景色、边框、圆角、内边距等样式配置
  - 支持左右拆分和上下拆分功能
  - 支持子容器的联动调整

### 属性配置功能

#### 基础属性

- 组件名称编辑
- 位置坐标设置（X、Y）
- 尺寸设置（宽度、高度）
- 铺满画布开关

#### 图表配置

- **显示控制**：标题、图例、提示框的显示/隐藏
- **图例配置**：位置（顶部、底部、左侧、右侧）和方向（水平、垂直）
- **标题设置**：自定义图表标题内容

#### 图表类型特有配置

**柱状图配置**：

- 柱状图类型（基础、堆叠、水平）
- 柱子宽度和间距
- 数值显示控制
- 坐标轴最大值/最小值设置

**折线图配置**：

- 折线类型（基础、平滑、阶梯、面积图）
- 数据点符号显示控制
- 线条宽度设置

**饼图配置**：

- 饼图半径设置
- 饼图中心位置

#### 主题颜色配置

- **颜色主题**：默认、蓝色、绿色、红色、紫色、橙色主题
- **自定义颜色**：支持自定义颜色组合
- **样式颜色**：
  - 标题颜色
  - 图例文字颜色
  - 图表背景色（支持透明度）
  - X轴/Y轴颜色
  - 网格线颜色
  - 提示框背景和文字颜色

#### DIV容器配置

- 背景颜色设置
- 边框样式（颜色、宽度、样式、圆角）
- 内边距设置
- 文本内容编辑
- 容器拆分功能（左右拆分、上下拆分）

### 工具栏功能

- **预览**：在新窗口中预览设计结果
- **保存**：将配置保存到本地存储
- **查看Schema**：查看和复制生成的JSON配置
- **画布尺寸选择**：快速选择常用尺寸或自定义尺寸

## 🛠️ 技术栈

- **前端框架**：Vue 3 + Composition API
- **构建工具**：Vite
- **UI 组件库**：Element Plus
- **图表库**：ECharts
- **状态管理**：Pinia
- **工具库**：@vueuse/core, lodash-es

## 📦 安装和运行

### 环境要求

- Node.js >= 16.0.0
- npm >= 7.0.0

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 📖 使用指南

### 基本操作

1. **添加组件**
   - 从左侧组件面板选择所需组件
   - 拖拽到中间画布区域
   - 松开鼠标完成添加

2. **调整组件**
   - 点击组件进行选择
   - 拖拽组件移动位置
   - 拖拽组件边角调整尺寸
   - 点击删除按钮移除组件

3. **配置属性**
   - 选择组件后在右侧属性面板进行配置
   - 基础属性：调整位置和尺寸
   - 配置属性：设置图表样式和显示选项
   - 主题颜色：自定义图表颜色方案

4. **画布设置**
   - 在顶部工具栏选择画布尺寸
   - 支持常用尺寸快速选择
   - 支持自定义宽高和单位

5. **预览和保存**
   - 点击"预览"按钮在新窗口查看效果
   - 点击"保存"按钮将配置保存到本地
   - 点击"查看Schema"查看生成的JSON配置

### 静态图表配置

#### 图表数据编辑

- 在属性面板中配置图表的显示选项
- 通过颜色主题快速改变图表配色
- 使用自定义颜色精确控制每个颜色

#### 支持的图表配置

- 完整的显示控制（标题、图例、提示框）
- 图例位置和方向自定义
- 丰富的颜色主题和自定义颜色
- 图表类型特有的高级配置

### DIV容器使用

#### 基础容器

```javascript
{
  "type": "div-container",
  "config": {
    "backgroundColor": "#ffffff",
    "borderColor": "#e4e7ed",
    "borderWidth": 1,
    "borderStyle": "solid",
    "borderRadius": 4,
    "padding": 16,
    "content": "容器内容"
  }
}
```

#### 拆分容器

- 右键点击DIV容器选择拆分方向
- 支持左右拆分和上下拆分
- 拆分后的子容器支持独立调整
- 子容器间自动保持布局关系

## 🔧 Schema 格式

系统生成的 Schema 格式如下：

```javascript
{
  "canvas": {
    "width": 1920,
    "height": 1080
  },
  "components": [
    {
      "id": "comp_xxxxxxxxx",
      "type": "pie-chart",
      "name": "饼图",
      "position": { "x": 100, "y": 100 },
      "size": { "width": 400, "height": 300 },
      "config": {
        "title": "访问来源",
        "showTitle": true,
        "showLegend": true,
        "showTooltip": true,
        "legendPosition": "bottom",
        "legendOrient": "horizontal",
        "colors": {
          "theme": "default",
          "titleColor": "#333333",
          "legendColor": "#333333",
          "backgroundColor": "transparent"
        },
        "pieConfig": {
          "radius": 60,
          "center": ["50%", "50%"]
        }
      }
    }
  ]
}
```

## 🎨 自定义开发

### 添加新的组件类型

1. **在组件面板中定义组件**（`ComponentPanel.vue`）

```javascript
{
  type: 'custom-chart',
  name: '自定义图表',
  description: '自定义图表描述',
  icon: CustomIcon,
  defaultConfig: { /* 默认配置 */ }
}
```

2. **在渲染器中实现组件**（`RenderComponent.vue`）

```vue
<div v-else-if="component.type === 'custom-chart'">
  <!-- 自定义组件实现 -->
</div>
```

3. **在属性面板中添加配置项**（`PropertyPanel.vue`）

```vue
<template v-if="selectedComponent.type === 'custom-chart'">
  <!-- 配置表单 -->
</template>
```

### 自定义样式

系统使用 CSS 变量，可以通过覆盖变量来自定义样式：

```css
:root {
  --primary-color: #409eff;
  --border-color: #e4e7ed;
  --background-color: #f5f5f5;
}
```

## 🐛 常见问题

### 1. 组件无法拖拽

- 检查浏览器是否支持 HTML5 拖拽 API
- 确保组件面板中的组件设置了 `draggable="true"`

### 2. 图表不显示

- 检查组件尺寸是否大于最小值
- 确保图表数据格式正确
- 查看浏览器控制台是否有错误信息

### 3. 属性修改不生效

- 确保已选中组件
- 检查属性配置是否正确
- 尝试重新选择组件

## 📝 开发计划

### 近期计划

- [ ] 完善API组件功能（api-table、api-chart、api-indicator）
- [ ] 添加更多图表类型（散点图、雷达图、仪表盘等）
- [ ] 支持图表数据的直接编辑
- [ ] 添加组件模板和预设布局

### 长期计划

- [ ] 支持组件分组和图层管理
- [ ] 添加撤销/重做功能
- [ ] 支持组件复制和粘贴
- [ ] 添加网格对齐和智能对齐功能
- [ ] 支持响应式布局设计
- [ ] 添加动画和交互效果

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request
