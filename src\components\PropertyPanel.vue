<template>
  <div
    v-if="
      selectedComponent ||
      selectedFormChild ||
      selectedFormColumn ||
      selectedFormRow
    "
    class="property-panel"
  >
    <div class="panel-header">
      <h3>属性设置</h3>
      <span v-if="selectedFormChild" class="form-child-indicator"
        >表单元素</span
      >
      <span v-if="selectedFormColumn" class="form-column-indicator"
        >表单列</span
      >
      <span v-if="selectedFormRow" class="form-row-indicator">表单行</span>
    </div>

    <div
      v-if="
        !selectedComponent &&
        !selectedFormChild &&
        !selectedFormColumn &&
        !selectedFormRow
      "
      class="no-selection"
    >
      <div class="no-selection-content">
        <ElIcon size="48"><Select /></ElIcon>
        <p>请选择一个组件来编辑属性</p>
      </div>
    </div>

    <!-- 表单行编辑模式 -->
    <div v-else-if="selectedFormRow" class="property-content">
      <ElForm label-position="left" label-width="80px" class="config-form">
        <FormRowPanel
          :form-row-config="selectedFormRow"
          @update-property="updateFormRowProperty"
        />
      </ElForm>
    </div>

    <!-- 表单列编辑模式 -->
    <div v-else-if="selectedFormColumn" class="property-content">
      <ElForm label-position="left" label-width="80px" class="config-form">
        <!-- 列背景色 -->
        <ElFormItem label="背景色">
          <ElColorPicker
            :model-value="
              selectedFormColumn.column.backgroundColor || '#ffffff'
            "
            @change="updateFormColumnProperty('backgroundColor', $event)"
            show-alpha
          />
        </ElFormItem>

        <!-- 列边框配置 -->
        <ElFormItem label="边框宽度">
          <ElInputNumber
            :model-value="selectedFormColumn.column.borderWidth || 1"
            @change="updateFormColumnProperty('borderWidth', $event)"
            :min="0"
            :max="10"
            size="small"
          />
        </ElFormItem>

        <ElFormItem label="边框颜色">
          <ElColorPicker
            :model-value="selectedFormColumn.column.borderColor || '#e4e7ed'"
            @change="updateFormColumnProperty('borderColor', $event)"
          />
        </ElFormItem>

        <ElFormItem label="边框样式">
          <ElSelect
            :model-value="selectedFormColumn.column.borderStyle || 'dashed'"
            @change="updateFormColumnProperty('borderStyle', $event)"
            size="small"
          >
            <ElOption label="实线" value="solid" />
            <ElOption label="虚线" value="dashed" />
            <ElOption label="点线" value="dotted" />
            <ElOption label="无边框" value="none" />
          </ElSelect>
        </ElFormItem>

        <!-- 列内边距 -->
        <ElFormItem label="内边距">
          <ElInputNumber
            :model-value="selectedFormColumn.column.padding || 8"
            @change="updateFormColumnProperty('padding', $event)"
            :min="0"
            :max="50"
            size="small"
          />
          <span class="property-hint">像素</span>
        </ElFormItem>
      </ElForm>
    </div>

    <!-- 表单子组件编辑模式 -->
    <div v-else-if="selectedFormChild" class="property-content">
      <ElForm label-position="left" label-width="80px" class="config-form">
        <!-- 输入框配置 -->
        <InputPanel
          v-if="selectedFormChild.type === 'input'"
          :input-config="selectedFormChild.config"
          :form-variables="formContainerVariables"
          @update-property="updateFormChildProperty"
        />

        <!-- 下拉选择配置 -->
        <SelectPanel
          v-if="selectedFormChild.type === 'select'"
          :select-config="selectedFormChild.config"
          :form-variables="formContainerVariables"
          @update-property="updateFormChildProperty"
        />

        <!-- 时间控件配置 -->
        <DatePickerPanel
          v-if="selectedFormChild.type === 'datepicker'"
          :date-picker-config="selectedFormChild.config"
          :form-variables="formContainerVariables"
          @update-property="updateFormChildProperty"
        />

        <!-- 按钮配置 -->
        <ButtonPanel
          v-if="selectedFormChild.type === 'button'"
          :button-config="selectedFormChild.config"
          :form-variables="formContainerVariables"
          :available-methods="globalMethodOptions"
          @update-property="updateFormChildProperty"
        />
      </ElForm>
    </div>

    <!-- 普通组件编辑模式 -->
    <div v-else class="property-content">
      <!-- 基础属性 -->
      <ElCollapse v-model="activeCollapse">
        <ElCollapseItem title="基础属性" name="basic">
          <div class="property-group">
            <!-- 组件ID -->
            <div class="property-item basic-property-item">
              <label class="property-label">组件ID</label>
              <ElInput
                :model-value="selectedComponent.id"
                @input="handleIdUpdate"
                @blur="validateComponentId"
                size="small"
                class="basic-input"
                :class="{ 'id-error': componentIdError }"
                placeholder="输入唯一的组件ID"
              />
              <div v-if="componentIdError" class="error-message">
                {{ componentIdError }}
              </div>
            </div>

            <!-- 组件名称 -->
            <div class="property-item basic-property-item">
              <label class="property-label">组件名称</label>
              <ElInput
                :model-value="selectedComponent.name"
                @input="updateProperty('name', $event)"
                size="small"
                class="basic-input"
              />
            </div>

            <!-- X坐标 -->
            <div
              v-if="!selectedComponent.config?.isChildContainer"
              class="property-item basic-property-item"
            >
              <label class="property-label">X坐标</label>
              <ElInputNumber
                :model-value="selectedComponent.position.x"
                @change="updateProperty('position.x', $event)"
                :min="0"
                size="small"
                controls-position="right"
                :disabled="selectedComponent.fullCanvas"
                class="basic-input-number"
              />
            </div>

            <!-- Y坐标 -->
            <div
              v-if="!selectedComponent.config?.isChildContainer"
              class="property-item basic-property-item"
            >
              <label class="property-label">Y坐标</label>
              <ElInputNumber
                :model-value="selectedComponent.position.y"
                @change="updateProperty('position.y', $event)"
                :min="0"
                size="small"
                controls-position="right"
                :disabled="selectedComponent.fullCanvas"
                class="basic-input-number"
              />
            </div>

            <!-- 宽度 -->
            <div class="property-item basic-property-item">
              <label class="property-label">
                宽度 (px)
                <span
                  v-if="selectedComponent.fullCanvas"
                  class="canvas-size-hint"
                  >画布宽度</span
                >
                <span
                  v-if="
                    selectedComponent.type === 'div-container' &&
                    Math.abs(displayWidth - canvasSize.width) > 1
                  "
                  class="size-mismatch-hint"
                  title="当前尺寸与画布宽度不匹配，点击修正按钮可调整为画布宽度"
                  >⚠️</span
                >
              </label>
              <div class="input-with-button">
                <ElInputNumber
                  :model-value="displayWidth"
                  @change="updateProperty('size.width', $event)"
                  :min="100"
                  size="small"
                  controls-position="right"
                  :disabled="selectedComponent.fullCanvas"
                  class="basic-input-number"
                />
                <ElButton
                  v-if="
                    selectedComponent.type === 'div-container' &&
                    Math.abs(displayWidth - canvasSize.width) > 1
                  "
                  size="small"
                  type="primary"
                  @click="fixDivContainerWidth"
                  title="修正为画布宽度"
                  class="fix-button"
                >
                  ✓
                </ElButton>
              </div>
            </div>

            <!-- 高度 -->
            <div class="property-item basic-property-item">
              <label class="property-label">
                高度 (px)
                <span
                  v-if="selectedComponent.fullCanvas"
                  class="canvas-size-hint"
                  >画布高度</span
                >
              </label>
              <ElInputNumber
                :model-value="displayHeight"
                @change="updateProperty('size.height', $event)"
                :min="100"
                size="small"
                controls-position="right"
                :disabled="selectedComponent.fullCanvas"
                class="basic-input-number"
              />
            </div>

            <!-- 铺满画布 -->
            <div class="property-item">
              <label class="property-label">铺满画布</label>
              <ElSwitch
                :model-value="selectedComponent.fullCanvas"
                @change="handleFullCanvas"
              />
            </div>
          </div>
        </ElCollapseItem>

        <!-- 配置属性 -->
        <ElCollapseItem title="配置属性" name="config">
          <!-- 自定义图表配置 -->
          <ElForm
            v-if="isCustomChart"
            label-position="left"
            label-width="80px"
            class="config-form"
          >
            <!-- 自定义图表特有配置 -->
            <custom-chart-panel
              :custom-config="selectedComponent.config.customConfig || {}"
              :component-config="selectedComponent.config"
              :variables="props.globalVariables"
              @update-property="updateProperty"
              @open-options-editor="openOptionsEditor"
              @open-data-editor="openCustomChartDataEditor"
            />
          </ElForm>

          <!-- 静态图表配置 -->
          <ElForm
            v-else-if="isStaticComponent"
            label-position="left"
            label-width="80px"
            class="config-form"
          >
            <!-- 显示标题 -->
            <ElFormItem label="显示标题">
              <ElSwitch
                :model-value="selectedComponent.config.showTitle !== false"
                @change="updateProperty('config.showTitle', $event)"
              />
            </ElFormItem>

            <!-- 标题内容 -->
            <ElFormItem
              label="标题内容"
              v-if="selectedComponent.config.showTitle !== false"
            >
              <ElInput
                :model-value="selectedComponent.config.title"
                @input="updateProperty('config.title', $event)"
                size="small"
                placeholder="请输入图表标题"
              />
            </ElFormItem>

            <!-- 显示选项 -->
            <ElFormItem label="显示图例">
              <ElSwitch
                :model-value="selectedComponent.config.showLegend !== false"
                @change="updateProperty('config.showLegend', $event)"
              />
            </ElFormItem>

            <!-- 图例位置 -->
            <ElFormItem
              label="图例位置"
              v-if="selectedComponent.config.showLegend !== false"
            >
              <ElSelect
                :model-value="
                  selectedComponent.config.legendPosition || 'bottom'
                "
                @change="updateProperty('config.legendPosition', $event)"
                size="small"
              >
                <ElOption label="顶部" value="top" />
                <ElOption label="底部" value="bottom" />
                <ElOption label="左侧" value="left" />
                <ElOption label="右侧" value="right" />
              </ElSelect>
            </ElFormItem>

            <!-- 图例方向 -->
            <ElFormItem
              label="图例方向"
              v-if="selectedComponent.config.showLegend !== false"
            >
              <ElSelect
                :model-value="
                  selectedComponent.config.legendOrient || 'horizontal'
                "
                @change="updateProperty('config.legendOrient', $event)"
                size="small"
              >
                <ElOption label="水平" value="horizontal" />
                <ElOption label="垂直" value="vertical" />
              </ElSelect>
            </ElFormItem>

            <ElFormItem label="显示提示">
              <ElSwitch
                :model-value="selectedComponent.config.showTooltip !== false"
                @change="updateProperty('config.showTooltip', $event)"
              />
            </ElFormItem>

            <!-- 柱状图特有配置 -->
            <bar-chart-panel
              v-if="selectedComponent.type === 'bar-chart'"
              :bar-config="selectedComponent.config.barConfig"
              :component-config="selectedComponent.config"
              :variables="props.globalVariables"
              @update-property="updateProperty"
              @update:type="handleChartTypeChange('bar-chart', $event)"
            />

            <!-- 折线图特有配置 -->
            <line-chart-panel
              v-if="selectedComponent.type === 'line-chart'"
              :line-config="selectedComponent.config.lineConfig"
              :component-config="selectedComponent.config"
              :variables="props.globalVariables"
              @update-property="updateProperty"
              @update:type="handleChartTypeChange('line-chart', $event)"
            />

            <!-- 饼图特有配置 -->
            <pie-chart-panel
              v-if="selectedComponent.type === 'pie-chart'"
              :pie-config="selectedComponent.config.pieConfig || {}"
              :component-config="selectedComponent.config"
              :variables="props.globalVariables"
              @update-property="updateProperty"
            />
          </ElForm>

          <!-- API组件配置 -->
          <div v-else-if="isApiComponent" class="property-group">
            <!-- 显示标题 -->
            <div class="property-item">
              <label class="property-label">显示标题</label>
              <ElSwitch
                :model-value="selectedComponent.config.showTitle !== false"
                @change="updateProperty('config.showTitle', $event)"
              />
            </div>

            <!-- 标题内容 -->
            <div
              class="property-item"
              v-if="selectedComponent.config.showTitle !== false"
            >
              <label class="property-label">标题内容</label>
              <ElInput
                :model-value="selectedComponent.config.title"
                @input="updateProperty('config.title', $event)"
                size="small"
                placeholder="请输入图表标题"
              />
            </div>

            <!-- 显示图例 -->
            <div class="property-item">
              <label class="property-label">显示图例</label>
              <ElSwitch
                :model-value="selectedComponent.config.showLegend !== false"
                @change="updateProperty('config.showLegend', $event)"
              />
            </div>

            <!-- 图例位置 -->
            <div
              class="property-item"
              v-if="selectedComponent.config.showLegend !== false"
            >
              <label class="property-label">图例位置</label>
              <ElSelect
                :model-value="
                  selectedComponent.config.legendPosition || 'bottom'
                "
                @change="updateProperty('config.legendPosition', $event)"
                size="small"
              >
                <ElOption label="顶部" value="top" />
                <ElOption label="底部" value="bottom" />
                <ElOption label="左侧" value="left" />
                <ElOption label="右侧" value="right" />
              </ElSelect>
            </div>

            <!-- 图例方向 -->
            <div
              class="property-item"
              v-if="selectedComponent.config.showLegend !== false"
            >
              <label class="property-label">图例方向</label>
              <ElSelect
                :model-value="
                  selectedComponent.config.legendOrient || 'horizontal'
                "
                @change="updateProperty('config.legendOrient', $event)"
                size="small"
              >
                <ElOption label="水平" value="horizontal" />
                <ElOption label="垂直" value="vertical" />
              </ElSelect>
            </div>

            <!-- 显示提示框 -->
            <div class="property-item">
              <label class="property-label">显示提示</label>
              <ElSwitch
                :model-value="selectedComponent.config.showTooltip !== false"
                @change="updateProperty('config.showTooltip', $event)"
              />
            </div>

            <div class="property-item">
              <label class="property-label">API 地址</label>
              <ElButton type="primary" size="small" @click="openApiEditor">
                配置 API
              </ElButton>
            </div>
            <div class="property-item">
              <label class="property-label">数据轮询</label>
              <ElSwitch
                :model-value="selectedComponent.config.polling.enabled"
                @change="updateProperty('config.polling.enabled', $event)"
              />
            </div>
            <div
              v-if="selectedComponent.config.polling.enabled"
              class="property-item"
            >
              <label class="property-label">轮询间隔(秒)</label>
              <ElInputNumber
                :model-value="selectedComponent.config.polling.interval"
                @change="updateProperty('config.polling.interval', $event)"
                :min="1"
                size="small"
              />
            </div>
          </div>

          <!-- DIV容器配置 -->
          <div v-else-if="isDivContainer">
            <DivContainerPanel
              :selected-component="selectedComponent"
              @update-property="updateProperty"
            />
          </div>

          <!-- 表单组件配置 -->
          <ElForm
            v-else-if="isFormComponent"
            label-position="left"
            label-width="80px"
            class="config-form"
          >
            <!-- 输入框配置 -->
            <InputPanel
              v-if="isInputComponent"
              :input-config="selectedComponent.config"
              @update-property="updateProperty"
            />

            <!-- 下拉选择配置 -->
            <SelectPanel
              v-if="isSelectComponent"
              :select-config="selectedComponent.config"
              @update-property="updateProperty"
            />

            <!-- 时间控件配置 -->
            <DatePickerPanel
              v-if="isDatePickerComponent"
              :date-picker-config="selectedComponent.config"
              @update-property="updateProperty"
            />

            <!-- 按钮配置 -->
            <ButtonPanel
              v-if="isButtonComponent"
              :button-config="selectedComponent.config"
              :form-variables="[]"
              :available-methods="globalMethodOptions"
              @update-property="updateProperty"
            />
          </ElForm>

          <!-- 表单容器配置 -->
          <ElForm
            v-else-if="isFormContainerComponent"
            label-position="left"
            label-width="80px"
            class="config-form"
          >
            <FormContainerPanel
              :form-container-config="selectedComponent.config"
              @update-property="updateProperty"
              @update-children-order="handleUpdateChildrenOrder"
            />
          </ElForm>

          <!-- 主题颜色配置 -->
          <ElCollapseItem
            title="主题颜色配置"
            name="theme-colors"
            v-if="isChartComponent && !isCustomChart"
          >
            <div class="property-group">
              <!-- 图表颜色主题 -->
              <div class="property-item">
                <label class="property-label">图表颜色主题</label>
                <ElSelect
                  :model-value="
                    selectedComponent.config.colors?.theme || 'default'
                  "
                  @change="updateProperty('config.colors.theme', $event)"
                  size="small"
                >
                  <ElOption label="默认" value="default" />
                  <ElOption label="蓝色主题" value="blue" />
                  <ElOption label="绿色主题" value="green" />
                  <ElOption label="红色主题" value="red" />
                  <ElOption label="紫色主题" value="purple" />
                  <ElOption label="橙色主题" value="orange" />
                  <ElOption label="海洋渐变" value="gradient_ocean" />
                  <ElOption label="日落渐变" value="gradient_sunset" />
                  <ElOption label="森林渐变" value="gradient_forest" />
                  <ElOption label="科技渐变" value="gradient_tech" />
                  <ElOption label="自定义" value="custom" />
                </ElSelect>
              </div>

              <!-- 自定义颜色配置 -->
              <template
                v-if="selectedComponent.config.colors?.theme === 'custom'"
              >
                <!-- 渐变色模式开关 -->
                <div class="property-item">
                  <label class="property-label">渐变色模式</label>
                  <ElSwitch
                    :model-value="
                      selectedComponent.config.colors?.gradientMode || false
                    "
                    @change="
                      updateProperty('config.colors.gradientMode', $event)
                    "
                  />
                </div>

                <!-- 纯色模式 -->
                <template v-if="!selectedComponent.config.colors?.gradientMode">
                  <div class="property-item">
                    <label class="property-label">自定义颜色</label>
                    <div class="custom-colors-container">
                      <div
                        v-for="(color, index) in customColors"
                        :key="index"
                        class="custom-color-item"
                      >
                        <ElColorPicker
                          :model-value="color"
                          @change="updateCustomColor(index, $event)"
                          size="small"
                          :predefine="predefineColors"
                        />
                        <ElButton
                          type="danger"
                          size="small"
                          @click="removeCustomColor(index)"
                          v-if="customColors.length > 1"
                        >
                          删除
                        </ElButton>
                      </div>
                      <ElButton
                        type="dashed"
                        size="small"
                        @click="addCustomColor"
                        class="add-color-btn"
                      >
                        添加颜色
                      </ElButton>
                    </div>
                  </div>
                </template>

                <!-- 渐变色模式 -->
                <template v-if="selectedComponent.config.colors?.gradientMode">
                  <!-- 渐变方向 -->
                  <div class="property-item">
                    <label class="property-label">渐变方向</label>
                    <ElSelect
                      :model-value="
                        selectedComponent.config.colors?.gradientDirection ||
                        'vertical'
                      "
                      @change="
                        updateProperty(
                          'config.colors.gradientDirection',
                          $event
                        )
                      "
                      size="small"
                    >
                      <ElOption label="垂直渐变" value="vertical" />
                      <ElOption label="水平渐变" value="horizontal" />
                      <ElOption label="对角线↘" value="diagonal1" />
                      <ElOption label="对角线↗" value="diagonal2" />
                      <ElOption label="径向渐变" value="radial" />
                    </ElSelect>
                  </div>

                  <!-- 自定义渐变色配置 -->
                  <div class="property-item gradient-config-section">
                    <label class="property-label">渐变色配置</label>
                    <div class="custom-gradients-container">
                      <div
                        v-for="(gradient, index) in customGradients"
                        :key="index"
                        class="gradient-item-card"
                      >
                        <div class="gradient-header">
                          <span class="gradient-index"
                            >渐变 {{ index + 1 }}</span
                          >
                          <ElButton
                            type="link"
                            size="small"
                            @click="removeCustomGradient(index)"
                            v-if="customGradients.length > 1"
                            class="remove-gradient-btn"
                          >
                            <ElIcon><Close /></ElIcon>
                          </ElButton>
                        </div>
                        <div class="gradient-colors-row">
                          <div class="gradient-color-picker">
                            <div class="color-picker-label">起始色</div>
                            <ElColorPicker
                              :model-value="gradient.startColor"
                              @change="
                                updateCustomGradientColor(
                                  index,
                                  'startColor',
                                  $event
                                )
                              "
                              size="small"
                              :predefine="predefineColors"
                              show-alpha
                            />
                            <div class="color-value">
                              {{ gradient.startColor }}
                            </div>
                          </div>
                          <div class="gradient-arrow">→</div>
                          <div class="gradient-color-picker">
                            <div class="color-picker-label">结束色</div>
                            <ElColorPicker
                              :model-value="gradient.endColor"
                              @change="
                                updateCustomGradientColor(
                                  index,
                                  'endColor',
                                  $event
                                )
                              "
                              size="small"
                              :predefine="predefineColors"
                              show-alpha
                            />
                            <div class="color-value">
                              {{ gradient.endColor }}
                            </div>
                          </div>
                        </div>
                        <!-- 渐变预览条 -->
                        <div
                          class="gradient-preview"
                          :style="
                            getGradientPreviewStyle(
                              gradient,
                              selectedComponent.config.colors
                                ?.gradientDirection || 'vertical'
                            )
                          "
                        ></div>
                      </div>
                      <ElButton
                        type="dashed"
                        size="small"
                        @click="addCustomGradient"
                        class="add-gradient-btn"
                      >
                        <ElIcon><Plus /></ElIcon>
                        添加新渐变
                      </ElButton>
                    </div>
                  </div>
                </template>
              </template>

              <!-- 标题颜色 -->
              <div class="property-item">
                <label class="property-label">标题颜色</label>
                <div class="color-input-group">
                  <ElColorPicker
                    :model-value="
                      selectedComponent.config.colors?.titleColor || '#333333'
                    "
                    @change="updateProperty('config.colors.titleColor', $event)"
                    size="small"
                    :predefine="predefineColors"
                  />
                  <ElInput
                    :model-value="
                      selectedComponent.config.colors?.titleColor || '#333333'
                    "
                    @input="updateProperty('config.colors.titleColor', $event)"
                    size="small"
                    class="color-input"
                    placeholder="#333333"
                  />
                </div>
              </div>

              <!-- 图例文字颜色 -->
              <div class="property-item">
                <label class="property-label">图例文字颜色</label>
                <div class="color-input-group">
                  <ElColorPicker
                    :model-value="
                      selectedComponent.config.colors?.legendColor || '#333333'
                    "
                    @change="
                      updateProperty('config.colors.legendColor', $event)
                    "
                    size="small"
                    :predefine="predefineColors"
                  />
                  <ElInput
                    :model-value="
                      selectedComponent.config.colors?.legendColor || '#333333'
                    "
                    @input="updateProperty('config.colors.legendColor', $event)"
                    size="small"
                    class="color-input"
                    placeholder="#333333"
                  />
                </div>
              </div>

              <!-- 图表背景色 -->
              <div class="property-item">
                <label class="property-label">图表背景色</label>
                <div class="color-input-group">
                  <ElColorPicker
                    :model-value="
                      selectedComponent.config.colors?.backgroundColor ||
                      'transparent'
                    "
                    @change="
                      updateProperty('config.colors.backgroundColor', $event)
                    "
                    size="small"
                    :predefine="predefineColors"
                    show-alpha
                  />
                  <ElInput
                    :model-value="
                      selectedComponent.config.colors?.backgroundColor ||
                      'transparent'
                    "
                    @input="
                      updateProperty('config.colors.backgroundColor', $event)
                    "
                    size="small"
                    class="color-input"
                    placeholder="transparent"
                  />
                </div>
              </div>

              <!-- 坐标轴颜色 -->
              <template v-if="!isPieChart">
                <div class="property-item">
                  <label class="property-label">X轴颜色</label>
                  <div class="color-input-group">
                    <ElColorPicker
                      :model-value="
                        selectedComponent.config.colors?.xAxisColor || '#666666'
                      "
                      @change="
                        updateProperty('config.colors.xAxisColor', $event)
                      "
                      size="small"
                      :predefine="predefineColors"
                    />
                    <ElInput
                      :model-value="
                        selectedComponent.config.colors?.xAxisColor || '#666666'
                      "
                      @input="
                        updateProperty('config.colors.xAxisColor', $event)
                      "
                      size="small"
                      class="color-input"
                      placeholder="#666666"
                    />
                  </div>
                </div>

                <div class="property-item">
                  <label class="property-label">Y轴颜色</label>
                  <div class="color-input-group">
                    <ElColorPicker
                      :model-value="
                        selectedComponent.config.colors?.yAxisColor || '#666666'
                      "
                      @change="
                        updateProperty('config.colors.yAxisColor', $event)
                      "
                      size="small"
                      :predefine="predefineColors"
                    />
                    <ElInput
                      :model-value="
                        selectedComponent.config.colors?.yAxisColor || '#666666'
                      "
                      @input="
                        updateProperty('config.colors.yAxisColor', $event)
                      "
                      size="small"
                      class="color-input"
                      placeholder="#666666"
                    />
                  </div>
                </div>

                <div class="property-item">
                  <label class="property-label">网格线颜色</label>
                  <div class="color-input-group">
                    <ElColorPicker
                      :model-value="
                        selectedComponent.config.colors?.gridColor || '#e0e0e0'
                      "
                      @change="
                        updateProperty('config.colors.gridColor', $event)
                      "
                      size="small"
                      :predefine="predefineColors"
                    />
                    <ElInput
                      :model-value="
                        selectedComponent.config.colors?.gridColor || '#e0e0e0'
                      "
                      @input="updateProperty('config.colors.gridColor', $event)"
                      size="small"
                      class="color-input"
                      placeholder="#e0e0e0"
                    />
                  </div>
                </div>
              </template>

              <!-- 提示框颜色 -->
              <div class="property-item">
                <label class="property-label">提示框背景色</label>
                <div class="color-input-group">
                  <ElColorPicker
                    :model-value="
                      selectedComponent.config.colors?.tooltipBg || '#ffffff'
                    "
                    @change="updateProperty('config.colors.tooltipBg', $event)"
                    size="small"
                    :predefine="predefineColors"
                    show-alpha
                  />
                  <ElInput
                    :model-value="
                      selectedComponent.config.colors?.tooltipBg || '#ffffff'
                    "
                    @input="updateProperty('config.colors.tooltipBg', $event)"
                    size="small"
                    class="color-input"
                    placeholder="#ffffff"
                  />
                </div>
              </div>

              <div class="property-item">
                <label class="property-label">提示框文字颜色</label>
                <div class="color-input-group">
                  <ElColorPicker
                    :model-value="
                      selectedComponent.config.colors?.tooltipText || '#333333'
                    "
                    @change="
                      updateProperty('config.colors.tooltipText', $event)
                    "
                    size="small"
                    :predefine="predefineColors"
                  />
                  <ElInput
                    :model-value="
                      selectedComponent.config.colors?.tooltipText || '#333333'
                    "
                    @input="updateProperty('config.colors.tooltipText', $event)"
                    size="small"
                    class="color-input"
                    placeholder="#333333"
                  />
                </div>
              </div>
            </div>
          </ElCollapseItem>
        </ElCollapseItem>

        <!-- 数据源 -->
        <ElCollapseItem
          title="数据源"
          name="data"
          v-if="!isCustomChart && !isFormContainerComponent"
        >
          <div class="property-group">
            <div class="property-item">
              <label class="property-label">数据配置</label>
              <ElButton type="primary" size="small" @click="openDataEditor">
                编辑数据
              </ElButton>
            </div>
          </div>
        </ElCollapseItem>
      </ElCollapse>
    </div>

    <!-- 数据编辑弹窗 -->
    <DraggableDialog
      v-model:visible="dataEditorVisible"
      :title="
        selectedComponent?.type === 'custom-chart'
          ? '编辑自定义图表数据'
          : '编辑图表数据'
      "
      width="80%"
      :close-on-click-outside="false"
    >
      <div class="data-editor">
        <!-- 表格编辑器 -->
        <div class="spreadsheet-container">
          <table class="spreadsheet-table">
            <!-- 表头 -->
            <thead>
              <tr>
                <th class="row-header"></th>
                <th
                  v-for="(col, colIndex) in tableColumns"
                  :key="colIndex"
                  class="col-header"
                >
                  {{ getColumnLabel(colIndex) }}
                </th>
                <th
                  v-if="!isPieChart"
                  class="col-header add-col-btn"
                  @click="addTableColumn"
                >
                  +
                </th>
              </tr>
            </thead>
            <!-- 表格内容 -->
            <tbody>
              <tr v-for="(row, rowIndex) in tableData" :key="rowIndex">
                <td class="row-header">{{ rowIndex + 1 }}</td>
                <td
                  v-for="(col, colIndex) in tableColumns"
                  :key="colIndex"
                  class="data-cell"
                >
                  <input
                    v-model="tableData[rowIndex][colIndex]"
                    class="cell-input"
                    @blur="updateTableData"
                  />
                </td>
                <td class="action-cell">
                  <ElButton
                    type="danger"
                    size="small"
                    @click="removeTableRow(rowIndex)"
                    v-if="tableData.length > 1"
                  >
                    删除
                  </ElButton>
                </td>
              </tr>
            </tbody>
          </table>

          <!-- 添加行按钮 -->
          <div class="table-actions">
            <ElButton type="dashed" size="small" @click="addTableRow">
              添加行
            </ElButton>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="chart-actions-section">
          <ElButton type="link" size="small" @click="clearTableData">
            清空数据
          </ElButton>
        </div>
      </div>

      <template #footer>
        <ElButton @click="dataEditorVisible = false">取消</ElButton>
        <ElButton type="primary" @click="saveDataEditor">确认</ElButton>
      </template>
    </DraggableDialog>

    <!-- 列编辑弹窗 -->
    <DraggableDialog
      v-model:visible="showColumnEditor"
      title="编辑表格列"
      width="50%"
      :close-on-click-outside="false"
    >
      <div class="column-editor">
        <div
          v-for="(column, index) in editingColumns"
          :key="index"
          class="column-item"
        >
          <ElInput v-model="column.key" placeholder="字段名" size="small" />
          <ElInput v-model="column.label" placeholder="显示名" size="small" />
          <ElInputNumber
            v-model="column.width"
            placeholder="宽度"
            size="small"
            :min="50"
          />
          <ElButton type="danger" size="small" @click="removeColumn(index)">
            删除
          </ElButton>
        </div>
        <ElButton type="dashed" @click="addColumn" class="add-column-btn">
          添加列
        </ElButton>
      </div>
      <template #footer>
        <ElButton @click="showColumnEditor = false">取消</ElButton>
        <ElButton type="primary" @click="saveColumnEditor">保存</ElButton>
      </template>
    </DraggableDialog>

    <!-- Options 编辑器 -->
    <OptionsEditor
      v-model="optionsEditorVisible"
      :options="currentOptionsForEditor"
      :chart-type="selectedComponent?.type || 'custom-chart'"
      @save="handleOptionsSave"
    />
  </div>
</template>

<script setup>
  import {
    ElIcon,
    ElInput,
    ElInputNumber,
    ElSwitch,
    ElCollapse,
    ElCollapseItem,
    ElSelect,
    ElOption,
    ElColorPicker,
    ElButton,
    ElMessage
  } from 'element-plus'
  import { Select, Close, Plus } from '@element-plus/icons-vue'
  import { ref, computed, watch } from 'vue'
  import {
    getChartTemplate,
    applyColorConfig
  } from '../utils/chartTemplates.js'
  import BarChartPanel from './property-panels/BarChartPanel.vue'
  import LineChartPanel from './property-panels/LineChartPanel.vue'
  import PieChartPanel from './property-panels/PieChartPanel.vue'
  import DivContainerPanel from './property-panels/DivContainerPanel.vue'
  import CustomChartPanel from './property-panels/CustomChartPanel.vue'
  import InputPanel from './property-panels/InputPanel.vue'
  import SelectPanel from './property-panels/SelectPanel.vue'
  import DatePickerPanel from './property-panels/DatePickerPanel.vue'
  import ButtonPanel from './property-panels/ButtonPanel.vue'
  import FormContainerPanel from './property-panels/FormContainerPanel.vue'
  import FormRowPanel from './property-panels/FormRowPanel.vue'
  import OptionsEditor from './OptionsEditor.vue'
  import DraggableDialog from './DraggableDialog.vue'

  const props = defineProps({
    selectedComponent: {
      type: Object,
      default: null
    },
    selectedFormChild: {
      type: Object,
      default: null
    },
    selectedFormColumn: {
      type: Object,
      default: null
    },
    selectedFormRow: {
      type: Object,
      default: null
    },
    formContainer: {
      type: Object,
      default: null
    },
    canvasSize: {
      type: Object,
      default: () => ({ width: 1920, height: 1080 })
    },
    allComponents: {
      type: Array,
      default: () => []
    },
    globalMethods: {
      type: String,
      default: ''
    },
    globalVariables: {
      type: Object,
      default: () => ({})
    }
  })

  const emit = defineEmits([
    'update-property',
    'update-form-child-property',
    'update-form-column-property',
    'update-form-row-property'
  ])

  const activeCollapse = ref(['basic', 'config'])

  // 组件ID编辑相关状态
  const componentIdError = ref('')

  // 显示的宽高值 - 铺满画布时显示画布尺寸，否则显示组件尺寸
  const displayWidth = computed(() => {
    return props.selectedComponent?.fullCanvas
      ? props.canvasSize.width
      : props.selectedComponent?.size.width
  })

  const displayHeight = computed(() => {
    return props.selectedComponent?.fullCanvas
      ? props.canvasSize.height
      : props.selectedComponent?.size.height
  })

  // Data editor state
  const dataEditorVisible = ref(false)
  const tableData = ref([])
  const tableColumns = ref([])
  const dataEditorContent = ref('')

  // Column editor state for API table
  const showColumnEditor = ref(false)
  const editingColumns = ref([])

  // Options editor state
  const optionsEditorVisible = ref(false)

  // 颜色配置相关数据
  const predefineColors = ref([
    '#ff4500',
    '#ff8c00',
    '#ffd700',
    '#90ee90',
    '#00ced1',
    '#1e90ff',
    '#c71585',
    '#ff1493',
    '#333333',
    '#666666',
    '#999999',
    '#cccccc'
  ])

  const customColors = ref([
    '#409EFF',
    '#67c23a',
    '#e6a23c',
    '#f56c6c',
    '#909399'
  ])

  // 自定义渐变色数据
  const customGradients = ref([
    {
      startColor: '#409EFF',
      endColor: '#67c23a'
    },
    {
      startColor: '#e6a23c',
      endColor: '#f56c6c'
    }
  ])

  // 计算属性
  const isStaticComponent = computed(
    () =>
      props.selectedComponent &&
      ['pie-chart', 'bar-chart', 'line-chart'].includes(
        props.selectedComponent.type
      )
  )

  const isCustomChart = computed(
    () =>
      props.selectedComponent && props.selectedComponent.type === 'custom-chart'
  )

  const isApiComponent = computed(
    () =>
      props.selectedComponent && props.selectedComponent.dataSource === 'api'
  )

  const isPieChart = computed(
    () =>
      props.selectedComponent && props.selectedComponent.type === 'pie-chart'
  )

  const isChartComponent = computed(
    () =>
      props.selectedComponent &&
      [
        'pie-chart',
        'bar-chart',
        'line-chart',
        'api-chart',
        'custom-chart'
      ].includes(props.selectedComponent.type)
  )

  // 当前编辑器的options配置
  const currentOptionsForEditor = computed(() => {
    if (
      !props.selectedComponent ||
      props.selectedComponent.type !== 'custom-chart'
    ) {
      return {}
    }

    // 优先使用自定义options，否则使用默认options
    return (
      props.selectedComponent.config?.customConfig?.customOptions ||
      props.selectedComponent.config?.options ||
      {}
    )
  })

  const isDivContainer = computed(
    () =>
      props.selectedComponent &&
      props.selectedComponent.type === 'div-container'
  )

  // 表单组件判断
  const isFormComponent = computed(
    () =>
      props.selectedComponent &&
      ['input', 'select', 'datepicker', 'button'].includes(
        props.selectedComponent.type
      )
  )

  const isInputComponent = computed(
    () => props.selectedComponent && props.selectedComponent.type === 'input'
  )

  const isSelectComponent = computed(
    () => props.selectedComponent && props.selectedComponent.type === 'select'
  )

  const isDatePickerComponent = computed(
    () =>
      props.selectedComponent && props.selectedComponent.type === 'datepicker'
  )

  const isButtonComponent = computed(
    () => props.selectedComponent && props.selectedComponent.type === 'button'
  )

  const isFormContainerComponent = computed(
    () =>
      props.selectedComponent &&
      props.selectedComponent.type === 'form-container'
  )

  // 获取表单容器的变量列表（用于表单子组件的变量绑定）
  const formContainerVariables = computed(() => {
    // 如果当前选中的是表单容器，直接返回其变量列表
    if (props.selectedComponent?.type === 'form-container') {
      return props.selectedComponent.config?.variables || []
    }

    // 如果选中的是表单子组件，从formContainer获取变量列表
    if (props.selectedFormChild && props.formContainer) {
      return props.formContainer.config?.variables || []
    }

    return []
  })

  // 解析全局方法列表
  const globalMethodOptions = computed(() => {
    const methods = []

    if (!props.globalMethods) {
      return methods
    }

    // 匹配函数声明和箭头函数
    const functionRegex =
      /(?:function\s+(\w+)\s*\(([^)]*)\)|const\s+(\w+)\s*=\s*(?:async\s+)?\(([^)]*)\)\s*=>|(\w+)\s*:\s*(?:async\s+)?function\s*\(([^)]*)\))/g
    const commentRegex = /\/\*\*?\s*(.*?)\s*\*?\*\//

    let match
    while ((match = functionRegex.exec(props.globalMethods)) !== null) {
      const name = match[1] || match[3] || match[5]
      const params = match[2] || match[4] || match[6] || ''

      // 查找方法前的注释
      const methodStartIndex = props.globalMethods.indexOf(match[0])
      const linesBeforeMethod = props.globalMethods
        .substring(0, methodStartIndex)
        .split('\n')
      let description = ''

      // 查找最近的注释
      for (let i = linesBeforeMethod.length - 1; i >= 0; i--) {
        const line = linesBeforeMethod[i].trim()
        if (line.startsWith('//')) {
          description = line.replace(/^\/\/\s*/, '')
          break
        } else if (line.includes('/**') || line.includes('/*')) {
          const commentMatch = commentRegex.exec(line)
          if (commentMatch) {
            description = commentMatch[1]
          }
          break
        } else if (line !== '') {
          break
        }
      }

      const methodInfo = {
        label: `${name}(${params}) - ${description || '无描述'}`,
        value: name,
        params,
        description: description || '无描述'
      }

      methods.push(methodInfo)
    }

    return methods
  })

  // 监听选中组件变化
  watch(
    () => props.selectedComponent,
    newComponent => {
      if (newComponent && isStaticComponent.value) {
        dataEditorContent.value = JSON.stringify(
          newComponent.config.options,
          null,
          2
        )
      }

      if (newComponent && newComponent.type === 'api-table') {
        editingColumns.value = JSON.parse(
          JSON.stringify(newComponent.config.columns || [])
        )
      }

      // 同步自定义颜色（自定义图表不需要）
      if (
        newComponent &&
        newComponent.config?.colors?.customColors &&
        !isCustomChart.value
      ) {
        customColors.value = [...newComponent.config.colors.customColors]
      } else if (
        newComponent &&
        isChartComponent.value &&
        !isCustomChart.value
      ) {
        // 如果是图表组件但没有自定义颜色，使用默认值
        const defaultColors = [
          '#409EFF',
          '#67c23a',
          '#e6a23c',
          '#f56c6c',
          '#909399'
        ]
        customColors.value = [...defaultColors]
        // 初始化默认的自定义颜色配置
        if (!newComponent.config?.colors?.customColors) {
          updateProperty('config.colors.customColors', defaultColors)
        }
      }

      // 同步自定义渐变色（自定义图表不需要）
      if (
        newComponent &&
        newComponent.config?.colors?.customGradients &&
        !isCustomChart.value
      ) {
        customGradients.value = [...newComponent.config.colors.customGradients]
      } else if (
        newComponent &&
        isChartComponent.value &&
        !isCustomChart.value
      ) {
        // 如果是图表组件但没有自定义渐变色，使用默认值
        const defaultGradients = [
          {
            startColor: '#409EFF',
            endColor: '#67c23a'
          },
          {
            startColor: '#e6a23c',
            endColor: '#f56c6c'
          }
        ]
        customGradients.value = [...defaultGradients]
        // 初始化默认的自定义渐变色配置
        if (!newComponent.config?.colors?.customGradients) {
          updateProperty('config.colors.customGradients', defaultGradients)
        }
      }
    },
    { deep: true }
  )

  // 从图表配置初始化表格数据
  const initTableDataFromChart = options => {
    if (!options) {
      if (isPieChart.value) {
        tableData.value = [
          ['', '数值'],
          ['类别1', '12'],
          ['类别2', '19'],
          ['类别3', '5'],
          ['类别4', '2'],
          ['类别5', '18']
        ]
        tableColumns.value = [0, 1]
      } else {
        tableData.value = [
          ['', '系列1', '系列2'],
          ['类别1', '12', '7'],
          ['类别2', '19', '11'],
          ['类别3', '5', '13'],
          ['类别4', '2', '21'],
          ['类别5', '18', '9']
        ]
        tableColumns.value = [0, 1, 2]
      }
      return
    }

    const series = options.series || []

    if (series[0]?.type === 'pie' && series[0]?.data) {
      const pieData = series[0].data
      const headerRow = ['', '数值']
      const dataRows = pieData.map(item => [
        item.name || '',
        String(item.value || '')
      ])

      tableData.value = [headerRow, ...dataRows]
      tableColumns.value = [0, 1]
      return
    }

    if (!options.xAxis || !options.xAxis.data) {
      if (isPieChart.value) {
        tableData.value = [
          ['', '数值'],
          ['类别1', '12']
        ]
        tableColumns.value = [0, 1]
      } else {
        tableData.value = [
          ['', '系列1', '系列2'],
          ['类别1', '12', '7']
        ]
        tableColumns.value = [0, 1, 2]
      }
      return
    }

    const categories = options.xAxis.data || []
    const headerRow = ['']
    series.forEach(s => {
      headerRow.push(s.name || '系列')
    })

    const dataRows = categories.map((category, index) => {
      const row = [category]
      series.forEach(s => {
        row.push(String(s.data?.[index] || ''))
      })
      return row
    })

    tableData.value = [headerRow, ...dataRows]
    tableColumns.value = Array.from({ length: headerRow.length }, (_, i) => i)
  }

  // 更新属性
  const updateProperty = (key, value) => {
    emit('update-property', key, value)
  }

  // 检查组件ID是否唯一
  const isComponentIdUnique = (newId, currentId) => {
    return !props.allComponents.some(
      component => component.id === newId && component.id !== currentId
    )
  }

  // 验证组件ID
  const validateComponentId = () => {
    const currentId = props.selectedComponent?.id
    if (!currentId) {
      componentIdError.value = 'ID不能为空'
      return false
    }

    // 检查ID格式（只允许字母、数字、下划线、连字符）
    const idPattern = /^[a-zA-Z0-9_-]+$/
    if (!idPattern.test(currentId)) {
      componentIdError.value = 'ID只能包含字母、数字、下划线和连字符'
      return false
    }

    // 检查ID唯一性
    if (!isComponentIdUnique(currentId, currentId)) {
      componentIdError.value = '该ID已存在，请使用唯一的ID'
      return false
    }

    componentIdError.value = ''
    return true
  }

  // 处理ID更新
  const handleIdUpdate = newId => {
    // 立即更新组件ID
    updateProperty('id', newId)

    // 清除之前的错误信息
    componentIdError.value = ''
  }

  // 监听选中组件的变化，清除ID错误信息
  watch(
    () => props.selectedComponent?.id,
    () => {
      componentIdError.value = ''
    }
  )

  // 更新表单子组件属性
  const updateFormChildProperty = (key, value) => {
    emit('update-form-child-property', key, value)
  }

  // 更新表单列属性
  const updateFormColumnProperty = (key, value) => {
    emit('update-form-column-property', key, value)
  }

  // 更新表单行属性
  const updateFormRowProperty = (key, value) => {
    emit('update-form-row-property', key, value)
  }

  // 处理表单容器子元素顺序更新
  const handleUpdateChildrenOrder = newChildren => {
    updateProperty('config.children', newChildren)
  }

  // 处理图表类型变化
  const handleChartTypeChange = (chartComponentType, newChartSpecificType) => {
    if (!props.selectedComponent) return

    // 1. Get a fresh template based on the base chart type (e.g., 'bar-chart')
    const newOptions = getChartTemplate(chartComponentType)
    if (!newOptions) {
      console.error(`Could not find template for ${chartComponentType}`)
      return
    }

    // 2. Preserve essential data from the old options
    const currentOptions = props.selectedComponent.config.options
    if (currentOptions) {
      // Preserve data series, categories, and legend info
      if (currentOptions.series) newOptions.series = currentOptions.series
      if (currentOptions.xAxis) newOptions.xAxis = currentOptions.xAxis
      if (currentOptions.yAxis) newOptions.yAxis = currentOptions.yAxis
      if (currentOptions.legend) newOptions.legend = currentOptions.legend
    }

    // 3. Update the specific config that tracks the new subtype
    if (chartComponentType === 'bar-chart') {
      updateProperty('config.barConfig.type', newChartSpecificType)
    } else if (chartComponentType === 'line-chart') {
      updateProperty('config.lineConfig.type', newChartSpecificType)
    }

    // 4. Apply display configurations
    // 应用标题显示配置
    if (props.selectedComponent.config?.showTitle === false) {
      newOptions.title.show = false
    } else {
      newOptions.title.show = true
      if (props.selectedComponent.config?.title) {
        newOptions.title.text = props.selectedComponent.config.title
      }
    }

    if (props.selectedComponent.config?.showLegend === false) {
      newOptions.legend.show = false
    } else {
      newOptions.legend.show = true
    }

    if (props.selectedComponent.config?.showTooltip === false) {
      newOptions.tooltip.show = false
    } else {
      newOptions.tooltip.show = true
    }

    // 5. Finally, replace the entire options object with the newly constructed one.
    // This triggers the chart to re-render with a consistent configuration.
    updateProperty('config.options', newOptions)
  }

  // 保存数据编辑
  const saveDataEditor = () => {
    if (props.selectedComponent?.type === 'custom-chart') {
      // 自定义图表的数据保存逻辑
      const extractedData =
        props.selectedComponent.config?.customConfig?.extractedData
      const originalOptions =
        props.selectedComponent.config?.customConfig?.customOptions

      if (!extractedData || !originalOptions) {
        ElMessage.error('缺少必要的配置信息')
        return
      }

      const newOptions = convertTableDataToOptions(
        tableData.value,
        tableColumns.value,
        originalOptions,
        extractedData.chartType
      )

      // 更新配置
      updateProperty('config.customConfig.customOptions', newOptions)
      updateProperty('config.options', newOptions)

      dataEditorVisible.value = false
      ElMessage.success('数据更新成功')
    } else {
      // 原有的静态图表数据保存逻辑
      try {
        const options = convertTableToEchartsConfig()
        if (options) {
          updateProperty('config.options', options)
          dataEditorVisible.value = false
          ElMessage.success('数据更新成功')
        } else {
          ElMessage.error('表格数据不完整，请检查')
        }
      } catch (error) {
        ElMessage.error('数据转换失败，请检查表格数据')
        console.error(error)
      }
    }
  }

  // 添加列
  const addColumn = () => {
    editingColumns.value.push({
      key: '',
      label: '',
      width: 120
    })
  }

  // 删除列
  const removeColumn = index => {
    editingColumns.value.splice(index, 1)
  }

  // 保存列编辑
  const saveColumnEditor = () => {
    updateProperty('config.columns', editingColumns.value)
    showColumnEditor.value = false
    ElMessage.success('列配置更新成功')
  }

  // 获取列标签 A, B, C, D...
  const getColumnLabel = index => {
    return String.fromCharCode(65 + index)
  }

  // 添加表格列
  const addTableColumn = () => {
    if (isPieChart.value) {
      return
    }
    const newColIndex = tableColumns.value.length
    tableColumns.value.push(newColIndex)
    tableData.value.forEach(row => {
      row.push('')
    })
  }

  // 添加表格行
  const addTableRow = () => {
    const newRow = new Array(tableColumns.value.length).fill('')
    tableData.value.push(newRow)
  }

  // 删除表格行
  const removeTableRow = rowIndex => {
    if (tableData.value.length > 1) {
      tableData.value.splice(rowIndex, 1)
    }
  }

  // 清空表格数据
  const clearTableData = () => {
    if (isPieChart.value) {
      tableData.value = [
        ['', '数值'],
        ['类别1', ''],
        ['类别2', ''],
        ['类别3', ''],
        ['类别4', ''],
        ['类别5', '']
      ]
      tableColumns.value = [0, 1]
    } else {
      tableData.value = [
        ['', '系列1', '系列2'],
        ['类别1', '', ''],
        ['类别2', '', ''],
        ['类别3', '', '']
      ]
      tableColumns.value = [0, 1, 2]
    }
  }

  // 更新表格数据
  const updateTableData = () => {
    // 实时更新图表数据预览
  }

  // 打开数据编辑器
  const openDataEditor = () => {
    if (props.selectedComponent) {
      initTableDataFromChart(props.selectedComponent.config?.options)
    }
    dataEditorVisible.value = true
  }

  // 打开Options编辑器
  const openOptionsEditor = () => {
    optionsEditorVisible.value = true
  }

  // 处理Options保存
  const handleOptionsSave = options => {
    if (
      !props.selectedComponent ||
      props.selectedComponent.type !== 'custom-chart'
    ) {
      return
    }

    // 保存自定义options配置
    updateProperty('config.customConfig.customOptions', options)

    // 同时更新主要的options配置，用于图表渲染
    updateProperty('config.options', options)
  }

  // 从自定义图表配置中提取示例数据
  const extractSampleDataFromOptions = options => {
    if (!options || !options.series) {
      return null
    }

    const series = options.series
    const categories = options.xAxis?.data || []

    // 判断图表类型
    const chartType = series[0]?.type

    if (chartType === 'pie') {
      // 饼图数据处理
      const pieData = series[0]?.data || []
      const headerRow = ['', '数值']
      const dataRows = pieData.map(item => [
        typeof item === 'object' ? item.name || '' : '',
        typeof item === 'object' ? String(item.value || '') : String(item || '')
      ])

      return {
        tableData: [headerRow, ...dataRows],
        tableColumns: [0, 1],
        chartType: 'pie'
      }
    } else {
      // 其他图表类型（柱状图、折线图等）
      const headerRow = ['']
      series.forEach(s => {
        headerRow.push(s.name || '系列')
      })

      const dataRows = categories.map((category, index) => {
        const row = [category]
        series.forEach(s => {
          const value = s.data?.[index]
          row.push(
            typeof value === 'object'
              ? String(value.value || '')
              : String(value || '')
          )
        })
        return row
      })

      return {
        tableData: [headerRow, ...dataRows],
        tableColumns: Array.from({ length: headerRow.length }, (_, i) => i),
        chartType: chartType || 'bar'
      }
    }
  }

  // 将表格数据转换回ECharts配置
  const convertTableDataToOptions = (
    tableData,
    tableColumns,
    originalOptions,
    chartType
  ) => {
    if (!tableData || tableData.length < 2) {
      return originalOptions
    }

    const newOptions = JSON.parse(JSON.stringify(originalOptions))

    if (chartType === 'pie') {
      // 处理饼图数据
      const pieData = []
      for (let i = 1; i < tableData.length; i++) {
        const row = tableData[i]
        if (row[0] && row[1]) {
          pieData.push({
            name: row[0],
            value: parseFloat(row[1]) || 0
          })
        }
      }

      if (newOptions.series && newOptions.series[0]) {
        newOptions.series[0].data = pieData
      }

      // 更新图例数据
      if (newOptions.legend) {
        newOptions.legend.data = pieData.map(item => item.name)
      }
    } else {
      // 处理其他图表类型
      const categories = []
      const seriesData = {}

      // 提取类别和系列数据
      for (let i = 1; i < tableData.length; i++) {
        const row = tableData[i]
        if (row[0]) {
          categories.push(row[0])

          for (let j = 1; j < row.length; j++) {
            const seriesIndex = j - 1
            if (!seriesData[seriesIndex]) {
              seriesData[seriesIndex] = []
            }
            seriesData[seriesIndex].push(parseFloat(row[j]) || 0)
          }
        }
      }

      // 更新xAxis数据
      if (newOptions.xAxis) {
        newOptions.xAxis.data = categories
      }

      // 更新series数据
      if (newOptions.series) {
        Object.keys(seriesData).forEach(index => {
          const seriesIndex = parseInt(index)
          if (newOptions.series[seriesIndex]) {
            newOptions.series[seriesIndex].data = seriesData[index]
          }
        })
      }

      // 更新图例数据
      if (newOptions.legend && newOptions.series) {
        newOptions.legend.data = newOptions.series.map(s => s.name)
      }
    }

    return newOptions
  }

  // 打开自定义图表数据编辑器
  const openCustomChartDataEditor = () => {
    if (
      !props.selectedComponent ||
      props.selectedComponent.type !== 'custom-chart'
    ) {
      return
    }

    const options = props.selectedComponent.config?.customConfig?.customOptions
    if (!options) {
      ElMessage.warning('请先配置 Options')
      return
    }

    // 直接从options中提取数据
    const extractedData = extractSampleDataFromOptions(options)
    if (!extractedData) {
      ElMessage.warning('无法从配置中提取数据')
      return
    }

    // 保存提取的数据到配置中，用于后续保存时使用
    updateProperty('config.customConfig.extractedData', extractedData)

    // 设置表格数据
    tableData.value = [...extractedData.tableData]
    tableColumns.value = [...extractedData.tableColumns]

    // 打开数据编辑器
    dataEditorVisible.value = true
  }

  // 自定义颜色相关方法
  const addCustomColor = () => {
    const newColors = [...(customColors.value || [])]
    newColors.push('#409EFF')
    updateProperty('config.colors.customColors', newColors)
    customColors.value = newColors
  }

  const removeCustomColor = index => {
    const newColors = [...customColors.value]
    if (newColors.length > 1) {
      newColors.splice(index, 1)
      updateProperty('config.colors.customColors', newColors)
      customColors.value = newColors
    } else {
      ElMessage.warning('至少需要保留一种颜色')
    }
  }

  const updateCustomColor = (index, color) => {
    const newColors = [...customColors.value]
    newColors[index] = color
    updateProperty('config.colors.customColors', newColors)
    customColors.value = newColors
  }

  // 自定义渐变色相关方法
  const addCustomGradient = () => {
    const newGradients = [...(customGradients.value || [])]
    newGradients.push({
      startColor: '#409EFF',
      endColor: '#67c23a'
    })
    updateProperty('config.colors.customGradients', newGradients)
    customGradients.value = newGradients
  }

  const removeCustomGradient = index => {
    const newGradients = [...customGradients.value]
    if (newGradients.length > 1) {
      newGradients.splice(index, 1)
      updateProperty('config.colors.customGradients', newGradients)
      customGradients.value = newGradients
    } else {
      ElMessage.warning('至少需要保留一个渐变色')
    }
  }

  const updateCustomGradientColor = (index, colorType, color) => {
    const newGradients = [...customGradients.value]
    newGradients[index][colorType] = color
    updateProperty('config.colors.customGradients', newGradients)
    customGradients.value = newGradients
  }

  // 生成渐变预览样式
  const getGradientPreviewStyle = (gradient, direction) => {
    const { startColor, endColor } = gradient

    let gradientCSS = ''
    switch (direction) {
      case 'horizontal':
        gradientCSS = `linear-gradient(to right, ${startColor}, ${endColor})`
        break
      case 'vertical':
        gradientCSS = `linear-gradient(to bottom, ${startColor}, ${endColor})`
        break
      case 'diagonal1':
        gradientCSS = `linear-gradient(to bottom right, ${startColor}, ${endColor})`
        break
      case 'diagonal2':
        gradientCSS = `linear-gradient(to top right, ${startColor}, ${endColor})`
        break
      case 'radial':
        gradientCSS = `radial-gradient(circle, ${startColor}, ${endColor})`
        break
      default:
        gradientCSS = `linear-gradient(to bottom, ${startColor}, ${endColor})`
        break
    }

    return {
      background: gradientCSS,
      height: '20px',
      borderRadius: '4px',
      marginTop: '8px'
    }
  }

  // 将表格数据转换为ECharts配置
  const convertTableToEchartsConfig = () => {
    if (!tableData.value || tableData.value.length < 2) {
      return null
    }
    if (!props.selectedComponent) return null

    const template = getChartTemplate(props.selectedComponent.type)
    if (!template) return null

    const headers = tableData.value[0].slice(1)
    const categories = []

    for (let i = 1; i < tableData.value.length; i++) {
      const row = tableData.value[i]
      if (row[0]) {
        categories.push(row[0])
      }
    }

    if (props.selectedComponent.type === 'pie-chart') {
      const pieData = []
      for (let i = 1; i < tableData.value.length; i++) {
        const row = tableData.value[i]
        if (row[0] && row[1]) {
          pieData.push({
            name: row[0],
            value: parseFloat(row[1]) || 0
          })
        }
      }
      template.series[0].data = pieData
      template.legend.data = pieData.map(item => item.name)
    } else {
      const series = []
      headers.forEach(header => {
        series.push({
          name: header,
          type: props.selectedComponent.type === 'line-chart' ? 'line' : 'bar',
          data: [],
          smooth: props.selectedComponent.type === 'line-chart'
        })
      })

      for (let i = 1; i < tableData.value.length; i++) {
        const row = tableData.value[i]
        if (row[0]) {
          for (let j = 1; j < row.length; j++) {
            const value = parseFloat(row[j]) || 0
            if (series[j - 1]) {
              series[j - 1].data.push(value)
            }
          }
        }
      }
      template.xAxis.data = categories
      template.series = series
      template.legend.data = headers
    }

    // 应用标题配置
    if (props.selectedComponent.config?.showTitle === false) {
      template.title.show = false
    } else {
      template.title.show = true
      if (props.selectedComponent.config?.title) {
        template.title.text = props.selectedComponent.config.title
      }
    }

    // 应用显示配置
    if (props.selectedComponent.config?.showLegend === false) {
      template.legend.show = false
    } else {
      template.legend.show = true
    }

    if (props.selectedComponent.config?.showTooltip === false) {
      template.tooltip.show = false
    } else {
      template.tooltip.show = true
    }

    // 应用颜色配置
    const colorConfig = props.selectedComponent.config?.colors
    const displayConfig = {
      showTitle: props.selectedComponent.config?.showTitle,
      showLegend: props.selectedComponent.config?.showLegend,
      showTooltip: props.selectedComponent.config?.showTooltip
    }
    return colorConfig
      ? applyColorConfig(template, colorConfig, displayConfig)
      : template
  }

  // 处理铺满画布
  const handleFullCanvas = value => {
    updateProperty('fullCanvas', value)
    if (value) {
      // 保存原始尺寸以便恢复
      updateProperty('originalSize', { ...props.selectedComponent.size })
      // 铺满画布时位置设为0,0
      updateProperty('position', { x: 0, y: 0 })
      // 不需要修改size，渲染时会根据fullCanvas标志处理
    } else {
      // 取消铺满画布时恢复原始尺寸和位置
      const originalSize = props.selectedComponent.originalSize || {
        width: 400,
        height: 300
      }
      updateProperty('size', originalSize)
      // 如果有原始位置，也可以恢复，否则保持当前位置
    }
  }

  // 修正DIV容器宽度为画布宽度
  const fixDivContainerWidth = () => {
    if (props.selectedComponent?.type === 'div-container') {
      updateProperty('size.width', props.canvasSize.width)
      ElMessage.success(
        `已将DIV容器宽度修正为画布宽度 ${props.canvasSize.width}px`
      )
    }
  }
</script>

<style scoped>
  .property-panel {
    width: 320px;
    background-color: #f8f9fa;
    border-left: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .panel-header {
    padding: 16px;
    border-bottom: 1px solid #e0e0e0;
    background-color: #fff;
  }

  .panel-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
  }

  .no-selection {
    flex-grow: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #909399;
    text-align: center;
  }

  .no-selection-content {
    text-align: center;
    color: #909399;
  }

  .no-selection-content p {
    margin-top: 12px;
    margin-bottom: 0;
  }

  .form-child-indicator,
  .form-column-indicator,
  .form-row-indicator {
    font-size: 12px;
    color: #409eff;
    background: rgba(64, 158, 255, 0.1);
    padding: 2px 8px;
    border-radius: 4px;
    margin-left: 8px;
  }

  .form-column-indicator {
    color: #67c23a;
    background: rgba(103, 194, 58, 0.1);
  }

  .form-row-indicator {
    color: #e6a23c;
    background: rgba(230, 162, 60, 0.1);
  }

  .property-content {
    flex-grow: 1;
    overflow-y: auto;
  }

  .property-group {
    padding: 8px 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .property-group:empty {
    display: none;
  }

  .property-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
  }

  .property-item .el-input-number {
    flex: 1;
    min-width: 0;
    width: 130px;
  }

  .property-item .el-input {
    flex: 1;
    min-width: 0;
    width: 130px;
  }

  .property-item .el-select {
    flex: 1;
    min-width: 0;
  }

  .property-item .el-color-picker {
    flex-shrink: 0;
  }

  .property-item .el-switch {
    flex-shrink: 0;
  }

  .property-item-row {
    display: flex;
    align-items: flex-end;
    gap: 16px;
  }

  .property-item-row .property-item {
    flex: 1;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .property-label {
    font-size: 14px;
    color: #303133;
    white-space: nowrap;
    flex-shrink: 0;
    width: 85px;
    text-align: left;
  }

  .position-inputs,
  .size-inputs {
    display: flex;
    gap: 8px;
    flex: 1;
  }

  .input-with-label {
    display: flex;
    align-items: center;
    gap: 4px;
    flex: 1;
  }

  .input-label {
    font-size: 12px;
    color: #606266;
  }

  .position-inputs .el-input-number,
  .size-inputs .el-input-number {
    flex: 1;
    min-width: 60px;
  }

  .property-item-row .el-input-number {
    width: 100%;
  }

  .size-full-canvas-row .property-item:last-child {
    flex-grow: 0;
  }

  :deep(.el-collapse-item__header) {
    padding: 0 16px;
    font-size: 14px;
    font-weight: 600;
    background-color: #fff;
    border-bottom: 1px solid #e0e0e0;
  }

  :deep(.el-collapse-item__wrap) {
    border-bottom: none;
  }

  :deep(.el-collapse-item__content) {
    padding-bottom: 0;
  }

  .api-editor-modal .el-drawer__body {
    display: flex;
    flex-direction: column;
  }

  .api-editor-modal .el-form {
    flex-grow: 1;
  }

  .api-editor-modal .response-viewer {
    margin-top: 16px;
    flex-shrink: 0;
    height: 200px;
  }

  .api-editor-modal .el-drawer__footer {
    padding: 16px;
    border-top: 1px solid #e0e0e0;
  }

  .column-editor {
    max-height: 400px;
    overflow-y: auto;
  }

  .column-item {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
    align-items: center;
  }

  .column-item .el-input {
    flex: 1;
  }

  .column-item .el-input-number {
    width: 100px;
  }

  .add-column-btn {
    width: 100%;
    margin-top: 8px;
  }

  /* 表格编辑器样式 */
  .data-editor {
    max-height: 500px;
    overflow-y: auto;
  }

  .spreadsheet-container {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 16px;
  }

  .spreadsheet-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
  }

  .spreadsheet-table th,
  .spreadsheet-table td {
    border: 1px solid #dcdfe6;
    padding: 0;
    text-align: center;
    vertical-align: middle;
  }

  .row-header,
  .col-header {
    background-color: #f5f7fa;
    font-weight: 500;
    color: #606266;
    width: 50px;
    height: 32px;
    line-height: 32px;
  }

  .col-header {
    min-width: 80px;
  }

  .add-col-btn {
    cursor: pointer;
    color: #409eff;
    width: 40px;
  }

  .add-col-btn:hover {
    background-color: #ecf5ff;
  }

  .data-cell {
    padding: 0;
    width: 80px;
    height: 32px;
  }

  .cell-input {
    width: 100%;
    height: 32px;
    border: none;
    outline: none;
    padding: 4px 8px;
    font-size: 13px;
    background: transparent;
    text-align: center;
  }

  .cell-input:focus {
    background-color: #fff;
    box-shadow: inset 0 0 0 1px #409eff;
  }

  .action-cell {
    width: 80px;
    padding: 2px;
  }

  .table-actions {
    padding: 8px;
    background-color: #fafafa;
    border-top: 1px solid #dcdfe6;
  }

  .chart-actions-section {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 12px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
  }

  .color-input-group {
    display: flex;
    gap: 8px;
  }

  .color-input {
    width: 100px;
  }

  .custom-colors-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .custom-color-item {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .add-color-btn {
    width: 100%;
    margin-top: 8px;
  }

  /* 自定义渐变色样式 */
  .gradient-config-section {
    flex-direction: column;
    align-items: flex-start;
  }

  .gradient-config-section .property-label {
    width: 100%;
    margin-bottom: 12px;
    font-weight: 500;
  }

  .custom-gradients-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    width: 100%;
  }

  .gradient-item-card {
    border: 1px solid #e1e6ef;
    border-radius: 8px;
    padding: 12px;
    background-color: #ffffff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
  }

  .gradient-item-card:hover {
    border-color: #409eff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
  }

  .gradient-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .gradient-index {
    font-size: 13px;
    font-weight: 500;
    color: #303133;
  }

  .remove-gradient-btn {
    color: #f56c6c;
    padding: 4px;
  }

  .remove-gradient-btn:hover {
    background-color: #fef0f0;
    color: #f56c6c;
  }

  .gradient-colors-row {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
  }

  .gradient-color-picker {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    flex: 1;
  }

  .color-picker-label {
    font-size: 12px;
    color: #606266;
    font-weight: 500;
  }

  .color-value {
    font-size: 11px;
    color: #909399;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background-color: #f5f7fa;
    padding: 2px 6px;
    border-radius: 3px;
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .gradient-arrow {
    font-size: 16px;
    color: #409eff;
    font-weight: bold;
    margin: 0 4px;
    align-self: center;
    margin-top: 20px;
  }

  .gradient-preview {
    border: 1px solid #e1e6ef;
    border-radius: 4px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .add-gradient-btn {
    width: 100%;
    margin-top: 8px;
    border: 2px dashed #d9d9d9;
    color: #666;
    background-color: #fafafa;
    transition: all 0.2s ease;
  }

  .add-gradient-btn:hover {
    border-color: #409eff;
    color: #409eff;
    background-color: #f0f8ff;
  }

  .property-group-color {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 12px;
  }

  .config-form {
    padding: 8px 16px;
  }

  /* 基础属性输入框样式 */
  .basic-property-item {
    align-items: center;
    min-height: 40px;
  }

  .basic-property-item .property-label {
    width: 85px;
    text-align: left;
    margin-right: 16px;
  }

  .basic-input,
  .basic-input-number {
    width: 150px !important;
    flex: none !important;
  }

  .basic-property-item .el-switch {
    margin-left: auto;
  }

  .canvas-size-hint {
    font-size: 11px;
    color: #909399;
    font-weight: normal;
    margin-left: 4px;
  }

  .size-mismatch-hint {
    color: #e6a23c;
    font-size: 12px;
    margin-left: 4px;
    cursor: help;
  }

  .input-with-button {
    display: flex;
    gap: 4px;
    align-items: center;
    width: 150px;
  }

  .input-with-button .basic-input-number {
    flex: 1;
    width: auto !important;
    min-width: 80px;
  }

  .fix-button {
    flex-shrink: 0;
    height: 28px;
    padding: 0 4px;
    font-size: 12px;
    min-width: 28px;
    width: 28px;
  }

  /* 组件ID编辑样式 */
  .id-error {
    border-color: #f56c6c !important;
  }

  .id-error:focus {
    border-color: #f56c6c !important;
    box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2) !important;
  }

  .error-message {
    font-size: 12px;
    color: #f56c6c;
    margin-top: 4px;
    line-height: 1.4;
  }
</style>
