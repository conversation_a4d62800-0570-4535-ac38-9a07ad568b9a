<template>
  <ElFormItem label="图表类型">
    <ElSelect
      :model-value="localConfig.type"
      @change="updateProperty('type', $event)"
      placeholder="请选择图表类型"
      size="small"
    >
      <ElOption label="基础折线图" value="basic" />
      <ElOption label="平滑折线图" value="smooth" />
      <ElOption label="阶梯折线图" value="step" />
      <ElOption label="面积图" value="area" />
    </ElSelect>
  </ElFormItem>

  <ElFormItem label="显示标记">
    <ElSwitch
      :model-value="localConfig.showSymbol"
      @change="updateProperty('showSymbol', $event)"
    />
  </ElFormItem>

  <ElFormItem label="线条宽度">
    <ElInputNumber
      :model-value="localConfig.lineWidth"
      @change="updateProperty('lineWidth', $event)"
      :min="1"
      :max="10"
      size="small"
      controls-position="right"
    />
  </ElFormItem>

  <!-- 变量绑定组件 -->
  <VariableBinding
    :config="componentConfig"
    :variables="variables"
    @update-property="handleVariableBindingUpdate"
  />
</template>

<script setup>
  import { computed } from 'vue'
  import {
    ElFormItem,
    ElSelect,
    ElOption,
    ElSwitch,
    ElInputNumber
  } from 'element-plus'
  import VariableBinding from './VariableBinding.vue'

  const props = defineProps({
    lineConfig: {
      type: Object,
      required: true
    },
    componentConfig: {
      type: Object,
      required: true
    },
    variables: {
      type: Object,
      default: () => ({})
    }
  })

  const emit = defineEmits(['update-property', 'update:type'])

  const localConfig = computed({
    get: () => props.lineConfig,
    set: () => {
      // Setter is not used, updates are emitted.
    }
  })

  function updateProperty(key, value) {
    if (key === 'type') {
      emit('update:type', value)
    } else {
      emit('update-property', `config.lineConfig.${key}`, value)
    }
  }

  // 处理变量绑定更新
  function handleVariableBindingUpdate(key, value) {
    emit('update-property', key, value)
  }
</script>

<style scoped>
  .el-select {
    width: 100%;
  }
</style>
