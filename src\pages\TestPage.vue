<template>
  <div class="bi-dashboard" :style="canvasStyle">
    <div
      :id="comp_1751516119068"
      class="form-container"
      :style="comp_1751516119068Style"
    >
      <div class="form-title">表单标题</div>
      <el-form :model="comp_1751516119068Form" class="form-content">
        <el-row :gutter="16" class="form-row">
          <el-col :span="8">
            <el-form-item label="输入框">
              <el-input v-model="name" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="下拉选择">
              <el-select v-model="selection" placeholder="请选择">
                <el-option
                  v-for="item in comp_1751517074435Options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-button type="primary" @click="hanldClick">按钮</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, computed } from 'vue'
  import {
    ElButton,
    ElInput,
    ElSelect,
    ElOption,
    ElMessage,
    ElForm,
    ElFormItem,
    ElRow,
    ElCol
  } from 'element-plus'

  // 响应式数据
  const canvasConfig = reactive({
    width: 1920,
    height: 1080
  })

  const comp_1751516119068Form = reactive({})
  const name = ref('张三')
  const selection = ref('选项1')
  const comp_1751517074435Options = ref([
    { label: '选项1', value: 'option1' },
    { label: '选项2', value: 'option2' }
  ])

  // 计算属性
  const canvasStyle = computed(() => ({
    width: canvasConfig.width + 'px',
    height: canvasConfig.height + 'px',
    position: 'relative',
    backgroundColor: '#ffffff'
  }))

  const comp_1751516119068Style = computed(() => ({
    position: 'absolute',
    left: '338px',
    top: '186px',
    width: '1302px',
    height: '425px',
    backgroundColor: '#ffffff',
    borderColor: '#e4e7ed',
    borderWidth: '1px',
    borderStyle: 'solid',
    borderRadius: '4px'
  }))

  // 方法
  const hanldClick = () => {
    // 自定义事件处理函数

    // 可以使用 this.variables 访问组件变量

    // 可以使用预装的工具类：axios, dayjs, ElMessage, ElMessageBox

    try {
      // 在这里编写您的事件逻辑

      console.log('按钮被点击了!')

      console.log('当前表单数据:', this.variables.formData)

      // 示例：显示消息提示

      ElMessage.success('自定义事件执行成功!')
    } catch (error) {
      console.error('事件执行失败:', error)

      ElMessage.error('事件执行失败: ' + error.message)
    }
  }

  // 生命周期
</script>

<style scoped>
  .bi-dashboard {
    position: relative;
    overflow: hidden;
    font-family:
      -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC',
      'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial,
      sans-serif;
  }

  .component-container {
    box-sizing: border-box;
  }

  .chart-container {
    box-sizing: border-box;
  }

  /* 表单容器样式 */
  .form-container {
    box-sizing: border-box;
    background: #ffffff;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;
  }

  .form-title {
    background: #f5f7fa;
    padding: 12px 16px;
    border-bottom: 1px solid #e4e7ed;
    font-weight: 500;
    color: #303133;
    margin: 0;
  }

  .form-content {
    padding: 16px;
  }

  .form-row {
    margin-bottom: 16px;
  }

  .form-row:last-child {
    margin-bottom: 0;
  }

  /* Element Plus 组件样式重置 */
  .el-button {
    margin: 0;
  }

  .el-input {
    width: 100%;
  }

  .el-select {
    width: 100%;
  }

  .el-date-editor {
    width: 100%;
  }

  .el-form-item {
    margin-bottom: 16px;
  }

  .el-form-item:last-child {
    margin-bottom: 0;
  }

  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }

  .el-row {
    display: flex;
    flex-wrap: wrap;
  }

  .el-col {
    position: relative;
    box-sizing: border-box;
  }
</style>
