<template>
  <div
    class="canvas-container"
    @drop="handleDrop"
    @dragover="handleDragOver"
    @dragleave="handleDragLeave"
    @click="handleCanvasClick"
    @contextmenu="handleCanvasContextMenu"
  >
    <div class="canvas-grid" :style="canvasGridStyle">
      <!-- 响应式模式提示 -->
      <!--
      <div v-if="props.canvasSize.isResponsive" class="responsive-indicator">
        <div class="responsive-badge">
          <span class="responsive-icon">📱</span>
          <span class="responsive-text">响应式模式</span>
          <span class="responsive-desc">预览和出码将使用 100vw × 100vh</span>
        </div>
      </div>
      -->

      <!-- 参考线 -->
      <template v-if="showGuides">
        <!-- 垂直参考线 -->
        <div
          v-for="(position, index) in guides.vertical"
          :key="'v-' + index"
          class="guide-line guide-line-vertical"
          :style="{ left: convertActualToDisplayX(position) + 'px' }"
        ></div>
        <!-- 水平参考线 -->
        <div
          v-for="(position, index) in guides.horizontal"
          :key="'h-' + index"
          class="guide-line guide-line-horizontal"
          :style="{ top: convertActualToDisplayY(position) + 'px' }"
        ></div>
      </template>

      <!-- 画布组件 -->
      <div
        v-for="component in independentComponents"
        :key="component.id"
        :data-component-id="component.id"
        :data-is-container="component.config?.isContainer"
        :data-is-child-container="component.config?.isChildContainer"
        class="canvas-component"
        :class="{ selected: selectedComponent?.id === component.id }"
        :style="getComponentStyle(component)"
        @click.stop="handleComponentClick(component)"
        @mousedown="startDrag(component, $event)"
        @contextmenu="handleComponentContextMenu(component, $event)"
      >
        <!-- 组件内容 -->
        <div class="component-content">
          <RenderComponent
            :component="component"
            :is-preview="false"
            :all-components="props.components"
            :selected-component="props.selectedComponent"
            :selected-form-child="props.selectedFormChild"
            :selected-form-row="props.selectedFormRow"
            :selected-form-column="props.selectedFormColumn"
            @select-component="emit('select-component', $event)"
            @delete-component="emit('delete-component', $event)"
            @select-form-child="emit('select-form-child', $event)"
            @update-form-child="emit('update-form-child', $event)"
            @delete-form-child="
              index => emit('delete-form-child', component, index)
            "
            @reorder-form-children="
              (index, direction) =>
                emit('reorder-form-children', component, index, direction)
            "
            @select-form-row="emit('select-form-row', $event)"
            @update-form-row="emit('update-form-row', $event)"
            @delete-form-row="emit('delete-form-row', $event)"
            @reorder-form-rows="
              (index, direction) => emit('reorder-form-rows', index, direction)
            "
            @form-row-context-menu="handleFormRowContextMenu"
            @form-column-context-menu="handleFormColumnContextMenu"
            @drop-to-column="emit('drop-to-column', $event)"
            @delete-column-form-child="emit('delete-column-form-child', $event)"
            @select-form-column="emit('select-form-column', $event)"
            @move-form-child-between-columns="
              emit('move-form-child-between-columns', $event)
            "
            @start-adjust-position="emit('start-adjust-position', $event)"
            @column-click-for-adjust="emit('column-click-for-adjust', $event)"
          />
        </div>

        <!-- 选中状态的调整手柄 -->
        <div
          v-if="selectedComponent?.id === component.id"
          class="resize-handles"
        >
          <div
            class="resize-handle resize-handle-nw"
            @mousedown.stop="startResize(component, 'nw', $event)"
          ></div>
          <div
            class="resize-handle resize-handle-ne"
            @mousedown.stop="startResize(component, 'ne', $event)"
          ></div>
          <div
            class="resize-handle resize-handle-sw"
            @mousedown.stop="startResize(component, 'sw', $event)"
          ></div>
          <div
            class="resize-handle resize-handle-se"
            @mousedown.stop="startResize(component, 'se', $event)"
          ></div>
        </div>

        <!-- 删除按钮 -->
        <div
          v-if="selectedComponent?.id === component.id"
          class="delete-btn"
          @click.stop="deleteComponent(component)"
        >
          <ElIcon><Delete /></ElIcon>
        </div>
      </div>
    </div>

    <!-- 右键菜单 -->
    <ContextMenu
      :visible="contextMenu.visible"
      :x="contextMenu.x"
      :y="contextMenu.y"
      :menu-type="contextMenu.menuType"
      :target-component="contextMenu.targetComponent"
      @create-div="handleCreateDivContainer"
      @split-div="handleSplitDiv"
      @add-form-row="handleAddFormRow"
      @split-form-column="handleSplitFormColumn"
      @delete-form-column="handleDeleteFormColumn"
      @close="hideContextMenu"
    />
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, onUnmounted, inject } from 'vue'
  import { ElIcon, ElMessageBox } from 'element-plus'
  import { Delete } from '@element-plus/icons-vue'
  import RenderComponent from './RenderComponent.vue'
  import ContextMenu from './ContextMenu.vue'

  const props = defineProps({
    components: {
      type: Array,
      default: () => []
    },
    selectedComponent: {
      type: Object,
      default: null
    },
    selectedFormChild: {
      type: Object,
      default: null
    },
    selectedFormRow: {
      type: Object,
      default: null
    },
    selectedFormColumn: {
      type: Object,
      default: null
    },
    canvasSize: {
      type: Object,
      default: () => ({ width: 800, height: 600 })
    }
  })

  // 定义事件
  const emit = defineEmits([
    'select-component',
    'update-component',
    'delete-component',
    'drop',
    'create-component',
    'split-component',
    'select-form-child',
    'update-form-child',
    'delete-form-child',
    'reorder-form-children',
    'add-form-row',
    'select-form-row',
    'update-form-row',
    'delete-form-row',
    'reorder-form-rows',
    'split-form-column',
    'delete-form-column',
    'form-row-context-menu',
    'drop-to-column',
    'delete-column-form-child',
    'select-form-column',
    'move-form-child-between-columns',
    'start-adjust-position',
    'column-click-for-adjust',
    'delete-form-child-by-delete-key',
    'delete-form-row-by-delete-key',
    'update-components'
  ])

  // 拖拽相关状态
  const isDragging = ref(false)
  const isResizing = ref(false)
  const dragStartPos = ref({ x: 0, y: 0 })
  const dragStartComponentPos = ref({ x: 0, y: 0 })
  const resizeStartPos = ref({ x: 0, y: 0 })
  const resizeDirection = ref('')
  const currentDragComponent = ref(null)

  const resizeStartData = ref({
    x: 0,
    y: 0,
    width: 0,
    height: 0,
    isXPercent: false,
    isYPercent: false,
    isWidthPercent: false,
    isHeightPercent: false
  })

  // 参考线相关状态
  const showGuides = ref(false)
  const guides = ref({
    vertical: [], // 垂直参考线位置
    horizontal: [] // 水平参考线位置
  })

  // 右键菜单相关状态
  const contextMenu = ref({
    visible: false,
    x: 0,
    y: 0,
    clickPosition: { x: 0, y: 0 }, // 记录实际点击位置用于创建组件
    menuType: 'canvas', // 'canvas' | 'div-container'
    targetComponent: null // 右键点击的目标组件
  })

  // 过滤掉插槽子组件，只渲染独立组件
  const independentComponents = computed(() => {
    const filtered = props.components.filter(component => !component.config?.isSlotChild)

    // 确保子容器在父容器之后渲染，这样子容器会显示在父容器上面
    const sorted = filtered.sort((a, b) => {
      // 如果a是容器组，b是子容器，则a排在前面（先渲染）
      if (a.config?.isContainer && b.config?.isChildContainer) {
        return -1
      }
      // 如果a是子容器，b是容器组，则b排在前面（先渲染）
      if (a.config?.isChildContainer && b.config?.isContainer) {
        return 1
      }
      // 其他情况保持原顺序
      return 0
    })

    // 调试：检查渲染顺序
    const childContainers = sorted.filter(comp => comp.config?.isChildContainer)
    const containerGroups = sorted.filter(comp => comp.config?.isContainer)
    if (childContainers.length > 0 || containerGroups.length > 0) {
      console.log('🔍 EditorCanvas - 渲染顺序:', {
        selectedComponentId: props.selectedComponent?.id,
        containerGroups: containerGroups.map(c => ({ id: c.id, type: 'container-group' })),
        childContainers: childContainers.map(c => ({
          id: c.id,
          parentId: c.config?.parentId,
          type: 'child-container',
          isParentSelected: props.selectedComponent?.id === c.config?.parentId
        }))
      })
    }

    return sorted
  })

  const canvasGridStyle = computed(() => ({
    width: '100%',
    height: '100%',
    position: 'relative',
    minWidth: '300px',
    minHeight: '300px'
  }))

  // 坐标转换函数：将基于显示尺寸的坐标转换为基于实际画布尺寸的坐标
  const convertDisplayToActualCoordinates = (
    displayX,
    displayY,
    displayRect,
    scale = 1
  ) => {
    // 考虑缩放因子的坐标转换
    const actualDisplayX = displayX / scale
    const actualDisplayY = displayY / scale

    const scaleX = props.canvasSize.width / (displayRect.width / scale)
    const scaleY = props.canvasSize.height / (displayRect.height / scale)

    return {
      x: Math.round(actualDisplayX * scaleX),
      y: Math.round(actualDisplayY * scaleY)
    }
  }

  // 坐标转换函数：将基于实际画布尺寸的坐标转换为基于显示尺寸的坐标
  const convertActualToDisplayCoordinates = (
    actualX,
    actualY,
    displayRect,
    scale = 1
  ) => {
    const scaleX = displayRect.width / scale / props.canvasSize.width
    const scaleY = displayRect.height / scale / props.canvasSize.height

    return {
      x: actualX * scaleX * scale,
      y: actualY * scaleY * scale
    }
  }

  // 计算组件样式
  const getComponentStyle = component => {
    // 获取实际位置和尺寸 - 确保数值类型
    let actualX = Number(component.position.x) || 0
    let actualY = Number(component.position.y) || 0
    let actualWidth = Number(component.size.width) || 100
    let actualHeight = Number(component.size.height) || 100

    // 兼容旧的百分比格式
    if (
      typeof component.position.x === 'string' &&
      component.position.x.endsWith('%')
    ) {
      actualX =
        (parseFloat(component.position.x) * props.canvasSize.width) / 100
    }
    if (
      typeof component.position.y === 'string' &&
      component.position.y.endsWith('%')
    ) {
      actualY =
        (parseFloat(component.position.y) * props.canvasSize.height) / 100
    }
    if (
      typeof component.size.width === 'string' &&
      component.size.width.endsWith('%')
    ) {
      actualWidth =
        (parseFloat(component.size.width) * props.canvasSize.width) / 100
    }
    if (
      typeof component.size.height === 'string' &&
      component.size.height.endsWith('%')
    ) {
      actualHeight =
        (parseFloat(component.size.height) * props.canvasSize.height) / 100
    }

    // 支持100vw和100vh：如果宽度或高度设置为100vw/100vh，则铺满画布
    if (component.size.width === '100vw' && component.size.height === '100vh') {
      return {
        left: '0',
        top: '0',
        width: '100%',
        height: '100%',
        position: 'absolute'
      }
    }

    // 如果组件设置了铺满画布，则使用100%铺满画布容器
    if (component.fullCanvas) {
      return {
        left: '0',
        top: '0',
        width: '100%',
        height: '100%',
        position: 'absolute'
      }
    }

    // 使用CSS百分比来确保正确的缩放显示
    const leftPercent = (actualX / props.canvasSize.width) * 100
    const topPercent = (actualY / props.canvasSize.height) * 100
    const widthPercent = (actualWidth / props.canvasSize.width) * 100
    const heightPercent = (actualHeight / props.canvasSize.height) * 100

    return {
      left: `${leftPercent}%`,
      top: `${topPercent}%`,
      width: `${widthPercent}%`,
      height: `${heightPercent}%`,
      position: 'absolute'
    }
  }

  // 选择组件
  const selectComponent = component => {
    emit('select-component', component)
  }

  // 处理组件点击（选择组件并隐藏右键菜单）
  const handleComponentClick = component => {
    selectComponent(component)
    hideContextMenu()
  }

  // 处理画布点击
  const handleCanvasClick = () => {
    emit('select-component', null)
    hideContextMenu()
  }

  // 处理画布右键菜单
  const handleCanvasContextMenu = event => {
    // 只在画布空白区域显示右键菜单，不在组件上显示
    if (event.target.closest('.canvas-component')) {
      return
    }

    event.preventDefault()

    const canvasElement = document.querySelector('.canvas-grid')
    if (!canvasElement) return

    const rect = canvasElement.getBoundingClientRect()
    const displayX = event.clientX - rect.left
    const displayY = event.clientY - rect.top

    // 获取当前缩放比例并转换为实际画布坐标
    const scale = getCurrentScale()
    const actualCoords = convertDisplayToActualCoordinates(
      displayX,
      displayY,
      rect,
      scale
    )

    contextMenu.value = {
      visible: true,
      x: event.clientX,
      y: event.clientY,
      clickPosition: actualCoords,
      menuType: 'canvas',
      targetComponent: null
    }
  }

  // 处理组件右键菜单
  const handleComponentContextMenu = (component, event) => {
    // 支持DIV容器和表单容器的右键菜单
    if (!['div-container', 'form-container'].includes(component.type)) {
      return
    }

    event.preventDefault()
    event.stopPropagation()

    // 先选中组件
    selectComponent(component)

    const menuType =
      component.type === 'div-container' ? 'div-container' : 'form-container'

    contextMenu.value = {
      visible: true,
      x: event.clientX,
      y: event.clientY,
      clickPosition: { x: 0, y: 0 },
      menuType: menuType,
      targetComponent: component
    }
  }

  // 隐藏右键菜单
  const hideContextMenu = () => {
    contextMenu.value.visible = false
  }

  // 处理拆分DIV容器
  const handleSplitDiv = ({ component, direction, count = 2 }) => {
    // 创建分割后的子容器配置
    const gapSize = 8 // 容器间隙
    const timestamp = Date.now()

    let childComponents
    const parentPadding = component.config?.parentPadding || 12 // 与父容器的边距，从配置中获取
    const childGap = 8 // 子容器间的间隙

    // 如果父容器有标题栏，需要考虑标题栏高度
    const titleBarHeight = component.config?.showTitleBar
      ? component.config?.titleHeight || 32
      : 0

    // 获取实际的容器尺寸 - 如果是铺满画布，使用画布尺寸，否则使用组件尺寸
    let actualWidth, actualHeight, actualX, actualY
    if (component.fullCanvas) {
      actualWidth = props.canvasSize.width
      actualHeight = props.canvasSize.height
      actualX = 0
      actualY = 0
    } else {
      actualWidth = component.size.width
      actualHeight = component.size.height
      actualX = component.position.x
      actualY = component.position.y
    }

    // 动态生成子容器
    childComponents = []

    if (direction === 'horizontal') {
      // 水平拆分：每个子容器宽度计算 = (父容器宽度 - 2*父边距 - (count-1)*子间隙) / count
      const availableWidth =
        actualWidth - 2 * parentPadding - (count - 1) * childGap
      const childWidth = Math.floor(availableWidth / count)
      // 可用高度需要减去标题栏高度
      const availableHeight = actualHeight - 2 * parentPadding - titleBarHeight

      for (let i = 0; i < count; i++) {
        childComponents.push({
          id: `${component.id}-h${i + 1}-${timestamp}`,
          type: 'div-container',
          position: {
            x: actualX + parentPadding + i * (childWidth + childGap),
            y: actualY + parentPadding + titleBarHeight
          },
          size: {
            width: childWidth,
            height: availableHeight
          },
          config: {
            backgroundColor: '#f8f9fa',
            borderColor: '#e4e7ed',
            borderWidth: 1,
            borderStyle: 'dashed',
            borderRadius: 4,
            padding: 8,
            content: '',
            isChildContainer: true,
            parentId: component.id,
            childIndex: i,
            parentDirection: direction,
            parentPadding: parentPadding,
            childGap: childGap,
            // 标题栏相关配置
            showTitleBar: false,
            titleText: `子容器${i + 1}`,
            titleHeight: 32,
            titleBackgroundColor: '#f5f5f5',
            titleTextColor: '#303133',
            titleFontSize: 14,
            titleAlign: 'center',
            // 插槽相关配置
            hasSlotChild: false,
            slotChildId: undefined
          }
        })
      }
    } else {
      // 垂直拆分：每个子容器高度计算 = (父容器高度 - 2*父边距 - 标题栏高度 - (count-1)*子间隙) / count
      const availableWidth = actualWidth - 2 * parentPadding
      const availableHeight =
        actualHeight -
        2 * parentPadding -
        titleBarHeight -
        (count - 1) * childGap
      const childHeight = Math.floor(availableHeight / count)

      for (let i = 0; i < count; i++) {
        childComponents.push({
          id: `${component.id}-v${i + 1}-${timestamp}`,
          type: 'div-container',
          position: {
            x: actualX + parentPadding,
            y:
              actualY +
              parentPadding +
              titleBarHeight +
              i * (childHeight + childGap)
          },
          size: {
            width: availableWidth,
            height: childHeight
          },
          config: {
            backgroundColor: '#f8f9fa',
            borderColor: '#e4e7ed',
            borderWidth: 1,
            borderStyle: 'dashed',
            borderRadius: 4,
            padding: 8,
            content: '',
            isChildContainer: true,
            parentId: component.id,
            childIndex: i,
            parentDirection: direction,
            parentPadding: parentPadding,
            childGap: childGap,
            // 标题栏相关配置
            showTitleBar: false,
            titleText: `子容器${i + 1}`,
            titleHeight: 32,
            titleBackgroundColor: '#f5f5f5',
            titleTextColor: '#303133',
            titleFontSize: 14,
            titleAlign: 'center',
            // 插槽相关配置
            hasSlotChild: false,
            slotChildId: undefined
          }
        })
      }
    }

    // 对子容器进行碰撞检测和位置调整
    const canvasElement = document.querySelector('.canvas-grid')
    if (canvasElement) {
      const rect = canvasElement.getBoundingClientRect()

      childComponents.forEach(childComponent => {
        // 转换为显示坐标进行碰撞检测
        const displayCoords = convertActualToDisplayCoordinates(
          childComponent.position.x,
          childComponent.position.y,
          rect
        )
        const displayScaleX = rect.width / props.canvasSize.width
        const displayScaleY = rect.height / props.canvasSize.height
        const displayWidth = childComponent.size.width * displayScaleX
        const displayHeight = childComponent.size.height * displayScaleY

        // 排除父容器自身进行碰撞检测
        const tempComponents = props.components.filter(
          comp => comp.id !== component.id
        )
        const hasCollision = tempComponents.some(otherComp => {
          if (otherComp.type !== 'div-container') return false

          let otherActualX = otherComp.position.x
          let otherActualY = otherComp.position.y
          let otherActualWidth = otherComp.size.width
          let otherActualHeight = otherComp.size.height

          if (typeof otherActualX === 'string' && otherActualX.endsWith('%')) {
            otherActualX =
              (parseFloat(otherActualX) * props.canvasSize.width) / 100
          }
          if (typeof otherActualY === 'string' && otherActualY.endsWith('%')) {
            otherActualY =
              (parseFloat(otherActualY) * props.canvasSize.height) / 100
          }
          if (
            typeof otherActualWidth === 'string' &&
            otherActualWidth.endsWith('%')
          ) {
            otherActualWidth =
              (parseFloat(otherActualWidth) * props.canvasSize.width) / 100
          }
          if (
            typeof otherActualHeight === 'string' &&
            otherActualHeight.endsWith('%')
          ) {
            otherActualHeight =
              (parseFloat(otherActualHeight) * props.canvasSize.height) / 100
          }

          const childRect = {
            x: childComponent.position.x,
            y: childComponent.position.y,
            width: childComponent.size.width,
            height: childComponent.size.height
          }

          const otherRect = {
            x: otherActualX,
            y: otherActualY,
            width: otherActualWidth,
            height: otherActualHeight
          }

          return checkCollision(childRect, otherRect, 8)
        })

        if (hasCollision) {
          const validPosition = findValidPosition(
            childComponent,
            displayCoords.x,
            displayCoords.y,
            displayWidth,
            displayHeight
          )

          // 将有效位置转换回实际画布坐标
          const actualCoords = convertDisplayToActualCoordinates(
            validPosition.x,
            validPosition.y,
            rect
          )

          childComponent.position = {
            x: actualCoords.x,
            y: actualCoords.y
          }
        }
      })
    }

    // 处理父容器原有的插槽子组件
    let slotChildToTransfer = null
    if (component.config?.hasSlotChild && component.config?.slotChildId) {
      // 将插槽子组件转移到第一个子容器
      slotChildToTransfer = component.config.slotChildId
      if (childComponents.length > 0) {
        childComponents[0].config.hasSlotChild = true
        childComponents[0].config.slotChildId = slotChildToTransfer
      }
    }

    // 更新父容器为容器组（不再包含children，因为子容器现在是独立组件）
    const updatedComponent = {
      ...component,
      config: {
        ...component.config,
        isContainer: true,
        direction: direction, // 'horizontal' | 'vertical'
        gap: gapSize,
        childrenIds: childComponents.map(child => child.id), // 只记录子组件ID
        // 清除插槽子组件引用，因为已转移到子容器
        hasSlotChild: false,
        slotChildId: undefined
      }
    }

    emit('split-component', {
      parentComponent: updatedComponent,
      childComponents: childComponents,
      transferredSlotChildId: slotChildToTransfer // 传递转移的插槽子组件ID
    })
    hideContextMenu()
  }

  // 创建DIV容器
  const handleCreateDivContainer = () => {
    const initialPosition = {
      x: contextMenu.value.clickPosition.x,
      y: contextMenu.value.clickPosition.y
    }

    const newComponent = {
      id: `div-${Date.now()}`,
      type: 'div-container',
      position: initialPosition,
      size: {
        width: 200,
        height: 150
      },
      config: {
        backgroundColor: '#ffffff',
        borderColor: '#e4e7ed',
        borderWidth: 1,
        borderStyle: 'solid',
        borderRadius: 4,
        padding: 8,
        content: '',
        // 标题栏相关配置
        showTitleBar: false,
        titleText: '标题',
        titleHeight: 32,
        titleBackgroundColor: '#f5f5f5',
        titleTextColor: '#303133',
        titleFontSize: 14,
        titleAlign: 'center',
        // 拆分配置
        parentPadding: 12,
        // 插槽相关配置
        hasSlotChild: false,
        slotChildId: undefined
      }
    }

    // 检查初始位置是否会碰撞，如果会则寻找合适位置
    const canvasElement = document.querySelector('.canvas-grid')
    if (canvasElement) {
      const rect = canvasElement.getBoundingClientRect()

      // 转换为显示坐标进行碰撞检测
      const displayCoords = convertActualToDisplayCoordinates(
        initialPosition.x,
        initialPosition.y,
        rect
      )
      const displayScaleX = rect.width / props.canvasSize.width
      const displayScaleY = rect.height / props.canvasSize.height
      const displayWidth = newComponent.size.width * displayScaleX
      const displayHeight = newComponent.size.height * displayScaleY

      if (
        checkDivContainerCollisions(
          newComponent,
          displayCoords.x,
          displayCoords.y,
          displayWidth,
          displayHeight
        )
      ) {
        const validPosition = findValidPosition(
          newComponent,
          displayCoords.x,
          displayCoords.y,
          displayWidth,
          displayHeight
        )

        // 将有效位置转换回实际画布坐标
        const actualCoords = convertDisplayToActualCoordinates(
          validPosition.x,
          validPosition.y,
          rect
        )

        newComponent.position = {
          x: actualCoords.x,
          y: actualCoords.y
        }
      }
    }

    emit('create-component', newComponent)
    hideContextMenu()
  }

  // 添加表单行
  const handleAddFormRow = ({ component }) => {
    emit('add-form-row', { component })
    hideContextMenu()
  }

  // 处理表单行右键菜单
  const handleFormRowContextMenu = ({ row, x, y }) => {
    contextMenu.value = {
      visible: true,
      x: x,
      y: y,
      clickPosition: { x: 0, y: 0 },
      menuType: 'form-row',
      targetComponent: row
    }
  }

  // 处理表单列右键菜单
  const handleFormColumnContextMenu = ({
    column,
    row,
    formContainer,
    x,
    y
  }) => {
    contextMenu.value = {
      visible: true,
      x: x,
      y: y,
      clickPosition: { x: 0, y: 0 },
      menuType: 'form-column',
      targetComponent: { column, row, formContainer }
    }
  }

  // 拆分表单列
  const handleSplitFormColumn = ({ component, columns }) => {
    emit('split-form-column', { component, columns })
    hideContextMenu()
  }

  // 删除表单列
  const handleDeleteFormColumn = ({ column, row, formContainer }) => {
    emit('delete-form-column', { column, row, formContainer })
    hideContextMenu()
  }

  // 检测拖拽位置是否在容器内部
  const findDropTargetContainer = (x, y) => {
    console.log('🔍 findDropTargetContainer 查找目标容器:', { x, y })

    // 查找所有可以作为目标容器的组件
    // DIV容器：排除容器组（但包括子DIV容器）
    // 表单容器：支持拖拽表单元素，但排除子容器
    const targetContainers = props.components.filter(comp => {
      if (comp.type === 'div-container') {
        // DIV容器可以接收拖拽，包括子DIV容器
        // 只排除容器组（isContainer为true的父容器）
        const canReceive = !comp.config?.isContainer
        console.log(
          '🔍 DIV容器检查:',
          comp.id,
          comp.type,
          'canReceive:',
          canReceive
        )
        return canReceive
      }
      if (comp.type === 'form-container') {
        // 表单容器排除子容器
        const canReceive = !comp.config?.isChildContainer
        console.log(
          '🔍 表单容器检查:',
          comp.id,
          comp.type,
          'canReceive:',
          canReceive
        )
        return canReceive
      }
      return false
    })

    console.log(
      '🔍 可接收拖拽的容器:',
      targetContainers.map(c => ({ id: c.id, type: c.type }))
    )

    // 按面积从小到大排序，优先选择最小的容器（更精确的定位）
    const sortedContainers = targetContainers
      .map(container => {
        // 获取容器的实际位置和尺寸（处理百分比格式）
        let actualX = container.position.x
        let actualY = container.position.y
        let actualWidth = container.size.width
        let actualHeight = container.size.height

        // 兼容旧的百分比格式，转换为实际像素
        if (typeof actualX === 'string' && actualX.endsWith('%')) {
          actualX = (parseFloat(actualX) * props.canvasSize.width) / 100
        }
        if (typeof actualY === 'string' && actualY.endsWith('%')) {
          actualY = (parseFloat(actualY) * props.canvasSize.height) / 100
        }
        if (typeof actualWidth === 'string' && actualWidth.endsWith('%')) {
          actualWidth = (parseFloat(actualWidth) * props.canvasSize.width) / 100
        }
        if (typeof actualHeight === 'string' && actualHeight.endsWith('%')) {
          actualHeight =
            (parseFloat(actualHeight) * props.canvasSize.height) / 100
        }

        const rect = {
          x: actualX,
          y: actualY,
          width: actualWidth,
          height: actualHeight
        }

        console.log('🔍 容器矩形:', container.id, rect)

        return {
          container,
          area: actualWidth * actualHeight,
          rect
        }
      })
      .filter(({ rect }) => {
        // 检查拖拽点是否在容器内部
        const isInside =
          x >= rect.x &&
          x <= rect.x + rect.width &&
          y >= rect.y &&
          y <= rect.y + rect.height
        console.log(
          '🔍 拖拽点是否在容器内:',
          { x, y },
          rect,
          'inside:',
          isInside
        )
        return isInside
      })
      .sort((a, b) => a.area - b.area)

    const result =
      sortedContainers.length > 0 ? sortedContainers[0].container : null
    console.log(
      '🔍 最终选择的目标容器:',
      result ? { id: result.id, type: result.type } : null
    )

    return result
  }

  // 拖拽悬停状态
  const dragOverContainer = ref(null)

  // 处理拖拽经过
  const handleDragOver = event => {
    event.preventDefault()

    console.log('🎯 EditorCanvas handleDragOver')

    const canvasElement = document.querySelector('.canvas-grid')
    if (!canvasElement) return

    const rect = canvasElement.getBoundingClientRect()
    const displayX = event.clientX - rect.left
    const displayY = event.clientY - rect.top

    // 获取当前缩放比例并转换为实际画布坐标
    const scale = getCurrentScale()
    console.log('🎯 当前缩放比例:', scale)

    const actualCoords = convertDisplayToActualCoordinates(
      displayX,
      displayY,
      rect,
      scale
    )

    console.log('🎯 拖拽坐标:', { displayX, displayY, actualCoords, scale })

    // 检查是否拖拽到容器内部（DIV容器或表单容器）
    const targetContainer = findDropTargetContainer(
      actualCoords.x,
      actualCoords.y
    )

    console.log('🎯 目标容器:', targetContainer)

    if (targetContainer !== dragOverContainer.value) {
      // 清除之前的悬停状态
      if (dragOverContainer.value) {
        const prevEl = document.querySelector(
          `[data-component-id="${dragOverContainer.value.id}"]`
        )
        if (prevEl) {
          prevEl.classList.remove('drag-over-container')
        }
      }

      // 设置新的悬停状态
      dragOverContainer.value = targetContainer
      if (targetContainer) {
        const containerEl = document.querySelector(
          `[data-component-id="${targetContainer.id}"]`
        )
        if (containerEl) {
          containerEl.classList.add('drag-over-container')
          console.log('🎯 添加悬停样式到容器:', targetContainer.type)
        }
      }
    }
  }

  // 处理拖拽离开
  const handleDragLeave = event => {
    // 只有当拖拽真正离开画布时才清除悬停状态
    const canvasElement = document.querySelector('.canvas-grid')
    if (!canvasElement) return

    const rect = canvasElement.getBoundingClientRect()
    const x = event.clientX
    const y = event.clientY

    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      if (dragOverContainer.value) {
        const containerEl = document.querySelector(
          `[data-component-id="${dragOverContainer.value.id}"]`
        )
        if (containerEl) {
          containerEl.classList.remove('drag-over-container')
        }
        dragOverContainer.value = null
      }
    }
  }

  // 处理拖拽放置
  const handleDrop = event => {
    event.preventDefault()
    event.stopPropagation()

    console.log('🌟 EditorCanvas handleDrop 被调用')

    // 清除悬停状态
    if (dragOverContainer.value) {
      const containerEl = document.querySelector(
        `[data-component-id="${dragOverContainer.value.id}"]`
      )
      if (containerEl) {
        containerEl.classList.remove('drag-over-container')
      }
      dragOverContainer.value = null
    }

    const canvasElement = document.querySelector('.canvas-grid')
    if (!canvasElement) return

    const rect = canvasElement.getBoundingClientRect()
    const displayX = event.clientX - rect.left
    const displayY = event.clientY - rect.top

    // 获取当前缩放比例并转换为实际画布坐标
    const scale = getCurrentScale()
    console.log('🌟 放置时缩放比例:', scale)

    const actualCoords = convertDisplayToActualCoordinates(
      displayX,
      displayY,
      rect,
      scale
    )

    console.log('🌟 放置坐标:', { displayX, displayY, actualCoords, scale })

    // 检查是否拖拽到容器内部（DIV容器或表单容器）
    const targetContainer = findDropTargetContainer(
      actualCoords.x,
      actualCoords.y
    )

    console.log('🌟 放置目标容器:', targetContainer)

    emit('drop', {
      ...actualCoords,
      targetContainer: targetContainer
    })
  }

  // 获取容器的所有子孙组件
  const getAllChildComponents = parentComponent => {
    const childComponents = []

    if (
      parentComponent.config?.isContainer &&
      parentComponent.config?.childrenIds
    ) {
      parentComponent.config.childrenIds.forEach(childId => {
        const child = props.components.find(comp => comp.id === childId)
        if (child) {
          childComponents.push(child)
          // 递归获取子组件的子组件
          childComponents.push(...getAllChildComponents(child))
        }
      })
    }

    return childComponents
  }

  // 开始拖拽组件
  const startDrag = (component, event) => {
    // 如果组件设置了铺满画布，则不允许拖动
    if (component.fullCanvas) return

    // 如果是子容器，禁用位置拖拽
    if (component.config?.isChildContainer) return

    // 如果是插槽子组件，禁用拖拽
    if (component.config?.isSlotChild) return

    if (isResizing.value) return

    isDragging.value = true
    currentDragComponent.value = component
    dragStartPos.value = { x: event.clientX, y: event.clientY }

    const canvasElement = document.querySelector('.canvas-grid')
    if (!canvasElement) return

    const rect = canvasElement.getBoundingClientRect()

    // 获取组件的实际位置，然后转换为显示坐标
    let actualPosX = component.position.x
    let actualPosY = component.position.y

    // 兼容旧的百分比格式，先转换为实际像素
    if (typeof actualPosX === 'string' && actualPosX.endsWith('%')) {
      actualPosX = (parseFloat(actualPosX) * props.canvasSize.width) / 100
    }

    if (typeof actualPosY === 'string' && actualPosY.endsWith('%')) {
      actualPosY = (parseFloat(actualPosY) * props.canvasSize.height) / 100
    }

    // 转换为显示坐标用于拖拽计算
    const displayCoords = convertActualToDisplayCoordinates(
      actualPosX,
      actualPosY,
      rect
    )

    dragStartComponentPos.value = {
      x: displayCoords.x,
      y: displayCoords.y
    }

    // 计算参考线位置
    const guidePositions = calculateGuidePositions(component)
    guides.value = guidePositions
    showGuides.value = true

    document.addEventListener('mousemove', handleDragMove)
    document.addEventListener('mouseup', handleDragEnd)
  }

  // 碰撞检测函数 - 检测两个矩形是否重合（考虑间距）
  const checkCollision = (rect1, rect2, minGap = 8) => {
    // 扩展检测区域，包含最小间距
    const expandedRect1 = {
      x: rect1.x - minGap,
      y: rect1.y - minGap,
      width: rect1.width + minGap * 2,
      height: rect1.height + minGap * 2
    }

    return !(
      expandedRect1.x >= rect2.x + rect2.width ||
      expandedRect1.x + expandedRect1.width <= rect2.x ||
      expandedRect1.y >= rect2.y + rect2.height ||
      expandedRect1.y + expandedRect1.height <= rect2.y
    )
  }

  // 检测DIV容器碰撞
  const checkDivContainerCollisions = (
    component,
    newX,
    newY,
    newWidth = null,
    newHeight = null
  ) => {
    // 只对DIV容器进行碰撞检测
    if (component.type !== 'div-container') return false

    const canvasElement = document.querySelector('.canvas-grid')
    if (!canvasElement) return false

    const rect = canvasElement.getBoundingClientRect()

    // 转换为实际画布坐标进行计算
    const actualCoords = convertDisplayToActualCoordinates(newX, newY, rect)
    const displayScaleX = rect.width / props.canvasSize.width
    const displayScaleY = rect.height / props.canvasSize.height

    // 计算当前组件的实际尺寸
    let actualWidth = newWidth ? newWidth / displayScaleX : component.size.width
    let actualHeight = newHeight
      ? newHeight / displayScaleY
      : component.size.height

    const currentRect = {
      x: actualCoords.x,
      y: actualCoords.y,
      width: actualWidth,
      height: actualHeight
    }

    // 检测与其他DIV容器的碰撞
    const minGap = 8 // 最小间距8px
    for (const otherComponent of props.components) {
      if (
        otherComponent.id === component.id ||
        otherComponent.type !== 'div-container'
      ) {
        continue
      }

      // 获取其他组件的实际坐标和尺寸
      let otherActualX = otherComponent.position.x
      let otherActualY = otherComponent.position.y
      let otherActualWidth = otherComponent.size.width
      let otherActualHeight = otherComponent.size.height

      // 兼容旧的百分比格式
      if (typeof otherActualX === 'string' && otherActualX.endsWith('%')) {
        otherActualX = (parseFloat(otherActualX) * props.canvasSize.width) / 100
      }
      if (typeof otherActualY === 'string' && otherActualY.endsWith('%')) {
        otherActualY =
          (parseFloat(otherActualY) * props.canvasSize.height) / 100
      }
      if (
        typeof otherActualWidth === 'string' &&
        otherActualWidth.endsWith('%')
      ) {
        otherActualWidth =
          (parseFloat(otherActualWidth) * props.canvasSize.width) / 100
      }
      if (
        typeof otherActualHeight === 'string' &&
        otherActualHeight.endsWith('%')
      ) {
        otherActualHeight =
          (parseFloat(otherActualHeight) * props.canvasSize.height) / 100
      }

      const otherRect = {
        x: otherActualX,
        y: otherActualY,
        width: otherActualWidth,
        height: otherActualHeight
      }

      if (checkCollision(currentRect, otherRect, minGap)) {
        return true
      }
    }

    return false
  }

  // 寻找最近的有效位置
  const findValidPosition = (
    component,
    targetX,
    targetY,
    targetWidth = null,
    targetHeight = null
  ) => {
    if (component.type !== 'div-container') {
      return { x: targetX, y: targetY }
    }

    const searchRadius = 50 // 搜索半径
    const step = 8 // 步长，与最小间距保持一致

    // 首先检查目标位置是否有效
    if (
      !checkDivContainerCollisions(
        component,
        targetX,
        targetY,
        targetWidth,
        targetHeight
      )
    ) {
      return { x: targetX, y: targetY }
    }

    // 螺旋搜索算法寻找最近的有效位置
    let bestDistance = Infinity
    let bestPosition = { x: targetX, y: targetY }

    for (let radius = step; radius <= searchRadius; radius += step) {
      // 在当前半径的圆周上搜索
      const steps = Math.max(8, Math.floor((2 * Math.PI * radius) / step))
      for (let i = 0; i < steps; i++) {
        const angle = (i / steps) * 2 * Math.PI
        const testX = Math.max(0, targetX + Math.cos(angle) * radius)
        const testY = Math.max(0, targetY + Math.sin(angle) * radius)

        if (
          !checkDivContainerCollisions(
            component,
            testX,
            testY,
            targetWidth,
            targetHeight
          )
        ) {
          const distance = Math.sqrt(
            Math.pow(testX - targetX, 2) + Math.pow(testY - targetY, 2)
          )
          if (distance < bestDistance) {
            bestDistance = distance
            bestPosition = { x: testX, y: testY }
          }
        }
      }

      // 如果找到了有效位置，直接返回
      if (bestDistance < Infinity) {
        break
      }
    }

    return bestPosition
  }

  // 拖拽移动
  const handleDragMove = event => {
    if (!isDragging.value || !currentDragComponent.value) return

    const canvasElement = document.querySelector('.canvas-grid')
    if (!canvasElement) return

    const deltaX = event.clientX - dragStartPos.value.x
    const deltaY = event.clientY - dragStartPos.value.y

    let newX = dragStartComponentPos.value.x + deltaX
    let newY = dragStartComponentPos.value.y + deltaY

    // 获取画布边界限制
    const rect = canvasElement.getBoundingClientRect()
    const component = currentDragComponent.value

    // 获取组件尺寸（显示坐标）
    let componentWidth = component.size.width
    let componentHeight = component.size.height



    // 如果是百分比，转换为像素
    if (typeof componentWidth === 'string' && componentWidth.endsWith('%')) {
      componentWidth = (parseFloat(componentWidth) * rect.width) / 100
    } else {
      // 确保是数字类型，然后转换实际尺寸到显示尺寸
      const actualWidth = typeof componentWidth === 'number' ? componentWidth : Number(componentWidth) || 100
      componentWidth = (actualWidth * rect.width) / props.canvasSize.width
    }

    if (typeof componentHeight === 'string' && componentHeight.endsWith('%')) {
      componentHeight = (parseFloat(componentHeight) * rect.height) / 100
    } else {
      // 确保是数字类型，然后转换实际尺寸到显示尺寸
      const actualHeight = typeof componentHeight === 'number' ? componentHeight : Number(componentHeight) || 100
      componentHeight = (actualHeight * rect.height) / props.canvasSize.height
    }



    // 吸附到参考线（转换参考线位置到显示坐标）
    const snapThreshold = 5

    guides.value.vertical.forEach(guidePos => {
      // 将实际坐标的参考线转换为显示坐标
      const displayGuidePos = (guidePos * rect.width) / props.canvasSize.width
      if (Math.abs(newX - displayGuidePos) < snapThreshold) {
        newX = displayGuidePos
      }
      // 也检查组件右边界和中心线的吸附
      if (Math.abs((newX + componentWidth) - displayGuidePos) < snapThreshold) {
        newX = displayGuidePos - componentWidth
      }
      if (Math.abs((newX + componentWidth/2) - displayGuidePos) < snapThreshold) {
        newX = displayGuidePos - componentWidth/2
      }
    })

    guides.value.horizontal.forEach(guidePos => {
      // 将实际坐标的参考线转换为显示坐标
      const displayGuidePos = (guidePos * rect.height) / props.canvasSize.height
      if (Math.abs(newY - displayGuidePos) < snapThreshold) {
        newY = displayGuidePos
      }
      // 也检查组件下边界和中心线的吸附
      if (Math.abs((newY + componentHeight) - displayGuidePos) < snapThreshold) {
        newY = displayGuidePos - componentHeight
      }
      if (Math.abs((newY + componentHeight/2) - displayGuidePos) < snapThreshold) {
        newY = displayGuidePos - componentHeight/2
      }
    })

    // 最终确保在画布边界内（吸附后可能超出边界）
    const maxX = rect.width - componentWidth
    const maxY = rect.height - componentHeight



    newX = Math.max(0, Math.min(newX, maxX))
    newY = Math.max(0, Math.min(newY, maxY))

    // 对DIV容器进行碰撞检测和位置调整
    if (currentDragComponent.value.type === 'div-container') {
      const validPosition = findValidPosition(
        currentDragComponent.value,
        newX,
        newY
      )
      newX = validPosition.x
      newY = validPosition.y
    }

    // 实时更新组件位置（仅用于视觉反馈，不修改props）
    const componentEl = document.querySelector(
      `[data-component-id="${currentDragComponent.value.id}"]`
    )
    if (componentEl) {
      // 转换显示坐标为百分比，保持与正常渲染一致的坐标系
      const actualX = (newX * props.canvasSize.width) / rect.width
      const actualY = (newY * props.canvasSize.height) / rect.height
      const leftPercent = (actualX / props.canvasSize.width) * 100
      const topPercent = (actualY / props.canvasSize.height) * 100

      componentEl.style.left = `${leftPercent}%`
      componentEl.style.top = `${topPercent}%`

      // 如果发生了碰撞检测调整，添加视觉提示
      if (
        currentDragComponent.value.type === 'div-container' &&
        checkDivContainerCollisions(
          currentDragComponent.value,
          dragStartComponentPos.value.x + deltaX,
          dragStartComponentPos.value.y + deltaY
        )
      ) {
        componentEl.style.boxShadow =
          '0 0 0 2px #f56c6c, 0 0 10px rgba(245, 108, 108, 0.3)'
      } else {
        componentEl.style.boxShadow = ''
      }
    }
  }

  // 拖拽结束
  const handleDragEnd = event => {
    if (!isDragging.value || !currentDragComponent.value) return

    const canvasElement = document.querySelector('.canvas-grid')
    if (canvasElement) {
      const rect = canvasElement.getBoundingClientRect()
      const deltaX = event.clientX - dragStartPos.value.x
      const deltaY = event.clientY - dragStartPos.value.y

      let newDisplayX = dragStartComponentPos.value.x + deltaX
      let newDisplayY = dragStartComponentPos.value.y + deltaY

      // 获取组件尺寸（显示坐标）
      const component = currentDragComponent.value
      let componentWidth = component.size.width
      let componentHeight = component.size.height

      // 如果是百分比，转换为像素
      if (typeof componentWidth === 'string' && componentWidth.endsWith('%')) {
        componentWidth = (parseFloat(componentWidth) * rect.width) / 100
      } else {
        // 转换实际尺寸到显示尺寸
        componentWidth = (componentWidth * rect.width) / props.canvasSize.width
      }

      if (typeof componentHeight === 'string' && componentHeight.endsWith('%')) {
        componentHeight = (parseFloat(componentHeight) * rect.height) / 100
      } else {
        // 转换实际尺寸到显示尺寸
        componentHeight = (componentHeight * rect.height) / props.canvasSize.height
      }

      // 吸附到参考线（转换参考线位置到显示坐标）
      const snapThreshold = 5

      guides.value.vertical.forEach(guidePos => {
        // 将实际坐标的参考线转换为显示坐标
        const displayGuidePos = (guidePos * rect.width) / props.canvasSize.width
        if (Math.abs(newDisplayX - displayGuidePos) < snapThreshold) {
          newDisplayX = displayGuidePos
        }
        // 也检查组件右边界和中心线的吸附
        if (Math.abs((newDisplayX + componentWidth) - displayGuidePos) < snapThreshold) {
          newDisplayX = displayGuidePos - componentWidth
        }
        if (Math.abs((newDisplayX + componentWidth/2) - displayGuidePos) < snapThreshold) {
          newDisplayX = displayGuidePos - componentWidth/2
        }
      })

      guides.value.horizontal.forEach(guidePos => {
        // 将实际坐标的参考线转换为显示坐标
        const displayGuidePos = (guidePos * rect.height) / props.canvasSize.height
        if (Math.abs(newDisplayY - displayGuidePos) < snapThreshold) {
          newDisplayY = displayGuidePos
        }
        // 也检查组件下边界和中心线的吸附
        if (Math.abs((newDisplayY + componentHeight) - displayGuidePos) < snapThreshold) {
          newDisplayY = displayGuidePos - componentHeight
        }
        if (Math.abs((newDisplayY + componentHeight/2) - displayGuidePos) < snapThreshold) {
          newDisplayY = displayGuidePos - componentHeight/2
        }
      })

      // 最终确保在画布边界内（吸附后可能超出边界）
      const maxDisplayX = rect.width - componentWidth
      const maxDisplayY = rect.height - componentHeight



      newDisplayX = Math.max(0, Math.min(newDisplayX, maxDisplayX))
      newDisplayY = Math.max(0, Math.min(newDisplayY, maxDisplayY))

      // 对DIV容器进行最终的碰撞检测
      if (currentDragComponent.value.type === 'div-container') {
        const validPosition = findValidPosition(
          currentDragComponent.value,
          newDisplayX,
          newDisplayY
        )
        newDisplayX = validPosition.x
        newDisplayY = validPosition.y
      }

      // 转换为实际画布坐标后保存
      const actualCoords = convertDisplayToActualCoordinates(
        newDisplayX,
        newDisplayY,
        rect
      )

      // 计算移动的距离（实际画布坐标）
      const moveDistanceX =
        actualCoords.x - currentDragComponent.value.position.x
      const moveDistanceY =
        actualCoords.y - currentDragComponent.value.position.y

      // 更新父容器位置
      emit('update-component', {
        ...currentDragComponent.value,
        position: {
          x: actualCoords.x,
          y: actualCoords.y
        }
      })

      // 如果是容器组，同步移动所有子组件
      if (currentDragComponent.value.config?.isContainer) {
        const childComponents = getAllChildComponents(
          currentDragComponent.value
        )
        childComponents.forEach(childComponent => {
          emit('update-component', {
            ...childComponent,
            position: {
              x: childComponent.position.x + moveDistanceX,
              y: childComponent.position.y + moveDistanceY
            }
          })
        })
      }
    }

    // 清除碰撞检测的视觉提示
    const componentEl = document.querySelector(
      `[data-component-id="${currentDragComponent.value.id}"]`
    )
    if (componentEl) {
      componentEl.style.boxShadow = ''
    }

    isDragging.value = false
    currentDragComponent.value = null
    showGuides.value = false
    document.removeEventListener('mousemove', handleDragMove)
    document.removeEventListener('mouseup', handleDragEnd)
  }

  // 开始调整尺寸
  const startResize = (component, direction, event) => {
    if (component.fullCanvas) return
    isResizing.value = true
    currentDragComponent.value = component
    resizeDirection.value = direction
    resizeStartPos.value = { x: event.clientX, y: event.clientY }

    const canvasElement = document.querySelector('.canvas-grid')
    if (!canvasElement) return
    const rect = canvasElement.getBoundingClientRect()

    // 获取组件的实际位置和尺寸，然后转换为显示坐标
    let actualPosX = component.position.x
    let actualPosY = component.position.y
    let actualWidth = component.size.width
    let actualHeight = component.size.height

    // 兼容旧的百分比格式，先转换为实际像素
    if (typeof actualPosX === 'string' && actualPosX.endsWith('%')) {
      actualPosX = (parseFloat(actualPosX) * props.canvasSize.width) / 100
    }

    if (typeof actualPosY === 'string' && actualPosY.endsWith('%')) {
      actualPosY = (parseFloat(actualPosY) * props.canvasSize.height) / 100
    }

    if (typeof actualWidth === 'string' && actualWidth.endsWith('%')) {
      actualWidth = (parseFloat(actualWidth) * props.canvasSize.width) / 100
    }

    if (typeof actualHeight === 'string' && actualHeight.endsWith('%')) {
      actualHeight = (parseFloat(actualHeight) * props.canvasSize.height) / 100
    }

    // 转换为显示坐标用于调整尺寸计算
    const displayCoords = convertActualToDisplayCoordinates(
      actualPosX,
      actualPosY,
      rect
    )
    const displayScaleX = rect.width / props.canvasSize.width
    const displayScaleY = rect.height / props.canvasSize.height

    resizeStartData.value = {
      x: displayCoords.x,
      y: displayCoords.y,
      width: actualWidth * displayScaleX,
      height: actualHeight * displayScaleY
    }

    console.log('🔧 调整大小开始数据:', {
      componentId: component.id,
      actualPos: { x: actualPosX, y: actualPosY },
      actualSize: { width: actualWidth, height: actualHeight },
      displayCoords,
      displayScale: { x: displayScaleX, y: displayScaleY },
      resizeStartData: resizeStartData.value
    })

    document.addEventListener('mousemove', handleResizeMove)
    document.addEventListener('mouseup', handleResizeEnd)
  }

  const calculateNewDimensions = (deltaX, deltaY) => {
    let { x, y, width, height } = resizeStartData.value

    if (resizeDirection.value.includes('e')) {
      width += deltaX
    }
    if (resizeDirection.value.includes('s')) {
      height += deltaY
    }
    if (resizeDirection.value.includes('w')) {
      width -= deltaX
      x += deltaX
    }
    if (resizeDirection.value.includes('n')) {
      height -= deltaY
      y += deltaY
    }

    // 获取画布边界限制
    const canvasElement = document.querySelector('.canvas-grid')
    if (canvasElement) {
      const rect = canvasElement.getBoundingClientRect()

      // 限制最小尺寸
      if (width < 50) {
        if (resizeDirection.value.includes('w')) x += width - 50
        width = 50
      }
      if (height < 50) {
        if (resizeDirection.value.includes('n')) y += height - 50
        height = 50
      }

      // 限制在画布边界内
      if (x < 0) {
        width += x
        x = 0
      }
      if (y < 0) {
        height += y
        y = 0
      }
      if (x + width > rect.width) {
        width = rect.width - x
      }
      if (y + height > rect.height) {
        height = rect.height - y
      }
    } else {
      // 回退到原有逻辑
      if (width < 50) {
        if (resizeDirection.value.includes('w')) x += width - 50
        width = 50
      }
      if (height < 50) {
        if (resizeDirection.value.includes('n')) y += height - 50
        height = 50
      }
    }

    return { newX: x, newY: y, newWidth: width, newHeight: height }
  }

  // 调整尺寸移动
  const handleResizeMove = event => {
    if (!isResizing.value || !currentDragComponent.value) return

    const deltaX = event.clientX - resizeStartPos.value.x
    const deltaY = event.clientY - resizeStartPos.value.y
    let { newX, newY, newWidth, newHeight } = calculateNewDimensions(
      deltaX,
      deltaY
    )

    console.log('🔧 调整大小移动:', {
      delta: { x: deltaX, y: deltaY },
      newDimensions: { x: newX, y: newY, width: newWidth, height: newHeight },
      resizeDirection: resizeDirection.value
    })

    // 对DIV容器进行碰撞检测和尺寸调整
    if (currentDragComponent.value.type === 'div-container') {
      // 检查调整后的尺寸是否会导致碰撞
      if (
        checkDivContainerCollisions(
          currentDragComponent.value,
          newX,
          newY,
          newWidth,
          newHeight
        )
      ) {
        // 如果会碰撞，尝试寻找有效的调整
        const validPosition = findValidPosition(
          currentDragComponent.value,
          newX,
          newY,
          newWidth,
          newHeight
        )

        // 如果找不到合适的位置，则限制调整
        if (validPosition.x !== newX || validPosition.y !== newY) {
          // 尝试只调整尺寸，不改变位置
          if (
            !checkDivContainerCollisions(
              currentDragComponent.value,
              resizeStartData.value.x,
              resizeStartData.value.y,
              newWidth,
              newHeight
            )
          ) {
            newX = resizeStartData.value.x
            newY = resizeStartData.value.y
          } else {
            // 如果调整尺寸也会碰撞，则保持原始尺寸和位置
            newX = resizeStartData.value.x
            newY = resizeStartData.value.y
            newWidth = resizeStartData.value.width
            newHeight = resizeStartData.value.height
          }
        } else {
          newX = validPosition.x
          newY = validPosition.y
        }
      }
    }

    const componentEl = document.querySelector(
      `[data-component-id="${currentDragComponent.value.id}"]`
    )
    if (componentEl) {
      // 获取画布信息用于坐标转换
      const canvasElement = document.querySelector('.canvas-grid')
      if (canvasElement) {
        const rect = canvasElement.getBoundingClientRect()

        // 转换显示坐标为实际坐标，然后转换为百分比
        const actualX = (newX * props.canvasSize.width) / rect.width
        const actualY = (newY * props.canvasSize.height) / rect.height
        const actualWidth = (newWidth * props.canvasSize.width) / rect.width
        const actualHeight = (newHeight * props.canvasSize.height) / rect.height

        const leftPercent = (actualX / props.canvasSize.width) * 100
        const topPercent = (actualY / props.canvasSize.height) * 100
        const widthPercent = (actualWidth / props.canvasSize.width) * 100
        const heightPercent = (actualHeight / props.canvasSize.height) * 100

        componentEl.style.left = `${leftPercent}%`
        componentEl.style.top = `${topPercent}%`
        componentEl.style.width = `${widthPercent}%`
        componentEl.style.height = `${heightPercent}%`
      }

      // 添加碰撞提示
      if (currentDragComponent.value.type === 'div-container') {
        const originalDimensions = calculateNewDimensions(deltaX, deltaY)
        const hasCollision = checkDivContainerCollisions(
          currentDragComponent.value,
          originalDimensions.newX,
          originalDimensions.newY,
          originalDimensions.newWidth,
          originalDimensions.newHeight
        )

        if (hasCollision) {
          componentEl.style.boxShadow =
            '0 0 0 2px #f56c6c, 0 0 10px rgba(245, 108, 108, 0.3)'
        } else {
          componentEl.style.boxShadow = ''
        }
      }
    }
  }

  // 获取兄弟容器
  const getSiblingContainer = childComponent => {
    if (
      !childComponent.config?.isChildContainer ||
      !childComponent.config?.parentId
    )
      return null

    const parentId = childComponent.config.parentId
    const parent = props.components.find(comp => comp.id === parentId)

    if (!parent || !parent.config?.childrenIds) return null

    const siblingId = parent.config.childrenIds.find(
      id => id !== childComponent.id
    )
    return props.components.find(comp => comp.id === siblingId)
  }

  // 父容器尺寸调整时，递归适配所有层级的子容器（包括孙容器、曾孙容器等）
  const adaptChildContainersSize = (parentContainer, newWidth, newHeight) => {
    if (
      !parentContainer.config?.childrenIds ||
      parentContainer.config.childrenIds.length === 0
    ) {
      return []
    }

    const childComponents = parentContainer.config.childrenIds
      .map(childId => props.components.find(comp => comp.id === childId))
      .filter(Boolean)

    if (childComponents.length === 0) {
      return []
    }

    const parentPadding = parentContainer.config?.parentPadding || 12 // 与父容器的边距，从配置中获取
    const childGap = 8 // 子容器间的间隙
    const direction = parentContainer.config.direction
    const allUpdatedComponents = []

    if (direction === 'horizontal') {
      // 左右布局：重新计算子容器的宽度和位置
      const availableWidth = newWidth - 2 * parentPadding - childGap
      const childWidth = Math.floor(availableWidth / 2)
      const availableHeight = newHeight - 2 * parentPadding

      childComponents.forEach((child, index) => {
        // 获取父容器的实际位置
        const parentActualX = parentContainer.fullCanvas
          ? 0
          : parentContainer.position.x
        const parentActualY = parentContainer.fullCanvas
          ? 0
          : parentContainer.position.y

        const updatedChild = {
          ...child,
          position: {
            x: parentActualX + parentPadding + index * (childWidth + childGap),
            y: parentActualY + parentPadding
          },
          size: {
            width: childWidth,
            height: availableHeight
          },
          config: {
            ...child.config,
            parentPadding: parentPadding,
            childGap: childGap
          }
        }
        allUpdatedComponents.push(updatedChild)

        // 递归处理子容器的子容器（孙容器）
        if (
          updatedChild.config?.isContainer &&
          updatedChild.config?.childrenIds?.length > 0
        ) {
          const grandChildComponents = adaptChildContainersSize(
            updatedChild,
            childWidth,
            availableHeight
          )
          allUpdatedComponents.push(...grandChildComponents)
        }
      })
    } else {
      // 上下布局：重新计算子容器的高度和位置
      const availableWidth = newWidth - 2 * parentPadding
      const availableHeight = newHeight - 2 * parentPadding - childGap
      const childHeight = Math.floor(availableHeight / 2)

      childComponents.forEach((child, index) => {
        // 获取父容器的实际位置
        const parentActualX = parentContainer.fullCanvas
          ? 0
          : parentContainer.position.x
        const parentActualY = parentContainer.fullCanvas
          ? 0
          : parentContainer.position.y

        const updatedChild = {
          ...child,
          position: {
            x: parentActualX + parentPadding,
            y: parentActualY + parentPadding + index * (childHeight + childGap)
          },
          size: {
            width: availableWidth,
            height: childHeight
          },
          config: {
            ...child.config,
            parentPadding: parentPadding,
            childGap: childGap
          }
        }
        allUpdatedComponents.push(updatedChild)

        // 递归处理子容器的子容器（孙容器）
        if (
          updatedChild.config?.isContainer &&
          updatedChild.config?.childrenIds?.length > 0
        ) {
          const grandChildComponents = adaptChildContainersSize(
            updatedChild,
            availableWidth,
            childHeight
          )
          allUpdatedComponents.push(...grandChildComponents)
        }
      })
    }

    return allUpdatedComponents
  }

  // 子容器尺寸调整适配
  const adaptSiblingContainerSize = (
    resizedChild,
    newX,
    newY,
    newWidth,
    newHeight,
    rect
  ) => {
    const sibling = getSiblingContainer(resizedChild)
    const parent = props.components.find(
      comp => comp.id === resizedChild.config.parentId
    )

    if (!sibling || !parent) return null

    const actualScaleX = props.canvasSize.width / rect.width
    const actualScaleY = props.canvasSize.height / rect.height

    // 转换为实际画布坐标和尺寸
    const actualCoords = convertDisplayToActualCoordinates(newX, newY, rect)
    const actualWidth = Math.round(newWidth * actualScaleX)
    const actualHeight = Math.round(newHeight * actualScaleY)

    const parentPadding = resizedChild.config.parentPadding || 12
    const childGap = resizedChild.config.childGap || 8
    const direction = resizedChild.config.parentDirection

    if (direction === 'horizontal') {
      // 左右布局：调整兄弟容器的宽度和位置
      const parentActualWidth = parent.fullCanvas
        ? props.canvasSize.width
        : parent.size.width
      const availableWidth = parentActualWidth - 2 * parentPadding - childGap
      const siblingWidth = availableWidth - actualWidth

      // 确保兄弟容器宽度不小于最小值
      if (siblingWidth < 50) return null

      let siblingX, siblingY
      if (resizedChild.config.childIndex === 0) {
        // 调整的是左侧容器，兄弟容器在右侧
        siblingX = actualCoords.x + actualWidth + childGap
        siblingY = sibling.position.y
      } else {
        // 调整的是右侧容器，兄弟容器在左侧
        siblingX = sibling.position.x
        siblingY = sibling.position.y
      }

      return {
        ...sibling,
        position: { x: siblingX, y: siblingY },
        size: { width: siblingWidth, height: sibling.size.height }
      }
    } else {
      // 上下布局：调整兄弟容器的高度和位置
      const parentActualHeight = parent.fullCanvas
        ? props.canvasSize.height
        : parent.size.height
      const availableHeight = parentActualHeight - 2 * parentPadding - childGap
      const siblingHeight = availableHeight - actualHeight

      // 确保兄弟容器高度不小于最小值
      if (siblingHeight < 50) return null

      let siblingX, siblingY
      if (resizedChild.config.childIndex === 0) {
        // 调整的是上部容器，兄弟容器在下部
        siblingX = sibling.position.x
        siblingY = actualCoords.y + actualHeight + childGap
      } else {
        // 调整的是下部容器，兄弟容器在上部
        siblingX = sibling.position.x
        siblingY = sibling.position.y
      }

      return {
        ...sibling,
        position: { x: siblingX, y: siblingY },
        size: { width: sibling.size.width, height: siblingHeight }
      }
    }
  }

  // 调整尺寸结束
  const handleResizeEnd = event => {
    if (!isResizing.value || !currentDragComponent.value) return

    const canvasElement = document.querySelector('.canvas-grid')
    if (!canvasElement) return

    const rect = canvasElement.getBoundingClientRect()
    const deltaX = event.clientX - resizeStartPos.value.x
    const deltaY = event.clientY - resizeStartPos.value.y
    let { newX, newY, newWidth, newHeight } = calculateNewDimensions(
      deltaX,
      deltaY
    )

    // 对DIV容器进行边界和碰撞检测
    if (currentDragComponent.value.type === 'div-container') {
      // 如果是子容器，检查是否超出父容器边界
      if (currentDragComponent.value.config?.isChildContainer) {
        const parent = props.components.find(
          comp => comp.id === currentDragComponent.value.config.parentId
        )
        if (parent) {
          const actualCoords = convertDisplayToActualCoordinates(
            newX,
            newY,
            rect
          )
          const actualScaleX = props.canvasSize.width / rect.width
          const actualScaleY = props.canvasSize.height / rect.height
          const actualWidth = Math.round(newWidth * actualScaleX)
          const actualHeight = Math.round(newHeight * actualScaleY)

          const parentPadding =
            currentDragComponent.value.config.parentPadding || 12
          const childGap = currentDragComponent.value.config.childGap || 8

          // 检查是否超出父容器边界
          const parentLeft = parent.position.x + parentPadding
          const parentTop = parent.position.y + parentPadding
          const parentRight =
            parent.position.x + parent.size.width - parentPadding
          const parentBottom =
            parent.position.y + parent.size.height - parentPadding

          // 限制子容器不能超出父容器边界
          if (actualCoords.x < parentLeft) {
            newX = convertActualToDisplayCoordinates(parentLeft, 0, rect).x
          }
          if (actualCoords.y < parentTop) {
            newY = convertActualToDisplayCoordinates(0, parentTop, rect).y
          }
          if (actualCoords.x + actualWidth > parentRight) {
            const maxWidth = parentRight - actualCoords.x
            newWidth = maxWidth / actualScaleX
          }
          if (actualCoords.y + actualHeight > parentBottom) {
            const maxHeight = parentBottom - actualCoords.y
            newHeight = maxHeight / actualScaleY
          }

          // 确保兄弟容器有足够的空间
          const direction = currentDragComponent.value.config.parentDirection
          if (direction === 'horizontal') {
            const availableWidth =
              parent.size.width - 2 * parentPadding - childGap
            const siblingMinWidth = 50
            const maxCurrentWidth = availableWidth - siblingMinWidth
            const currentActualWidth = Math.round(newWidth * actualScaleX)

            if (currentActualWidth > maxCurrentWidth) {
              newWidth = maxCurrentWidth / actualScaleX
            }
          } else {
            const availableHeight =
              parent.size.height - 2 * parentPadding - childGap
            const siblingMinHeight = 50
            const maxCurrentHeight = availableHeight - siblingMinHeight
            const currentActualHeight = Math.round(newHeight * actualScaleY)

            if (currentActualHeight > maxCurrentHeight) {
              newHeight = maxCurrentHeight / actualScaleY
            }
          }
        }
      }

      // 对其他DIV容器进行碰撞检测（不包括子容器间的碰撞）
      if (
        !currentDragComponent.value.config?.isChildContainer &&
        checkDivContainerCollisions(
          currentDragComponent.value,
          newX,
          newY,
          newWidth,
          newHeight
        )
      ) {
        const validPosition = findValidPosition(
          currentDragComponent.value,
          newX,
          newY,
          newWidth,
          newHeight
        )

        if (validPosition.x !== newX || validPosition.y !== newY) {
          // 尝试只调整尺寸，不改变位置
          if (
            !checkDivContainerCollisions(
              currentDragComponent.value,
              resizeStartData.value.x,
              resizeStartData.value.y,
              newWidth,
              newHeight
            )
          ) {
            newX = resizeStartData.value.x
            newY = resizeStartData.value.y
          } else {
            // 如果调整尺寸也会碰撞，则保持原始尺寸和位置
            newX = resizeStartData.value.x
            newY = resizeStartData.value.y
            newWidth = resizeStartData.value.width
            newHeight = resizeStartData.value.height
          }
        } else {
          newX = validPosition.x
          newY = validPosition.y
        }
      }
    }

    // 转换为实际画布坐标和尺寸后保存
    const actualCoords = convertDisplayToActualCoordinates(newX, newY, rect)
    const actualScaleX = props.canvasSize.width / rect.width
    const actualScaleY = props.canvasSize.height / rect.height

    // 更新当前组件
    emit('update-component', {
      ...currentDragComponent.value,
      position: {
        x: actualCoords.x,
        y: actualCoords.y
      },
      size: {
        width: Math.round(newWidth * actualScaleX),
        height: Math.round(newHeight * actualScaleY)
      }
    })

    // 如果是子容器，同步调整兄弟容器
    if (currentDragComponent.value.config?.isChildContainer) {
      const updatedSibling = adaptSiblingContainerSize(
        currentDragComponent.value,
        newX,
        newY,
        newWidth,
        newHeight,
        rect
      )

      if (updatedSibling) {
        emit('update-component', updatedSibling)
      }
    }

    // 如果是父容器，同步调整所有子容器
    if (
      currentDragComponent.value.config?.isContainer &&
      currentDragComponent.value.config?.childrenIds
    ) {
      const updatedParent = {
        ...currentDragComponent.value,
        position: {
          x: actualCoords.x,
          y: actualCoords.y
        },
        size: {
          width: Math.round(newWidth * actualScaleX),
          height: Math.round(newHeight * actualScaleY)
        }
      }

      const updatedChildComponents = adaptChildContainersSize(
        updatedParent,
        Math.round(newWidth * actualScaleX),
        Math.round(newHeight * actualScaleY)
      )

      updatedChildComponents.forEach(childComponent => {
        emit('update-component', childComponent)
      })
    }

    // 清除碰撞检测的视觉提示
    const componentEl = document.querySelector(
      `[data-component-id="${currentDragComponent.value.id}"]`
    )
    if (componentEl) {
      componentEl.style.boxShadow = ''
    }

    isResizing.value = false
    currentDragComponent.value = null
    document.removeEventListener('mousemove', handleResizeMove)
    document.removeEventListener('mouseup', handleResizeEnd)
  }

  // 删除组件
  const deleteComponent = async component => {
    try {
      await ElMessageBox.confirm('确定要删除这个组件吗？', '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      emit('delete-component', component.id)
    } catch {
      // 用户取消删除
    }
  }

  // 处理键盘事件
  const handleKeyDown = async event => {
    if (event.key === 'Delete') {
      // 优先级1：删除选中的表单子组件
      if (props.selectedFormChild && props.selectedComponent) {
        await deleteFormChild(props.selectedFormChild, props.selectedComponent)
      }
      // 优先级2：删除选中的表单列
      else if (props.selectedFormColumn) {
        await deleteFormColumn(props.selectedFormColumn)
      }
      // 优先级3：删除选中的表单行
      else if (props.selectedFormRow) {
        await deleteFormRow(props.selectedFormRow)
      }
      // 优先级4：删除选中的组件
      else if (props.selectedComponent) {
        await deleteComponent(props.selectedComponent)
      }
    }
  }

  // 删除表单子组件
  const deleteFormChild = async (formChild, formContainer) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除表单组件"${formChild.config?.label || '未命名'}"吗？`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 发送删除表单子组件事件
      emit('delete-form-child-by-delete-key', {
        formChild: formChild,
        formContainer: formContainer
      })
    } catch {
      // 用户取消删除
    }
  }

  // 删除表单列
  const deleteFormColumn = async selectedFormColumn => {
    try {
      const { column, row, formContainer } = selectedFormColumn
      const childrenCount = column.children?.length || 0
      const childrenText =
        childrenCount > 0 ? `及其${childrenCount}个表单组件` : ''

      await ElMessageBox.confirm(
        `删除列将会同时删除列内的所有表单组件${childrenText}，此操作不可撤销。`,
        '确认删除列',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 发送删除表单列事件
      emit('delete-form-column', { column, row, formContainer })
    } catch {
      // 用户取消删除
    }
  }

  // 删除表单行
  const deleteFormRow = async selectedFormRow => {
    try {
      // 计算行内的总组件数
      let totalComponents = 0
      if (selectedFormRow.children) {
        totalComponents += selectedFormRow.children.length
      }
      if (selectedFormRow.columns) {
        selectedFormRow.columns.forEach(col => {
          if (col.children) {
            totalComponents += col.children.length
          }
        })
      }

      const componentsText =
        totalComponents > 0 ? `及其${totalComponents}个表单组件` : ''

      await ElMessageBox.confirm(
        `删除行将会同时删除行内的所有列和表单组件${componentsText}，此操作不可撤销。`,
        '确认删除行',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 发送删除表单行事件
      emit('delete-form-row-by-delete-key', selectedFormRow)
    } catch {
      // 用户取消删除
    }
  }

  // 全局点击事件处理（用于隐藏右键菜单）
  const handleGlobalClick = event => {
    // 如果点击的不是右键菜单本身，则关闭菜单
    if (!event.target.closest('.context-menu')) {
      hideContextMenu()
    }
  }

  // 全局右键事件处理（用于隐藏右键菜单，但排除画布区域）
  const handleGlobalContextMenu = event => {
    // 如果右键点击在画布容器外，则隐藏菜单
    if (!event.target.closest('.canvas-container')) {
      hideContextMenu()
    }
  }

  // 添加和移除事件监听器
  onMounted(() => {
    document.addEventListener('keydown', handleKeyDown)
    document.addEventListener('click', handleGlobalClick)
    document.addEventListener('contextmenu', handleGlobalContextMenu)
  })

  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeyDown)
    document.removeEventListener('click', handleGlobalClick)
    document.removeEventListener('contextmenu', handleGlobalContextMenu)
  })

  // 坐标转换辅助函数
  const convertActualToDisplayX = (actualX) => {
    const canvasElement = document.querySelector('.canvas-grid')
    if (!canvasElement) return actualX
    const rect = canvasElement.getBoundingClientRect()
    return (actualX * rect.width) / props.canvasSize.width
  }

  const convertActualToDisplayY = (actualY) => {
    const canvasElement = document.querySelector('.canvas-grid')
    if (!canvasElement) return actualY
    const rect = canvasElement.getBoundingClientRect()
    return (actualY * rect.height) / props.canvasSize.height
  }

  // 计算所有可能的参考线位置
  const calculateGuidePositions = currentComponent => {
    const positions = {
      vertical: new Set(),
      horizontal: new Set()
    }

    // 添加画布边界参考线
    positions.vertical.add(0) // 左边界
    positions.vertical.add(props.canvasSize.width) // 右边界
    positions.vertical.add(props.canvasSize.width / 2) // 中心线

    positions.horizontal.add(0) // 上边界
    positions.horizontal.add(props.canvasSize.height) // 下边界
    positions.horizontal.add(props.canvasSize.height / 2) // 中心线

    // 遍历所有其他组件
    props.components.forEach(comp => {
      if (comp.id === currentComponent.id) return

      // 获取组件的实际位置和尺寸
      let compX = comp.position.x
      let compY = comp.position.y
      let compWidth = comp.size.width
      let compHeight = comp.size.height

      // 处理百分比格式
      if (typeof compX === 'string' && compX.endsWith('%')) {
        compX = (parseFloat(compX) * props.canvasSize.width) / 100
      }
      if (typeof compY === 'string' && compY.endsWith('%')) {
        compY = (parseFloat(compY) * props.canvasSize.height) / 100
      }
      if (typeof compWidth === 'string' && compWidth.endsWith('%')) {
        compWidth = (parseFloat(compWidth) * props.canvasSize.width) / 100
      }
      if (typeof compHeight === 'string' && compHeight.endsWith('%')) {
        compHeight = (parseFloat(compHeight) * props.canvasSize.height) / 100
      }

      // 垂直方向的参考线（左对齐、中对齐、右对齐）
      positions.vertical.add(compX) // 左边界
      positions.vertical.add(compX + compWidth) // 右边界
      positions.vertical.add(compX + compWidth / 2) // 中心线

      // 水平方向的参考线（上对齐、中对齐、下对齐）
      positions.horizontal.add(compY) // 上边界
      positions.horizontal.add(compY + compHeight) // 下边界
      positions.horizontal.add(compY + compHeight / 2) // 中心线
    })

    return {
      vertical: Array.from(positions.vertical).sort((a, b) => a - b),
      horizontal: Array.from(positions.horizontal).sort((a, b) => a - b)
    }
  }

  // 批量更新子容器边框颜色
  const updateChildContainerStyles = () => {
    const updatedComponents = props.components.map(component => {
      if (component.config?.isChildContainer) {
        return {
          ...component,
          config: {
            ...component.config,
            backgroundColor: '#f8f9fa',
            borderColor: '#e4e7ed',
            borderWidth: 1,
            borderStyle: 'dashed'
          }
        }
      }
      return component
    })

    emit('update-components', updatedComponents)
  }

  // 暴露方法供外部调用
  defineExpose({
    updateChildContainerStyles
  })

  // 获取当前缩放比例
  const getCurrentScale = () => {
    // 从父组件获取缩放比例
    const scaleControls = inject('scaleControls', null)
    return scaleControls?.currentScale?.value || 1
  }
</script>

<style scoped>
  .canvas-container {
    background-color: white;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    overflow: visible; /* 确保选中效果和删除按钮不会被裁剪 */
    position: relative;
  }

  .canvas-grid {
    background-image:
      linear-gradient(to right, #f0f0f0 1px, transparent 1px),
      linear-gradient(to bottom, #f0f0f0 1px, transparent 1px);
    background-size: 20px 20px;
    position: relative;
    overflow: visible; /* 确保子元素不会被裁剪 */
  }

  .canvas-component {
    position: absolute;
    border: 2px solid transparent;
    cursor: move;
    user-select: none;
    box-sizing: border-box;
    overflow: visible; /* 确保删除按钮不会被裁剪 */
  }

  .canvas-component.selected {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    z-index: 1000; /* 确保选中的组件在上层 */
  }

  /* 当父容器被选中时，确保子容器仍然可见 */
  .canvas-component.selected[data-is-container="true"] {
    z-index: 999; /* 降低容器组的z-index，让子容器能显示在上面 */
  }

  /* 子容器应该有更高的z-index，确保在父容器选中时仍然可见 */
  .canvas-component[data-is-child-container="true"] {
    z-index: 1001;
  }

  .canvas-component.collision-warning {
    border-color: #f56c6c !important;
    box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.3) !important;
  }

  .component-content {
    width: 100%;
    height: 100%;
    background: white;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .resize-handles {
    position: absolute;
    inset: -4px;
    pointer-events: none;
  }

  .resize-handle {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #409eff;
    border: 1px solid #fff;
    border-radius: 2px;
    pointer-events: all;
    cursor: nw-resize;
  }

  .resize-handle-nw {
    top: 0;
    left: 0;
    cursor: nw-resize;
  }

  .resize-handle-ne {
    top: 0;
    right: 0;
    cursor: ne-resize;
  }

  .resize-handle-sw {
    bottom: 0;
    left: 0;
    cursor: sw-resize;
  }

  .resize-handle-se {
    bottom: 0;
    right: 0;
    cursor: se-resize;
  }

  .delete-btn {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 20px;
    height: 20px;
    background: #f56c6c;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: white;
    font-size: 12px;
    transition: all 0.2s;
    z-index: 1001; /* 确保在最上层 */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3); /* 添加阴影提高可见性 */
  }

  .delete-btn:hover {
    background: #f78989;
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  }

  /* 参考线样式 */
  .guide-line {
    position: absolute;
    pointer-events: none;
    z-index: 1000;
  }

  .guide-line-vertical {
    width: 1px;
    height: 100%;
    top: 0;
    background-color: #409eff;
  }

  .guide-line-horizontal {
    width: 100%;
    height: 1px;
    left: 0;
    background-color: #409eff;
  }

  /* 拖拽悬停样式 */
  .drag-over-container {
    border: 2px dashed #409eff !important;
    background-color: rgba(64, 158, 255, 0.1) !important;
    box-shadow: 0 0 15px rgba(64, 158, 255, 0.3) !important;
  }

  .drag-over-container::after {
    content: '拖拽到此处添加组件';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(64, 158, 255, 0.9);
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    pointer-events: none;
    max-width: 80%;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  /* 响应式模式提示样式 */
  .responsive-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
    pointer-events: none;
  }

  .responsive-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    font-weight: 500;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .responsive-icon {
    font-size: 14px;
  }

  .responsive-text {
    font-weight: 600;
  }

  .responsive-desc {
    opacity: 0.9;
    font-size: 11px;
    margin-left: 4px;
  }
</style>
