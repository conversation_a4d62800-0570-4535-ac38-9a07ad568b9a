<template>
  <div class="debug-preview-page">
    <div class="debug-controls">
      <button @click="loadTestData">加载测试数据</button>
      <button @click="clearData">清空数据</button>
      <button @click="showDebugInfo = !showDebugInfo">
        {{ showDebugInfo ? '隐藏' : '显示' }}调试信息
      </button>
    </div>

    <div class="debug-content">
      <!-- 调试信息 -->
      <div v-if="showDebugInfo" class="debug-info">
        <h3>调试信息</h3>
        <div class="debug-section">
          <h4>原始数据</h4>
          <pre>{{ JSON.stringify(originalData, null, 2) }}</pre>
        </div>

        <div class="debug-section">
          <h4>转换后数据</h4>
          <pre>{{ JSON.stringify(schema?.components || [], null, 2) }}</pre>
        </div>

        <div class="debug-section">
          <h4>渲染统计</h4>
          <p>总组件数: {{ schema?.components?.length || 0 }}</p>
          <p>顶级组件数: {{ topLevelComponents.length }}</p>
          <p>子容器数: {{ childContainers.length }}</p>
          <p>容器组件数: {{ containerComponents.length }}</p>
        </div>

        <div class="debug-section">
          <h4>组件关系</h4>
          <div
            v-for="container in containerComponents"
            :key="container.id"
            style="margin-bottom: 20px; border: 1px solid #ccc; padding: 10px"
          >
            <p>
              <strong>容器 {{ container.id }} ({{ container.name }}):</strong>
            </p>
            <div style="background: #f0f0f0; padding: 5px; margin: 5px 0">
              <div>
                <strong>isContainer:</strong>
                {{ container.config?.isContainer }}
              </div>
              <div>
                <strong>childrenIds:</strong>
                {{ JSON.stringify(container.config?.childrenIds) }}
              </div>
              <div>
                <strong>isChildContainer:</strong>
                {{ container.config?.isChildContainer }}
              </div>
              <div>
                <strong>parentId:</strong> {{ container.config?.parentId }}
              </div>
            </div>
            <ul>
              <li
                v-for="childId in container.config?.childrenIds || []"
                :key="childId"
              >
                子组件: {{ childId }}
                <span v-if="getComponentById(childId)"
                  >(✓ {{ getComponentById(childId)?.name }})</span
                >
                <span v-else>(✗ 缺失)</span>
              </li>
            </ul>
          </div>
        </div>

        <div class="debug-section">
          <h4>层级结构树</h4>
          <div class="hierarchy-tree">
            <div v-for="component in topLevelComponents" :key="component.id">
              <div v-html="buildTreeHTML(component, 0)"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 预览区域 -->
      <div class="preview-area">
        <h3>预览效果</h3>
        <div v-if="schema" class="preview-container">
          <div class="preview-wrapper" :style="wrapperStyle">
            <SchemaRenderer
              :schema="schema"
              is-preview
              @update-component="handleUpdateComponent"
            />
          </div>
        </div>
        <div v-else class="no-schema">
          <p>没有数据，请点击"加载测试数据"</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue'
  import SchemaRenderer from '../components/SchemaRenderer.vue'

  const schema = ref(null)
  const originalData = ref(null)
  const showDebugInfo = ref(true)

  // 计算属性
  const topLevelComponents = computed(() => {
    if (!schema.value?.components) return []
    return schema.value.components.filter(
      comp => !comp.config?.isChildContainer
    )
  })

  const childContainers = computed(() => {
    if (!schema.value?.components) return []
    return schema.value.components.filter(comp => comp.config?.isChildContainer)
  })

  const containerComponents = computed(() => {
    if (!schema.value?.components) return []
    return schema.value.components.filter(comp => comp.config?.isContainer)
  })

  const wrapperStyle = computed(() => {
    if (!schema.value?.canvas) return {}
    return {
      width: `${schema.value.canvas.width}px`,
      height: `${schema.value.canvas.height}px`,
      border: '1px solid #ddd',
      backgroundColor: '#fff'
    }
  })

  // 方法
  const getComponentById = id => {
    return schema.value?.components?.find(comp => comp.id === id)
  }

  const loadTestData = () => {
    console.log('🚀 开始加载测试数据')
    const testData = {
      version: '2.0',
      canvas: { width: 1000, height: 700 },
      components: [
        {
          id: 'div1',
          type: 'div-container',
          name: '祖父容器(层级0)',
          position: { x: 50, y: 50 },
          size: { width: 900, height: 600 },
          config: {
            isContainer: true,
            direction: 'horizontal',
            showTitleBar: true,
            titleText: '祖父容器(层级0)',
            backgroundColor: '#f8f9fa',
            borderColor: '#6c757d',
            borderWidth: 3,
            borderStyle: 'solid',
            borderRadius: 8,
            padding: 12
          },
          children: [
            {
              id: 'div2',
              type: 'div-container',
              name: '父容器(层级1)',
              position: { x: 0, y: 0 },
              size: { width: 450, height: 600 },
              config: {
                isContainer: true,
                direction: 'vertical',
                showTitleBar: true,
                titleText: '父容器(层级1)',
                backgroundColor: '#e3f2fd',
                borderColor: '#2196f3',
                borderWidth: 2,
                borderStyle: 'solid',
                borderRadius: 6,
                padding: 8
              },
              children: [
                {
                  id: 'div3',
                  type: 'div-container',
                  name: '子容器1(层级2)',
                  position: { x: 0, y: 0 },
                  size: { width: 450, height: 200 },
                  config: {
                    showTitleBar: true,
                    titleText: '子容器1(层级2)',
                    content: '这是第2层的子容器1',
                    backgroundColor: '#fff3e0',
                    borderColor: '#ff9800',
                    borderWidth: 1,
                    borderStyle: 'solid',
                    borderRadius: 4,
                    padding: 8
                  }
                },
                {
                  id: 'div4',
                  type: 'div-container',
                  name: '子容器2(层级2)',
                  position: { x: 0, y: 200 },
                  size: { width: 450, height: 400 },
                  config: {
                    isContainer: true,
                    direction: 'horizontal',
                    showTitleBar: true,
                    titleText: '子容器2(层级2)-还有子组件',
                    backgroundColor: '#e8f5e8',
                    borderColor: '#4caf50',
                    borderWidth: 2,
                    borderStyle: 'solid',
                    borderRadius: 4,
                    padding: 8
                  },
                  children: [
                    {
                      id: 'div5',
                      type: 'div-container',
                      name: '曾孙容器1(层级3)',
                      position: { x: 0, y: 0 },
                      size: { width: 225, height: 400 },
                      config: {
                        showTitleBar: true,
                        titleText: '曾孙容器1(层级3)',
                        content: '这是第3层的曾孙容器1',
                        backgroundColor: '#fff0f6',
                        borderColor: '#eb2f96',
                        borderWidth: 1,
                        borderStyle: 'solid',
                        borderRadius: 4,
                        padding: 8
                      }
                    },
                    {
                      id: 'div6',
                      type: 'div-container',
                      name: '曾孙容器2(层级3)',
                      position: { x: 225, y: 0 },
                      size: { width: 225, height: 400 },
                      config: {
                        isContainer: true,
                        direction: 'vertical',
                        showTitleBar: true,
                        titleText: '曾孙容器2(层级3)-还有子组件',
                        backgroundColor: '#f6ffed',
                        borderColor: '#52c41a',
                        borderWidth: 2,
                        borderStyle: 'solid',
                        borderRadius: 4,
                        padding: 6
                      },
                      children: [
                        {
                          id: 'div7',
                          type: 'div-container',
                          name: '玄孙容器1(层级4)',
                          position: { x: 0, y: 0 },
                          size: { width: 225, height: 200 },
                          config: {
                            showTitleBar: true,
                            titleText: '玄孙容器1(层级4)',
                            content: '这是第4层的玄孙容器1',
                            backgroundColor: '#f0f5ff',
                            borderColor: '#1890ff',
                            borderWidth: 1,
                            borderStyle: 'solid',
                            borderRadius: 4,
                            padding: 6
                          }
                        },
                        {
                          id: 'div8',
                          type: 'div-container',
                          name: '玄孙容器2(层级4)',
                          position: { x: 0, y: 200 },
                          size: { width: 225, height: 200 },
                          config: {
                            showTitleBar: true,
                            titleText: '玄孙容器2(层级4)',
                            content: '这是第4层的玄孙容器2',
                            backgroundColor: '#fef0f0',
                            borderColor: '#f56565',
                            borderWidth: 1,
                            borderStyle: 'solid',
                            borderRadius: 4,
                            padding: 6
                          }
                        }
                      ]
                    }
                  ]
                }
              ]
            },
            {
              id: 'div9',
              type: 'div-container',
              name: '右侧容器(层级1)',
              position: { x: 450, y: 0 },
              size: { width: 450, height: 600 },
              config: {
                isContainer: true,
                direction: 'vertical',
                showTitleBar: true,
                titleText: '右侧容器(层级1)',
                backgroundColor: '#fce4ec',
                borderColor: '#e91e63',
                borderWidth: 2,
                borderStyle: 'solid',
                borderRadius: 6,
                padding: 8
              },
              children: [
                {
                  id: 'div10',
                  type: 'div-container',
                  name: '右侧子容器1(层级2)',
                  position: { x: 0, y: 0 },
                  size: { width: 450, height: 300 },
                  config: {
                    showTitleBar: true,
                    titleText: '右侧子容器1(层级2)',
                    content: '这是右侧第2层的子容器1',
                    backgroundColor: '#fff8e1',
                    borderColor: '#ffc107',
                    borderWidth: 1,
                    borderStyle: 'solid',
                    borderRadius: 4,
                    padding: 8
                  }
                },
                {
                  id: 'div11',
                  type: 'div-container',
                  name: '右侧子容器2(层级2)',
                  position: { x: 0, y: 300 },
                  size: { width: 450, height: 300 },
                  config: {
                    showTitleBar: true,
                    titleText: '右侧子容器2(层级2)',
                    content: '这是右侧第2层的子容器2',
                    backgroundColor: '#f1f8e9',
                    borderColor: '#8bc34a',
                    borderWidth: 1,
                    borderStyle: 'solid',
                    borderRadius: 4,
                    padding: 8
                  }
                }
              ]
            }
          ]
        }
      ]
    }

    originalData.value = testData
    processData(testData)
  }

  const clearData = () => {
    schema.value = null
    originalData.value = null
    localStorage.removeItem('bi-schema-preview')
  }

  const processData = async data => {
    try {
      // 动态导入转换工具
      const { transformToFlatSchema } = await import(
        '../utils/schemaTransform.js'
      )
      // 转换为平铺格式
      const flatComponents = transformToFlatSchema(data.components)

      console.log('🔍 转换前数据:', data.components)
      console.log('🔍 转换后数据:', flatComponents)

      schema.value = {
        canvas: data.canvas,
        components: flatComponents
      }

      // 保存到localStorage以便在正常预览页面查看
      localStorage.setItem('bi-schema-preview', JSON.stringify(data))
    } catch (error) {
      console.error('处理数据时出错:', error)
    }
  }

  const handleUpdateComponent = updatedComponent => {
    console.log('组件更新:', updatedComponent)
  }

  const buildTreeHTML = (component, level) => {
    const indent = '&nbsp;&nbsp;'.repeat(level * 2)
    const icon = component.config?.isContainer ? '📁' : '📄'
    const color =
      level === 0
        ? '#007acc'
        : level === 1
          ? '#52c41a'
          : level === 2
            ? '#fa8c16'
            : '#eb2f96'

    let html = `<div style="color: ${color}; margin: 2px 0;">`
    html += `${indent}${icon} ${component.id} (${component.name})`

    if (component.config?.isContainer && component.config?.childrenIds) {
      const childCount = component.config.childrenIds.length
      html += ` <span style="color: #666;">[${childCount}个子组件]</span>`
    }

    if (component.config?.content) {
      html += ` <span style="color: #999; font-size: 12px;">- "${component.config.content}"</span>`
    }

    html += '</div>'

    // 递归渲染子组件
    if (component.config?.isContainer && component.config?.childrenIds) {
      component.config.childrenIds.forEach(childId => {
        const childComponent = getComponentById(childId)
        if (childComponent) {
          html += buildTreeHTML(childComponent, level + 1)
        } else {
          html += `<div style="color: red; margin: 2px 0;">${'&nbsp;&nbsp;'.repeat((level + 1) * 2)}❌ ${childId} (缺失)</div>`
        }
      })
    }

    return html
  }

  onMounted(() => {
    // 尝试加载已有数据
    const savedData = localStorage.getItem('bi-schema-preview')
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData)
        originalData.value = parsedData
        processData(parsedData)
      } catch (error) {
        console.error('加载数据失败:', error)
      }
    }
  })
</script>

<style scoped>
  .debug-preview-page {
    padding: 20px;
    font-family: Arial, sans-serif;
  }

  .debug-controls {
    margin-bottom: 20px;
  }

  .debug-controls button {
    margin-right: 10px;
    padding: 8px 16px;
    border: 1px solid #ddd;
    background: #f5f5f5;
    cursor: pointer;
    border-radius: 4px;
  }

  .debug-controls button:hover {
    background: #e0e0e0;
  }

  .debug-content {
    display: flex;
    gap: 20px;
  }

  .debug-info {
    flex: 1;
    background: #f9f9f9;
    padding: 16px;
    border-radius: 8px;
    max-height: 80vh;
    overflow-y: auto;
  }

  .debug-section {
    margin-bottom: 20px;
  }

  .debug-section h4 {
    margin: 0 0 10px 0;
    color: #333;
  }

  .debug-section pre {
    background: #fff;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #ddd;
    font-size: 12px;
    overflow-x: auto;
    max-height: 300px;
  }

  .preview-area {
    flex: 1;
  }

  .preview-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .preview-wrapper {
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
  }

  .no-schema {
    text-align: center;
    padding: 40px;
    color: #666;
  }

  .hierarchy-tree {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    font-family: monospace;
    font-size: 13px;
    line-height: 1.4;
  }
</style>
