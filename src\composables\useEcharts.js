import { ref, watch, onUnmounted } from 'vue'
import { useResizeObserver } from '@vueuse/core'
import * as echarts from 'echarts'

/**
 * @description ECharts hook
 * @param {Ref<HTMLElement>} chartRef ECharts容器元素的ref
 * @param {Ref<object>} options ECharts配置项的ref
 * @returns { { chartInstance: Ref<echarts.ECharts> } }
 */
export function useEcharts(chartRef, options) {
  const chartInstance = ref(null)

  const initChart = () => {
    if (!chartRef.value) return

    try {
      chartInstance.value = echarts.init(chartRef.value)
      if (options.value) {
        chartInstance.value.setOption(options.value)
      }
    } catch (e) {
      console.error('ECharts init error:', e)
    }
  }

  // 监听配置变化
  watch(
    options,
    newOptions => {
      if (chartInstance.value && newOptions) {
        chartInstance.value.setOption(newOptions, true)
      }
    },
    { deep: true }
  )

  // 监听容器元素变化，进行初始化
  const unwatchEl = watch(
    chartRef,
    newEl => {
      if (newEl && !chartInstance.value) {
        initChart()
        // 停止监听，防止重复初始化
        unwatchEl()
      }
    },
    { immediate: true }
  )

  // 监听容器尺寸变化，重新初始化图表
  useResizeObserver(chartRef, () => {
    if (chartInstance.value) {
      // 保存当前配置
      const currentOptions = chartInstance.value.getOption()
      // 销毁当前实例
      chartInstance.value.dispose()
      // 重新初始化
      chartInstance.value = echarts.init(chartRef.value)
      // 应用配置
      chartInstance.value.setOption(currentOptions)
    }
  })

  // 组件卸载时销毁图表
  onUnmounted(() => {
    if (chartInstance.value) {
      chartInstance.value.dispose()
      chartInstance.value = null
    }
  })

  return {
    chartInstance
  }
}
