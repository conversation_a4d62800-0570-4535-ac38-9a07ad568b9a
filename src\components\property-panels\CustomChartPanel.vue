<template>
  <div class="custom-chart-panel">
    <!-- 数据源配置 -->
    <ElFormItem label="数据源类型">
      <ElSelect
        :model-value="localConfig.dataSourceType || 'static'"
        @change="updateProperty('dataSourceType', $event)"
        size="small"
        placeholder="请选择数据源类型"
      >
        <ElOption label="静态数据" value="static" />
        <ElOption label="API接口" value="api" />
      </ElSelect>
    </ElFormItem>

    <!-- 静态数据配置 -->
    <template
      v-if="
        localConfig.dataSourceType === 'static' || !localConfig.dataSourceType
      "
    >
      <ElFormItem label="数据说明">
        <div class="data-description">
          <p>图表数据由 Options 配置中的 series 字段控制</p>
          <p>您可以在 Options 配置中直接设置数据</p>
        </div>
      </ElFormItem>
    </template>

    <!-- API数据配置 -->
    <template v-if="localConfig.dataSourceType === 'api'">
      <ElFormItem label="API地址">
        <ElInput
          :model-value="localConfig.apiUrl || ''"
          @input="updateProperty('apiUrl', $event)"
          size="small"
          placeholder="请输入API地址"
        />
      </ElFormItem>

      <ElFormItem label="请求方法">
        <ElSelect
          :model-value="localConfig.apiMethod || 'GET'"
          @change="updateProperty('apiMethod', $event)"
          size="small"
        >
          <ElOption label="GET" value="GET" />
          <ElOption label="POST" value="POST" />
        </ElSelect>
      </ElFormItem>

      <ElFormItem label="数据轮询">
        <ElSwitch
          :model-value="localConfig.polling?.enabled || false"
          @change="updateProperty('polling.enabled', $event)"
        />
      </ElFormItem>

      <ElFormItem v-if="localConfig.polling?.enabled" label="轮询间隔(秒)">
        <ElInputNumber
          :model-value="localConfig.polling?.interval || 10"
          @change="updateProperty('polling.interval', $event)"
          :min="1"
          :max="3600"
          size="small"
          controls-position="right"
        />
      </ElFormItem>
    </template>

    <!-- Options 配置按钮 -->
    <ElFormItem label="图表配置">
      <div class="options-config-section">
        <ElButton
          type="primary"
          @click="openOptionsEditor"
          class="options-config-btn"
        >
          <ElIcon><Setting /></ElIcon>
          配置 Options
        </ElButton>
        <div class="options-hint">
          点击按钮打开 JSON 编辑器，可粘贴 ECharts 官网示例配置
        </div>
      </div>
    </ElFormItem>

    <!-- 数据编辑功能 -->
    <ElFormItem label="数据编辑" v-if="hasCustomOptions">
      <div class="data-config-section">
        <ElButton
          type="warning"
          @click="openDataEditor"
          class="data-config-btn"
        >
          <ElIcon><Edit /></ElIcon>
          编辑数据
        </ElButton>
        <div class="data-hint">
          自动提取 Options 中的示例数据，编辑替换为您的数据
        </div>
      </div>
    </ElFormItem>

    <!-- 当前配置预览 -->
    <ElFormItem label="当前配置">
      <div class="current-config-preview">
        <div class="config-status">
          <span class="status-label">状态:</span>
          <ElTag :type="configStatus.type" size="small">
            {{ configStatus.text }}
          </ElTag>
        </div>
        <div v-if="hasCustomOptions" class="config-info">
          <div class="info-item">
            <span>图表类型:</span>
            <ElTag size="small">{{ chartTypeDisplay }}</ElTag>
          </div>
          <div class="info-item">
            <span>系列数量:</span>
            <ElTag size="small">{{ seriesCount }} 个</ElTag>
          </div>
        </div>
      </div>
    </ElFormItem>

    <!-- 变量绑定组件 -->
    <VariableBinding
      :config="componentConfig"
      :variables="variables"
      @update-property="handleVariableBindingUpdate"
    />
  </div>
</template>

<script setup>
  import { computed } from 'vue'
  import {
    ElFormItem,
    ElSelect,
    ElOption,
    ElInput,
    ElInputNumber,
    ElSwitch,
    ElButton,
    ElIcon,
    ElTag
  } from 'element-plus'
  import { Setting, Edit } from '@element-plus/icons-vue'
  import VariableBinding from './VariableBinding.vue'

  const props = defineProps({
    customConfig: {
      type: Object,
      required: true
    },
    componentConfig: {
      type: Object,
      required: true
    },
    variables: {
      type: Object,
      default: () => ({})
    }
  })

  const emit = defineEmits([
    'update-property',
    'open-options-editor',
    'load-sample-data',
    'open-data-editor'
  ])

  const localConfig = computed({
    get: () => props.customConfig,
    set: () => {
      // We don't use the setter, updates are handled via events
    }
  })

  // 检查是否有自定义配置
  const hasCustomOptions = computed(() => {
    return (
      localConfig.value.customOptions &&
      Object.keys(localConfig.value.customOptions).length > 0
    )
  })

  // 配置状态
  const configStatus = computed(() => {
    if (!hasCustomOptions.value) {
      return {
        type: 'info',
        text: '使用默认配置'
      }
    }

    const options = localConfig.value.customOptions
    if (options.series && options.series.length > 0) {
      return {
        type: 'success',
        text: '已自定义配置'
      }
    }

    return {
      type: 'warning',
      text: '配置不完整'
    }
  })

  // 图表类型显示
  const chartTypeDisplay = computed(() => {
    if (!hasCustomOptions.value) return '未设置'

    const options = localConfig.value.customOptions
    if (options.series && options.series.length > 0) {
      const types = [...new Set(options.series.map(s => s.type))]
      return types.join(', ')
    }

    return '未知'
  })

  // 系列数量
  const seriesCount = computed(() => {
    if (!hasCustomOptions.value) return 0

    const options = localConfig.value.customOptions
    return options.series ? options.series.length : 0
  })

  function updateProperty(key, value) {
    emit('update-property', `config.customConfig.${key}`, value)
  }

  function openOptionsEditor() {
    emit('open-options-editor')
  }

  function openDataEditor() {
    emit('open-data-editor')
  }

  // 处理变量绑定更新
  function handleVariableBindingUpdate(key, value) {
    emit('update-property', key, value)
  }
</script>

<style scoped>
  .custom-chart-panel {
    .data-description {
      background-color: #f5f7fa;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 12px;
      font-size: 12px;
      color: #606266;
      line-height: 1.4;

      p {
        margin: 0;
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .options-config-section {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .options-config-btn {
      align-self: flex-start;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .options-hint {
      font-size: 12px;
      color: #909399;
      line-height: 1.4;
    }

    .data-config-section {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .data-config-btn {
      align-self: flex-start;
      display: flex;
      align-items: center;
      gap: 6px;
      margin-right: 8px;
    }

    .data-hint {
      font-size: 12px;
      color: #909399;
      line-height: 1.4;
    }

    .current-config-preview {
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      padding: 12px;
    }

    .config-status {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;

      .status-label {
        font-size: 12px;
        color: #606266;
      }
    }

    .config-info {
      display: flex;
      flex-direction: column;
      gap: 6px;
    }

    .info-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 12px;

      span {
        color: #606266;
      }
    }
  }

  .el-select {
    width: 100%;
  }
</style>
