{"name": "bi-web", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix", "lint:check": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@guolao/vue-monaco-editor": "^1.5.5", "@vueuse/core": "^13.4.0", "axios": "^1.10.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.10.2", "lodash-es": "^4.17.21", "monaco-editor": "^0.52.2", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.29.0", "@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-prettier": "^10.2.0", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-vue": "^10.2.0", "prettier": "^3.6.1", "vite": "^5.0.0"}}