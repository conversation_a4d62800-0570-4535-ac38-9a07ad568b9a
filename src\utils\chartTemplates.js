import { cloneDeep } from 'lodash-es'

// 图表配置模板
const chartTemplates = {
  // 柱状图模板
  'bar-chart': {
    backgroundColor: 'transparent',
    title: {
      text: '柱状图标题',
      left: 'center',
      textStyle: {
        color: '#333333',
        fontSize: 16
      }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: '#ffffff',
      borderColor: '#ccc',
      borderWidth: 1,
      textStyle: {
        color: '#333333'
      }
    },
    legend: {
      data: ['系列1', '系列2'],
      bottom: 10,
      textStyle: {
        color: '#333333'
      }
    },
    grid: {
      top: 60,
      left: 10,
      right: 10,
      bottom: 60,
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['类别1', '类别2', '类别3', '类别4', '类别5'],
      axisLine: {
        lineStyle: {
          color: '#666666'
        }
      },
      axisLabel: {
        color: '#666666'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#666666'
        }
      },
      axisLabel: {
        color: '#666666'
      },
      splitLine: {
        lineStyle: {
          color: '#e0e0e0'
        }
      }
    },
    color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'],
    series: [
      {
        name: '系列1',
        type: 'bar',
        data: [12, 19, 5, 2, 18]
      },
      {
        name: '系列2',
        type: 'bar',
        data: [7, 11, 13, 21, 9]
      }
    ]
  },

  // 折线图模板
  'line-chart': {
    backgroundColor: 'transparent',
    title: {
      text: '折线图标题',
      left: 'center',
      textStyle: {
        color: '#333333',
        fontSize: 16
      }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: '#ffffff',
      borderColor: '#ccc',
      borderWidth: 1,
      textStyle: {
        color: '#333333'
      }
    },
    legend: {
      data: ['系列1', '系列2'],
      bottom: 10,
      textStyle: {
        color: '#333333'
      }
    },
    grid: {
      top: 60,
      left: 10,
      right: 10,
      bottom: 60,
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['类别1', '类别2', '类别3', '类别4', '类别5'],
      axisLine: {
        lineStyle: {
          color: '#666666'
        }
      },
      axisLabel: {
        color: '#666666'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#666666'
        }
      },
      axisLabel: {
        color: '#666666'
      },
      splitLine: {
        lineStyle: {
          color: '#e0e0e0'
        }
      }
    },
    color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'],
    series: [
      {
        name: '系列1',
        type: 'line',
        data: [12, 19, 5, 2, 18],
        smooth: true
      },
      {
        name: '系列2',
        type: 'line',
        data: [7, 11, 13, 21, 9],
        smooth: true
      }
    ]
  },

  // 饼图模板
  'pie-chart': {
    backgroundColor: 'transparent',
    title: {
      text: '饼图标题',
      left: 'center',
      textStyle: {
        color: '#333333',
        fontSize: 16
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} ({d}%)',
      backgroundColor: '#ffffff',
      borderColor: '#ccc',
      borderWidth: 1,
      textStyle: {
        color: '#333333'
      }
    },
    legend: {
      orient: 'horizontal',
      bottom: 10,
      left: 'center',
      data: ['类别1', '类别2', '类别3', '类别4', '类别5'],
      textStyle: {
        color: '#333333'
      }
    },
    color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'],
    series: [
      {
        name: '数据',
        type: 'pie',
        radius: '60%',
        center: ['50%', '60%'],
        data: [
          { value: 12, name: '类别1' },
          { value: 19, name: '类别2' },
          { value: 5, name: '类别3' },
          { value: 2, name: '类别4' },
          { value: 18, name: '类别5' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  },

  // 自定义图表模板
  'custom-chart': {
    backgroundColor: 'transparent',
    title: {
      text: '自定义图表',
      left: 'center',
      textStyle: {
        color: '#333333',
        fontSize: 16
      }
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: '#ffffff',
      borderColor: '#ccc',
      borderWidth: 1,
      textStyle: {
        color: '#333333'
      }
    },
    legend: {
      data: ['数据'],
      bottom: 10,
      textStyle: {
        color: '#333333'
      }
    },
    grid: {
      top: 60,
      left: 10,
      right: 10,
      bottom: 60,
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['类别1', '类别2', '类别3', '类别4', '类别5'],
      axisLine: {
        lineStyle: {
          color: '#666666'
        }
      },
      axisLabel: {
        color: '#666666'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#666666'
        }
      },
      axisLabel: {
        color: '#666666'
      },
      splitLine: {
        lineStyle: {
          color: '#e0e0e0'
        }
      }
    },
    color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'],
    series: [
      {
        name: '数据',
        type: 'bar',
        data: [10, 20, 30, 40, 50]
      }
    ]
  }
}

// 颜色主题配置
const colorThemes = {
  default: [
    '#5470c6',
    '#91cc75',
    '#fac858',
    '#ee6666',
    '#73c0de',
    '#3ba272',
    '#fc8452',
    '#9a60b4',
    '#ea7ccc'
  ],
  blue: ['#1890ff', '#40a9ff', '#69c0ff', '#91d5ff', '#bae7ff', '#e6f7ff'],
  green: ['#52c41a', '#73d13d', '#95de64', '#b7eb8f', '#d9f7be', '#f0ffff'],
  red: ['#f5222d', '#ff4d4f', '#ff7875', '#ffa39e', '#ffccc7', '#fff1f0'],
  purple: ['#722ed1', '#9254de', '#b37feb', '#d3adf7', '#efdbff', '#f9f0ff'],
  orange: ['#fa8c16', '#ffa940', '#ffc069', '#ffd591', '#ffe7ba', '#fff7e6'],
  // 渐变色主题
  gradient_ocean: [
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#667eea' },
        { offset: 1, color: '#764ba2' }
      ]
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#f093fb' },
        { offset: 1, color: '#f5576c' }
      ]
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#4facfe' },
        { offset: 1, color: '#00f2fe' }
      ]
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#43e97b' },
        { offset: 1, color: '#38f9d7' }
      ]
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#fa709a' },
        { offset: 1, color: '#fee140' }
      ]
    }
  ],
  gradient_sunset: [
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#ff9a56' },
        { offset: 1, color: '#ff6b6b' }
      ]
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#ffecd2' },
        { offset: 1, color: '#fcb69f' }
      ]
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#a8edea' },
        { offset: 1, color: '#fed6e3' }
      ]
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#ff9a9e' },
        { offset: 1, color: '#fecfef' }
      ]
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#fad0c4' },
        { offset: 1, color: '#ffd1ff' }
      ]
    }
  ],
  gradient_forest: [
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#134e5e' },
        { offset: 1, color: '#71b280' }
      ]
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#5f7c8a' },
        { offset: 1, color: '#a8b8d8' }
      ]
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#89f7fe' },
        { offset: 1, color: '#66a6ff' }
      ]
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#48c6ef' },
        { offset: 1, color: '#6f86d6' }
      ]
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#667eea' },
        { offset: 1, color: '#764ba2' }
      ]
    }
  ],
  gradient_tech: [
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#667db6' },
        { offset: 1, color: '#0082c8' }
      ]
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#f12711' },
        { offset: 1, color: '#f5af19' }
      ]
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#654ea3' },
        { offset: 1, color: '#eaafc8' }
      ]
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#009ffd' },
        { offset: 1, color: '#2a2a72' }
      ]
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#8360c3' },
        { offset: 1, color: '#2ebf91' }
      ]
    }
  ]
}

// 创建渐变色配置
function createGradientConfig(gradient, direction) {
  const { startColor, endColor } = gradient

  // 根据方向设置渐变参数
  let gradientConfig = {
    type: 'linear',
    colorStops: [
      { offset: 0, color: startColor },
      { offset: 1, color: endColor }
    ]
  }

  switch (direction) {
    case 'horizontal':
      gradientConfig.x = 0
      gradientConfig.y = 0
      gradientConfig.x2 = 1
      gradientConfig.y2 = 0
      break
    case 'vertical':
      gradientConfig.x = 0
      gradientConfig.y = 0
      gradientConfig.x2 = 0
      gradientConfig.y2 = 1
      break
    case 'diagonal1': // 对角线 ↘
      gradientConfig.x = 0
      gradientConfig.y = 0
      gradientConfig.x2 = 1
      gradientConfig.y2 = 1
      break
    case 'diagonal2': // 对角线 ↗
      gradientConfig.x = 0
      gradientConfig.y = 1
      gradientConfig.x2 = 1
      gradientConfig.y2 = 0
      break
    case 'radial': // 径向渐变
      gradientConfig = {
        type: 'radial',
        x: 0.5,
        y: 0.5,
        r: 0.5,
        colorStops: [
          { offset: 0, color: startColor },
          { offset: 1, color: endColor }
        ]
      }
      break
    default:
      // 默认垂直渐变
      gradientConfig.x = 0
      gradientConfig.y = 0
      gradientConfig.x2 = 0
      gradientConfig.y2 = 1
      break
  }

  return gradientConfig
}

// 获取图表模板
export function getChartTemplate(chartType) {
  return JSON.parse(JSON.stringify(chartTemplates[chartType] || {}))
}

// 应用颜色配置到图表选项
export function applyColorConfig(
  options,
  colorConfig,
  displayConfig = {},
  config = {}
) {
  if (!options) return options

  const result = JSON.parse(JSON.stringify(options))

  // 应用颜色主题
  if (colorConfig.theme) {
    if (colorConfig.theme === 'custom') {
      if (colorConfig.gradientMode && colorConfig.customGradients) {
        // 自定义渐变色模式
        const gradients = colorConfig.customGradients.map(gradient => {
          return createGradientConfig(
            gradient,
            colorConfig.gradientDirection || 'vertical'
          )
        })
        result.color = gradients
      } else if (colorConfig.customColors) {
        // 自定义纯色模式
        result.color = [...colorConfig.customColors]
      }
    } else if (colorThemes[colorConfig.theme]) {
      result.color = [...colorThemes[colorConfig.theme]]
    }
  }

  // 应用标题颜色
  if (colorConfig.titleColor) {
    result.title.textStyle.color = colorConfig.titleColor
  }

  // 应用坐标轴颜色 (只对有坐标轴的图表类型应用)
  if (colorConfig.xAxisColor && result.xAxis) {
    result.xAxis.axisLine.lineStyle.color = colorConfig.xAxisColor
    result.xAxis.axisLabel.color = colorConfig.xAxisColor
  }

  if (colorConfig.yAxisColor && result.yAxis) {
    result.yAxis.axisLine.lineStyle.color = colorConfig.yAxisColor
    result.yAxis.axisLabel.color = colorConfig.yAxisColor
  }

  // 应用网格线颜色 (只对有坐标轴的图表类型应用)
  if (colorConfig.gridColor && result.yAxis && result.yAxis.splitLine) {
    result.yAxis.splitLine.lineStyle.color = colorConfig.gridColor
  }

  // 应用图例配置
  if (colorConfig.legendColor || config.legendPosition || config.legendOrient) {
    const legendConfig = {
      ...result.legend
    }

    // 图例位置
    if (config.legendPosition) {
      const position = config.legendPosition
      if (position === 'top') {
        legendConfig.top = '10px'
        legendConfig.bottom = undefined
        legendConfig.left = 'center'
        legendConfig.right = undefined
      } else if (position === 'bottom') {
        legendConfig.top = undefined
        legendConfig.bottom = '10px'
        legendConfig.left = 'center'
        legendConfig.right = undefined
      } else if (position === 'left') {
        legendConfig.top = 'center'
        legendConfig.bottom = undefined
        legendConfig.left = '10px'
        legendConfig.right = undefined
      } else if (position === 'right') {
        legendConfig.top = 'center'
        legendConfig.bottom = undefined
        legendConfig.left = undefined
        legendConfig.right = '10px'
      }
    }

    // 图例方向
    if (config.legendOrient) {
      legendConfig.orient = config.legendOrient
    }

    // 图例文字颜色
    if (colorConfig.legendColor) {
      legendConfig.textStyle = {
        ...legendConfig.textStyle,
        color: colorConfig.legendColor
      }
    }

    result.legend = legendConfig
  }

  // 应用背景色
  if (colorConfig.backgroundColor) {
    result.backgroundColor = colorConfig.backgroundColor
  }

  // 应用提示框颜色
  if (colorConfig.tooltipBg) {
    result.tooltip.backgroundColor = colorConfig.tooltipBg
  }

  if (colorConfig.tooltipText) {
    result.tooltip.textStyle.color = colorConfig.tooltipText
  }

  // 应用显示配置
  if (displayConfig.showTitle === false) {
    result.title.show = false
  } else if (displayConfig.showTitle === true) {
    result.title.show = true
  }

  if (displayConfig.showLegend === false) {
    result.legend.show = false
  } else if (displayConfig.showLegend === true) {
    result.legend.show = true
  }

  if (displayConfig.showTooltip === false) {
    result.tooltip.show = false
  } else if (displayConfig.showTooltip === true) {
    result.tooltip.show = true
  }

  return result
}

// 更新柱状图配置
export const updateBarChartOptions = (
  baseOptions,
  barConfig,
  displayConfig = {}
) => {
  if (!baseOptions || !barConfig) {
    return baseOptions
  }

  const newOptions = cloneDeep(baseOptions)

  if (!newOptions.series) {
    return newOptions
  }

  const isHorizontal = barConfig.type?.includes('horizontal')

  if (isHorizontal) {
    // For horizontal bar charts, swap xAxis and yAxis
    ;[newOptions.xAxis, newOptions.yAxis] = [newOptions.yAxis, newOptions.xAxis]
  } else {
    // Ensure we are in a vertical state if we switch back from horizontal
    // This requires knowing the "default" state which might be tricky if not stored.
    // Assuming default is x: category, y: value
    if (newOptions.xAxis.type === 'value') {
      ;[newOptions.xAxis, newOptions.yAxis] = [
        newOptions.yAxis,
        newOptions.xAxis
      ]
    }
  }

  newOptions.series.forEach(s => {
    // Force the type to bar, in case we are switching from another chart type
    s.type = 'bar'
    s.barWidth = barConfig.barWidth
    s.barGap = `${barConfig.barGap}%`
    s.stack = barConfig.type?.includes('stack') ? 'total' : undefined

    if (barConfig.showValue) {
      s.label = {
        show: true,
        position: barConfig.valuePosition || 'inside',
        fontWeight: 'bold',
        fontSize: barConfig.valueFontSize || 12,
        color: barConfig.valueColor || '#333333'
      }
    } else {
      s.label = { show: false }
    }
  })

  // Set min/max on the value axis
  const valueAxis = isHorizontal ? newOptions.xAxis : newOptions.yAxis
  if (valueAxis) {
    valueAxis.min = barConfig.min
    valueAxis.max = barConfig.max
  }

  // 应用显示配置
  if (displayConfig.showTitle === false) {
    newOptions.title.show = false
  } else if (displayConfig.showTitle === true) {
    newOptions.title.show = true
  }

  if (displayConfig.showLegend === false) {
    newOptions.legend.show = false
  } else if (displayConfig.showLegend === true) {
    newOptions.legend.show = true
  }

  if (displayConfig.showTooltip === false) {
    newOptions.tooltip.show = false
  } else if (displayConfig.showTooltip === true) {
    newOptions.tooltip.show = true
  }

  return newOptions
}

// 更新折线图配置
export const updateLineChartOptions = (
  baseOptions,
  lineConfig,
  displayConfig = {}
) => {
  if (!baseOptions || !lineConfig) {
    return baseOptions
  }

  // Create a deep copy to avoid mutations
  const newOptions = cloneDeep(baseOptions)

  if (!newOptions.series) {
    return newOptions
  }

  const newSeries = newOptions.series.map(s => {
    // Regardless of original type, force it to 'line' for this configuration
    const newS = { ...s, type: 'line' }

    return {
      ...newS,
      smooth: lineConfig.type === 'smooth',
      step: lineConfig.type === 'step' ? 'start' : false, // or 'middle', 'end'
      areaStyle: lineConfig.type === 'area' ? {} : null,
      symbol: lineConfig.showSymbol ? undefined : 'none',
      lineStyle: {
        ...s.lineStyle,
        width: lineConfig.lineWidth
      }
    }
  })

  const finalOptions = {
    ...newOptions,
    series: newSeries
  }

  // 应用显示配置
  if (displayConfig.showTitle === false) {
    finalOptions.title.show = false
  } else if (displayConfig.showTitle === true) {
    finalOptions.title.show = true
  }

  if (displayConfig.showLegend === false) {
    finalOptions.legend.show = false
  } else if (displayConfig.showLegend === true) {
    finalOptions.legend.show = true
  }

  if (displayConfig.showTooltip === false) {
    finalOptions.tooltip.show = false
  } else if (displayConfig.showTooltip === true) {
    finalOptions.tooltip.show = true
  }

  return finalOptions
}

// 更新饼图配置
export const updatePieChartOptions = (
  baseOptions,
  pieConfig,
  displayConfig = {}
) => {
  if (!baseOptions || !pieConfig) {
    return baseOptions
  }

  // Create a deep copy to avoid mutations
  const newOptions = cloneDeep(baseOptions)

  if (!newOptions.series || !newOptions.series[0]) {
    return newOptions
  }

  // 更新饼图半径
  if (pieConfig.radius !== undefined) {
    // 支持数组格式 [内半径, 外半径] 和单一值格式
    let radius = pieConfig.radius

    // 确保数值格式转换为百分比字符串
    if (typeof radius === 'number') {
      radius = `${radius}%`
    } else if (Array.isArray(radius)) {
      radius = radius.map(r => (typeof r === 'number' ? `${r}%` : r))
    }

    newOptions.series[0].radius = radius
  } else {
    // 如果没有自定义半径，使用更大的默认值
    newOptions.series[0].radius = '75%'
  }

  // 更新饼图中心位置
  if (pieConfig.center !== undefined) {
    newOptions.series[0].center = pieConfig.center
  } else {
    // 如果没有自定义中心位置，确保完全居中
    newOptions.series[0].center = ['50%', '50%']
  }

  // 优化标题和图例位置以获得更好的空间利用
  if (newOptions.title) {
    newOptions.title.top = newOptions.title.top || '5%'
    newOptions.title.left = newOptions.title.left || 'center'
  }

  if (newOptions.legend) {
    newOptions.legend.bottom = newOptions.legend.bottom || '5%'
    newOptions.legend.left = newOptions.legend.left || 'center'
  }

  // 应用显示配置
  if (displayConfig.showTitle === false) {
    newOptions.title.show = false
  } else if (displayConfig.showTitle === true) {
    newOptions.title.show = true
  }

  if (displayConfig.showLegend === false) {
    newOptions.legend.show = false
  } else if (displayConfig.showLegend === true) {
    newOptions.legend.show = true
  }

  if (displayConfig.showTooltip === false) {
    newOptions.tooltip.show = false
  } else if (displayConfig.showTooltip === true) {
    newOptions.tooltip.show = true
  }

  return newOptions
}

// 获取颜色主题
export function getColorTheme(themeName) {
  return colorThemes[themeName] || colorThemes.default
}

export { colorThemes }
