# Monaco Editor 离线使用配置说明

## 概述

本项目中的 `@guolao/vue-monaco-editor` 已配置为离线使用，不依赖外部 CDN。

## 配置说明

### 1. 依赖安装

已在 `package.json` 中添加了 `monaco-editor` 依赖：

```json
{
  "dependencies": {
    "monaco-editor": "^0.52.2"
  }
}
```

### 2. Vite 配置

在 `vite.config.js` 中配置了 Monaco Editor 的优化依赖：

```javascript
export default defineConfig({
  plugins: [vue()],
  optimizeDeps: {
    include: [
      'monaco-editor/esm/vs/editor/editor.main.js',
      'monaco-editor/esm/vs/language/json/json.worker',
      'monaco-editor/esm/vs/language/css/css.worker',
      'monaco-editor/esm/vs/language/html/html.worker',
      'monaco-editor/esm/vs/language/typescript/ts.worker',
      'monaco-editor/esm/vs/editor/editor.worker'
    ]
  }
})
```

### 3. 主入口配置

在 `src/main.js` 中配置了离线 Monaco Editor：

```javascript
// 配置 Monaco Editor 离线使用
import { loader } from "@guolao/vue-monaco-editor"
import * as monaco from "monaco-editor"
import editorWorker from "monaco-editor/esm/vs/editor/editor.worker?worker"
import jsonWorker from "monaco-editor/esm/vs/language/json/json.worker?worker"
import cssWorker from "monaco-editor/esm/vs/language/css/css.worker?worker"
import htmlWorker from "monaco-editor/esm/vs/language/html/html.worker?worker"
import tsWorker from "monaco-editor/esm/vs/language/typescript/ts.worker?worker"

// 配置 Monaco Editor 的 worker 环境
self.MonacoEnvironment = {
  getWorker(_, label) {
    if (label === "json") {
      return new jsonWorker()
    }
    if (label === "css" || label === "scss" || label === "less") {
      return new cssWorker()
    }
    if (label === "html" || label === "handlebars" || label === "razor") {
      return new htmlWorker()
    }
    if (label === "typescript" || label === "javascript") {
      return new tsWorker()
    }
    return new editorWorker()
  }
}

// 配置 loader 使用本地的 monaco-editor
loader.config({ monaco })
```

## 使用方法

配置完成后，你可以在组件中正常使用 Monaco Editor：

```vue
<template>
  <vue-monaco-editor
    v-model:value="code"
    theme="vs-dark"
    language="json"
    :options="editorOptions"
    @mount="handleMount"
  />
</template>

<script setup>
import { VueMonacoEditor } from '@guolao/vue-monaco-editor'
import { ref } from 'vue'

const code = ref('{"hello": "world"}')
const editorOptions = {
  automaticLayout: true,
  formatOnType: true,
  formatOnPaste: true,
}

const handleMount = (editor) => {
  console.log('Editor mounted:', editor)
}
</script>
```

## 优势

1. **完全离线**：不需要访问外部 CDN
2. **性能更好**：资源直接从本地加载
3. **稳定性高**：不依赖网络状况
4. **安全性**：不会向外部发送请求

## 注意事项

1. 首次构建时间可能会稍长，因为需要打包 Monaco Editor 资源
2. 打包后的体积会增加约 5-10MB
3. 如果需要其他语言支持，需要额外导入对应的 worker

## 内网部署建议

对于内网环境，建议：

1. 使用本配置确保完全离线运行
2. 定期更新 `monaco-editor` 版本以获得最新功能
3. 根据实际需要调整支持的语言类型以减少打包体积

## 故障排除

如果遇到问题：

1. 确保 `monaco-editor` 依赖已正确安装
2. 检查浏览器控制台是否有错误信息
3. 验证 Vite 配置是否正确应用
4. 清除 `node_modules` 并重新安装依赖

```bash
rm -rf node_modules
npm install
# 或
pnpm install
``` 