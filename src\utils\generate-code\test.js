import { generateCode } from './index.js'

// 测试schema - 简化版本
const testSchema = {
  canvas: {
    width: 1920,
    height: 1080
  },
  version: '2.0',
  components: [
    {
      id: 'rootContainer',
      type: 'div-container',
      name: '根容器',
      position: {
        x: 0,
        y: 0
      },
      size: {
        width: 1920,
        height: 1080
      },
      config: {
        backgroundColor: '#ffffff'
      },
      fullCanvas: true,
      children: [
        {
          id: 'leftContainer',
          type: 'div-container',
          name: '左侧容器',
          position: {
            x: 12,
            y: 12
          },
          size: {
            width: 944,
            height: 1056
          },
          config: {
            backgroundColor: '#f8f9fa'
          },
          children: [
            {
              id: 'innerContainer',
              type: 'div-container',
              name: '内部容器',
              position: {
                x: 24,
                y: 24
              },
              size: {
                width: 900,
                height: 500
              },
              config: {
                backgroundColor: '#ffffff'
              }
            }
          ]
        },
        {
          id: 'rightContainer',
          type: 'div-container',
          name: '右侧容器',
          position: {
            x: 964,
            y: 12
          },
          size: {
            width: 944,
            height: 1056
          },
          config: {
            backgroundColor: '#f8f9fa'
          }
        }
      ]
    }
  ]
}

// 生成代码
try {
  const generatedCode = generateCode(testSchema, 'vue3')
  console.log('生成的Vue3代码:')
  console.log('='.repeat(50))
  console.log(generatedCode)
} catch (error) {
  console.error('代码生成错误:', error)
}

// 测试Vue2代码生成
export function testVue2Generation() {
  try {
    const vue2Code = generateCode(testSchema, 'vue2')
    console.log('=== Vue2 代码生成测试 ===')
    console.log(vue2Code)
    return vue2Code
  } catch (error) {
    console.error('Vue2代码生成失败:', error)
    return null
  }
}

// 测试Vue3代码生成
export function testVue3Generation() {
  try {
    const vue3Code = generateCode(testSchema, 'vue3')
    console.log('=== Vue3 代码生成测试 ===')
    console.log(vue3Code)
    return vue3Code
  } catch (error) {
    console.error('Vue3代码生成失败:', error)
    return null
  }
}

// 在浏览器控制台中可以调用这些函数进行测试
if (typeof window !== 'undefined') {
  window.testCodeGeneration = {
    testVue2: testVue2Generation,
    testVue3: testVue3Generation,
    testSchema
  }
}
