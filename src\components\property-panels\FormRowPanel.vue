<template>
  <div class="form-row-panel">
    <!-- 基础属性 -->
    <div class="panel-section">
      <h4 class="section-title">基础设置</h4>

      <ElFormItem label="行高度">
        <ElInputNumber
          :model-value="localConfig.height || 60"
          @change="updateProperty('height', $event)"
          :min="40"
          :max="200"
          size="small"
          controls-position="right"
        />
        <span class="property-hint">像素</span>
      </ElFormItem>

      <ElFormItem label="背景色">
        <ElColorPicker
          :model-value="localConfig.backgroundColor || 'transparent'"
          @change="updateProperty('backgroundColor', $event)"
          size="small"
          show-alpha
        />
      </ElFormItem>

      <ElFormItem label="内边距">
        <ElInputNumber
          :model-value="localConfig.padding || 8"
          @change="updateProperty('padding', $event)"
          :min="0"
          :max="50"
          size="small"
          controls-position="right"
        />
        <span class="property-hint">像素</span>
      </ElFormItem>
    </div>

    <!-- 列配置 -->
    <div class="panel-section">
      <h4 class="section-title">列配置</h4>

      <ElFormItem label="列数量">
        <ElInputNumber
          :model-value="columnCount"
          @change="handleColumnCountChange"
          :min="1"
          :max="6"
          size="small"
          controls-position="right"
        />
      </ElFormItem>

      <ElFormItem label="列宽设置" v-if="columnCount > 0">
        <div class="column-spans-container">
          <div
            v-for="(span, index) in columnSpans"
            :key="index"
            class="column-span-item"
          >
            <label class="column-label">第{{ index + 1 }}列</label>
            <ElInputNumber
              :model-value="span"
              @change="handleColumnSpanChange(index, $event)"
              :min="1"
              :max="24"
              size="small"
              controls-position="right"
            />
            <span class="span-unit">格</span>
          </div>
          <div class="span-total" :class="{ 'span-warning': totalSpan !== 24 }">
            总计：{{ totalSpan }}/24 格
          </div>
        </div>
      </ElFormItem>

      <!-- 快速设置按钮 -->
      <ElFormItem label="快速设置" v-if="columnCount > 1">
        <div class="quick-actions">
          <ElButton size="small" @click="setEqualSpans" type="primary" plain>
            平均分配
          </ElButton>
          <ElButton
            v-if="columnCount === 2"
            size="small"
            @click="setSpans([8, 16])"
            plain
          >
            1:2
          </ElButton>
          <ElButton
            v-if="columnCount === 2"
            size="small"
            @click="setSpans([16, 8])"
            plain
          >
            2:1
          </ElButton>
          <ElButton
            v-if="columnCount === 3"
            size="small"
            @click="setSpans([6, 12, 6])"
            plain
          >
            1:2:1
          </ElButton>
        </div>
      </ElFormItem>

      <!-- 列操作 -->
      <ElFormItem label="列操作">
        <div class="column-actions">
          <ElButton
            size="small"
            type="success"
            @click="addColumn"
            :disabled="columnCount >= 6"
          >
            添加列
          </ElButton>
          <ElButton
            size="small"
            type="warning"
            @click="removeColumn"
            :disabled="columnCount <= 1"
          >
            删除列
          </ElButton>
        </div>
      </ElFormItem>
    </div>

    <!-- 边框设置 -->
    <div class="panel-section">
      <h4 class="section-title">边框设置</h4>

      <ElFormItem label="边框宽度">
        <ElInputNumber
          :model-value="localConfig.borderWidth || 0"
          @change="updateProperty('borderWidth', $event)"
          :min="0"
          :max="10"
          size="small"
          controls-position="right"
        />
        <span class="property-hint">像素</span>
      </ElFormItem>

      <ElFormItem label="边框颜色">
        <ElColorPicker
          :model-value="localConfig.borderColor || '#e4e7ed'"
          @change="updateProperty('borderColor', $event)"
          size="small"
        />
      </ElFormItem>

      <ElFormItem label="边框样式">
        <ElSelect
          :model-value="localConfig.borderStyle || 'solid'"
          @change="updateProperty('borderStyle', $event)"
          size="small"
        >
          <ElOption label="实线" value="solid" />
          <ElOption label="虚线" value="dashed" />
          <ElOption label="点线" value="dotted" />
          <ElOption label="无边框" value="none" />
        </ElSelect>
      </ElFormItem>
    </div>
  </div>
</template>

<script setup>
  import { computed, ref, watch } from 'vue'
  import {
    ElFormItem,
    ElInputNumber,
    ElColorPicker,
    ElSelect,
    ElOption,
    ElButton,
    ElMessage
  } from 'element-plus'

  const props = defineProps({
    formRowConfig: {
      type: Object,
      required: true
    }
  })

  const emit = defineEmits(['update-property'])

  const localConfig = computed(() => props.formRowConfig || {})

  // 计算当前列数
  const columnCount = computed(() => {
    return localConfig.value.columns?.length || 0
  })

  // 计算当前列的span值
  const columnSpans = ref([])

  // 计算总span值
  const totalSpan = computed(() => {
    return columnSpans.value.reduce((sum, span) => sum + span, 0)
  })

  // 初始化列span值
  const initColumnSpans = () => {
    const count = columnCount.value
    if (count > 0) {
      // 如果已有配置，使用已有配置
      if (
        localConfig.value.columnSpans &&
        localConfig.value.columnSpans.length === count
      ) {
        columnSpans.value = [...localConfig.value.columnSpans]
      } else {
        // 否则使用平均分配
        const evenSpan = Math.floor(24 / count)
        const remainder = 24 % count
        const newSpans = new Array(count).fill(evenSpan)

        // 将余数分配给前面的列
        for (let i = 0; i < remainder; i++) {
          newSpans[i]++
        }

        columnSpans.value = newSpans
        updateProperty('columnSpans', columnSpans.value)
      }
    }
  }

  // 监听列数变化
  watch(() => columnCount.value, initColumnSpans, { immediate: true })

  function updateProperty(key, value) {
    emit('update-property', key, value)
  }

  // 处理列数量变化
  const handleColumnCountChange = newCount => {
    if (newCount < 1 || newCount > 6) return

    const currentCount = columnCount.value

    if (newCount === currentCount) return

    // 创建新的列数组
    const newColumns = []

    if (newCount > currentCount) {
      // 增加列：保留现有列，添加新列
      if (localConfig.value.columns) {
        newColumns.push(...localConfig.value.columns)
      }

      // 添加新列
      for (let i = currentCount; i < newCount; i++) {
        newColumns.push({
          id: `col_${Date.now()}_${i}`,
          children: [],
          borderWidth: 1,
          borderStyle: 'dashed',
          borderColor: '#d9d9d9',
          backgroundColor: 'transparent',
          padding: 8
        })
      }
    } else {
      // 减少列：只保留前面的列
      if (localConfig.value.columns) {
        newColumns.push(...localConfig.value.columns.slice(0, newCount))
      }
    }

    // 更新列配置
    updateProperty('columns', newColumns)

    // 重新计算span值，平均分配
    const evenSpan = Math.floor(24 / newCount)
    const remainder = 24 % newCount
    const newSpans = new Array(newCount).fill(evenSpan)

    // 将余数分配给前面的列
    for (let i = 0; i < remainder; i++) {
      newSpans[i]++
    }

    columnSpans.value = newSpans
    updateProperty('columnSpans', newSpans)

    ElMessage.success(
      `已${newCount > currentCount ? '添加' : '删除'}列，当前${newCount}列`
    )
  }

  // 处理单个列span值变化
  const handleColumnSpanChange = (index, value) => {
    if (value < 1) value = 1
    if (value > 24) value = 24

    const newSpans = [...columnSpans.value]
    newSpans[index] = value
    columnSpans.value = newSpans
    updateProperty('columnSpans', newSpans)

    // 检查总和是否为24
    const total = newSpans.reduce((sum, span) => sum + span, 0)
    if (total !== 24) {
      ElMessage.warning(`当前总计${total}格，建议调整为24格`)
    }
  }

  // 设置平均分配
  const setEqualSpans = () => {
    const count = columnCount.value
    const evenSpan = Math.floor(24 / count)
    const remainder = 24 % count
    const newSpans = new Array(count).fill(evenSpan)

    // 将余数分配给前面的列
    for (let i = 0; i < remainder; i++) {
      newSpans[i]++
    }

    columnSpans.value = newSpans
    updateProperty('columnSpans', newSpans)
    ElMessage.success('已设置为平均分配')
  }

  // 设置指定span值
  const setSpans = spans => {
    if (spans.length !== columnCount.value) return

    const total = spans.reduce((sum, span) => sum + span, 0)
    if (total !== 24) {
      ElMessage.warning(`设置的span值总和为${total}，不等于24`)
      return
    }

    columnSpans.value = [...spans]
    updateProperty('columnSpans', spans)

    const ratioText = spans.join(':')
    ElMessage.success(`已设置为 ${ratioText} 比例`)
  }

  // 添加列
  const addColumn = () => {
    if (columnCount.value >= 6) return
    handleColumnCountChange(columnCount.value + 1)
  }

  // 删除列
  const removeColumn = () => {
    if (columnCount.value <= 1) return
    handleColumnCountChange(columnCount.value - 1)
  }
</script>

<style scoped>
  .form-row-panel {
    padding: 16px;
  }

  .panel-section {
    margin-bottom: 24px;
  }

  .section-title {
    font-size: 14px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 12px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }

  .property-hint {
    font-size: 12px;
    color: #909399;
    margin-left: 8px;
  }

  .column-spans-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .column-span-item {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .column-label {
    font-size: 12px;
    color: #606266;
    width: 50px;
    flex-shrink: 0;
  }

  .column-span-item .el-input-number {
    flex: 1;
    max-width: 80px;
  }

  .span-unit {
    font-size: 12px;
    color: #909399;
    width: 20px;
    flex-shrink: 0;
  }

  .span-total {
    font-size: 12px;
    color: #606266;
    padding: 8px;
    background-color: #f5f7fa;
    border-radius: 4px;
    text-align: center;
    margin-top: 8px;
  }

  .span-total.span-warning {
    background-color: #fef0f0;
    color: #f56c6c;
    border: 1px solid #fbc4c4;
  }

  .warning-text {
    font-weight: 500;
  }

  .quick-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
  }

  .quick-actions .el-button {
    flex: none;
    font-size: 12px;
    padding: 4px 8px;
  }

  .column-actions {
    display: flex;
    gap: 8px;
  }

  .column-actions .el-button {
    flex: 1;
  }

  :deep(.el-form-item) {
    margin-bottom: 12px;
  }

  :deep(.el-form-item__label) {
    font-size: 13px;
    color: #606266;
    font-weight: 500;
  }

  :deep(.el-input-number) {
    width: 100%;
  }

  :deep(.el-select) {
    width: 100%;
  }

  :deep(.el-color-picker) {
    width: 100%;
  }
</style>
