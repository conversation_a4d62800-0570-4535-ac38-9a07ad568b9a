<template>
  <div class="select-panel">
    <ElFormItem label="标签文本">
      <ElInput
        :model-value="localConfig.label || '下拉选择'"
        @input="updateProperty('label', $event)"
        size="small"
        placeholder="请输入标签文本"
      />
    </ElFormItem>

    <ElFormItem label="占位符">
      <ElInput
        :model-value="localConfig.placeholder || '请选择'"
        @input="updateProperty('placeholder', $event)"
        size="small"
        placeholder="请输入占位符文本"
      />
    </ElFormItem>

    <ElFormItem label="默认值">
      <ElInput
        :model-value="localConfig.value || ''"
        @input="updateProperty('value', $event)"
        size="small"
        placeholder="请输入默认值"
      />
    </ElFormItem>

    <!-- 变量绑定设置 -->
    <ElFormItem label="变量绑定">
      <ElSelect
        :model-value="localConfig.bindVariable || ''"
        @change="updateProperty('bindVariable', $event)"
        size="small"
        placeholder="选择绑定的变量"
        clearable
      >
        <ElOption label="不绑定变量" value="" />
        <ElOption
          v-for="variable in availableVariables"
          :key="variable.id"
          :label="`${variable.name} (${variable.type})`"
          :value="variable.id"
        />
      </ElSelect>
    </ElFormItem>

    <!-- 变量绑定提示 -->
    <ElFormItem
      v-if="localConfig.bindVariable && selectedVariable"
      label="绑定信息"
    >
      <div class="variable-info">
        <div class="variable-info-item">
          <span class="info-label">变量名:</span>
          <span class="info-value">{{ selectedVariable.name }}</span>
        </div>
        <div class="variable-info-item">
          <span class="info-label">类型:</span>
          <span class="info-value">{{ selectedVariable.type }}</span>
        </div>
        <div class="variable-info-item">
          <span class="info-label">描述:</span>
          <span class="info-value">{{
            selectedVariable.description || '无'
          }}</span>
        </div>
      </div>
    </ElFormItem>

    <ElFormItem label="选项配置">
      <div class="options-container">
        <div
          v-for="(option, index) in localOptions"
          :key="index"
          class="option-item"
        >
          <ElInput
            :model-value="option.label"
            @input="updateOptionLabel(index, $event)"
            size="small"
            placeholder="显示文本"
          />
          <ElInput
            :model-value="option.value"
            @input="updateOptionValue(index, $event)"
            size="small"
            placeholder="选项值"
          />
          <ElButton
            type="danger"
            size="small"
            @click="removeOption(index)"
            v-if="localOptions.length > 1"
          >
            删除
          </ElButton>
        </div>
        <ElButton
          type="dashed"
          size="small"
          @click="addOption"
          class="add-option-btn"
        >
          添加选项
        </ElButton>
      </div>
    </ElFormItem>

    <ElFormItem label="尺寸大小">
      <ElSelect
        :model-value="localConfig.size || 'default'"
        @change="updateProperty('size', $event)"
        size="small"
      >
        <ElOption label="大" value="large" />
        <ElOption label="默认" value="default" />
        <ElOption label="小" value="small" />
      </ElSelect>
    </ElFormItem>

    <ElFormItem label="标签宽度">
      <ElInput
        :model-value="localConfig.labelWidth || '80px'"
        @input="updateProperty('labelWidth', $event)"
        size="small"
        placeholder="例: 80px"
      />
    </ElFormItem>

    <ElFormItem label="标签位置">
      <ElSelect
        :model-value="localConfig.labelPosition || 'left'"
        @change="updateProperty('labelPosition', $event)"
        size="small"
      >
        <ElOption label="左侧" value="left" />
        <ElOption label="右侧" value="right" />
        <ElOption label="顶部" value="top" />
      </ElSelect>
    </ElFormItem>

    <ElFormItem label="多选">
      <ElSwitch
        :model-value="localConfig.multiple || false"
        @change="updateProperty('multiple', $event)"
      />
    </ElFormItem>

    <ElFormItem label="可清空">
      <ElSwitch
        :model-value="localConfig.clearable !== false"
        @change="updateProperty('clearable', $event)"
      />
    </ElFormItem>

    <ElFormItem label="可过滤">
      <ElSwitch
        :model-value="localConfig.filterable || false"
        @change="updateProperty('filterable', $event)"
      />
    </ElFormItem>

    <ElFormItem label="禁用">
      <ElSwitch
        :model-value="localConfig.disabled || false"
        @change="updateProperty('disabled', $event)"
      />
    </ElFormItem>
  </div>
</template>

<script setup>
  import { computed, ref, watch } from 'vue'
  import {
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElSwitch,
    ElButton
  } from 'element-plus'

  const props = defineProps({
    selectConfig: {
      type: Object,
      required: true
    },
    formVariables: {
      type: Array,
      default: () => []
    }
  })

  const emit = defineEmits(['update-property'])

  const localConfig = computed({
    get: () => props.selectConfig,
    set: () => {
      // We don't use the setter, updates are handled via events
    }
  })

  // 获取可用的变量列表（适合选择框的变量类型）
  const availableVariables = computed(() => {
    const suitableTypes = ['string', 'number', 'array']
    return (props.formVariables || []).filter(variable =>
      suitableTypes.includes(variable.type)
    )
  })

  // 获取当前绑定的变量信息
  const selectedVariable = computed(() => {
    if (!localConfig.value.bindVariable) return null
    return availableVariables.value.find(
      variable => variable.id === localConfig.value.bindVariable
    )
  })

  const localOptions = ref(
    localConfig.value.options
      ? [...localConfig.value.options]
      : [
          { label: '选项1', value: 'option1' },
          { label: '选项2', value: 'option2' },
          { label: '选项3', value: 'option3' }
        ]
  )

  watch(
    () => localConfig.value.options,
    newOptions => {
      if (newOptions) {
        localOptions.value = [...newOptions]
      }
    },
    { deep: true }
  )

  function updateProperty(key, value) {
    emit('update-property', `config.${key}`, value)
  }

  function updateOptionLabel(index, value) {
    localOptions.value[index].label = value
    updateProperty('options', [...localOptions.value])
  }

  function updateOptionValue(index, value) {
    localOptions.value[index].value = value
    updateProperty('options', [...localOptions.value])
  }

  function addOption() {
    localOptions.value.push({
      label: `选项${localOptions.value.length + 1}`,
      value: `option${localOptions.value.length + 1}`
    })
    updateProperty('options', [...localOptions.value])
  }

  function removeOption(index) {
    localOptions.value.splice(index, 1)
    updateProperty('options', [...localOptions.value])
  }
</script>

<style scoped>
  .select-panel {
    padding: 8px 0;
  }

  .el-select {
    width: 100%;
  }

  .options-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .option-item {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .option-item .el-input {
    flex: 1;
  }

  .add-option-btn {
    width: 100%;
    margin-top: 4px;
  }

  .variable-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    background: #f5f7fa;
    padding: 8px;
    border-radius: 4px;
    font-size: 12px;
  }

  .variable-info-item {
    display: flex;
    justify-content: space-between;
  }

  .info-label {
    color: #606266;
    font-weight: 500;
  }

  .info-value {
    color: #303133;
  }
</style>
