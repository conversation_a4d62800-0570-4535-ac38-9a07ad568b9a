<template>
  <teleport to="body">
    <div
      v-if="visible"
      class="context-menu"
      :style="menuStyle"
      @click.stop
      @contextmenu.prevent
    >
      <!-- 画布菜单 -->
      <template v-if="menuType === 'canvas'">
        <div class="context-menu-item" @click="handleCreateDiv">
          <el-icon class="menu-icon"><Box /></el-icon>
          <span>创建DIV容器</span>
        </div>
      </template>

      <!-- DIV容器菜单 -->
      <template v-else-if="menuType === 'div-container'">
        <div
          class="context-menu-item submenu-item"
          @mouseenter="showSubmenu = 'split'"
          @mouseleave="showSubmenu = null"
        >
          <el-icon class="menu-icon"><Grid /></el-icon>
          <span>拆分</span>
          <el-icon class="arrow-icon"><ArrowRight /></el-icon>

          <!-- 拆分子菜单 -->
          <div
            v-if="
              showSubmenu === 'split' ||
              showSubmenu === 'horizontal' ||
              showSubmenu === 'vertical'
            "
            class="submenu"
            :style="submenuStyle"
            @mouseenter="
              showSubmenu = showSubmenu === 'split' ? 'split' : showSubmenu
            "
            @mouseleave="showSubmenu = null"
          >
            <div
              class="context-menu-item submenu-item"
              @mouseenter="showSubmenu = 'horizontal'"
            >
              <el-icon class="menu-icon"><Operation /></el-icon>
              <span>水平拆分</span>
              <el-icon class="arrow-icon"><ArrowRight /></el-icon>

              <!-- 水平拆分个数子菜单 -->
              <div
                v-if="showSubmenu === 'horizontal'"
                class="submenu split-count-submenu"
                :style="thirdSubmenuStyle"
                @mouseenter="showSubmenu = 'horizontal'"
                @mouseleave="showSubmenu = 'split'"
              >
                <div
                  v-for="count in [2, 3, 4]"
                  :key="count"
                  class="context-menu-item"
                  @click="handleSplit('horizontal', count)"
                >
                  <span>{{ count }}个</span>
                </div>
              </div>
            </div>
            <div
              class="context-menu-item submenu-item"
              @mouseenter="showSubmenu = 'vertical'"
            >
              <el-icon class="menu-icon"><Menu /></el-icon>
              <span>垂直拆分</span>
              <el-icon class="arrow-icon"><ArrowRight /></el-icon>

              <!-- 垂直拆分个数子菜单 -->
              <div
                v-if="showSubmenu === 'vertical'"
                class="submenu split-count-submenu"
                :style="thirdSubmenuStyle"
                @mouseenter="showSubmenu = 'vertical'"
                @mouseleave="showSubmenu = 'split'"
              >
                <div
                  v-for="count in [2, 3, 4]"
                  :key="count"
                  class="context-menu-item"
                  @click="handleSplit('vertical', count)"
                >
                  <span>{{ count }}个</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 表单容器菜单 -->
      <template v-else-if="menuType === 'form-container'">
        <div class="context-menu-item" @click="handleAddRow">
          <el-icon class="menu-icon"><Plus /></el-icon>
          <span>新增行</span>
        </div>
      </template>

      <!-- 表单行菜单 -->
      <template v-else-if="menuType === 'form-row'">
        <div
          class="context-menu-item submenu-item"
          @mouseenter="showSubmenu = 'split-column'"
          @mouseleave="showSubmenu = null"
        >
          <el-icon class="menu-icon"><Grid /></el-icon>
          <span>拆分列</span>
          <el-icon class="arrow-icon"><ArrowRight /></el-icon>

          <!-- 拆分列子菜单 -->
          <div
            v-if="showSubmenu === 'split-column'"
            class="submenu column-submenu"
            :style="submenuStyle"
          >
            <div class="column-grid">
              <div
                v-for="cols in 24"
                :key="cols"
                class="context-menu-item column-item"
                @click="handleSplitColumn(cols)"
              >
                <span>{{ cols }}列</span>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 表单列菜单 -->
      <template v-else-if="menuType === 'form-column'">
        <div class="context-menu-item delete-item" @click="handleDeleteColumn">
          <el-icon class="menu-icon"><Delete /></el-icon>
          <span>删除列</span>
        </div>
      </template>
    </div>
  </teleport>
</template>

<script setup>
  import { computed, watch, ref } from 'vue'
  import { ElIcon } from 'element-plus'
  import {
    Box,
    Grid,
    ArrowRight,
    Operation,
    Menu,
    Plus,
    Delete
  } from '@element-plus/icons-vue'

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    x: {
      type: Number,
      default: 0
    },
    y: {
      type: Number,
      default: 0
    },
    menuType: {
      type: String,
      default: 'canvas', // 'canvas' | 'div-container' | 'form-container' | 'form-row' | 'form-column'
      validator: value =>
        [
          'canvas',
          'div-container',
          'form-container',
          'form-row',
          'form-column'
        ].includes(value)
    },
    targetComponent: {
      type: Object,
      default: null
    }
  })

  const emit = defineEmits([
    'create-div',
    'split-div',
    'add-form-row',
    'split-form-column',
    'delete-form-column',
    'close'
  ])

  const showSubmenu = ref(null)

  const menuStyle = computed(() => ({
    left: `${props.x}px`,
    top: `${props.y}px`
  }))

  const submenuStyle = computed(() => ({
    left: '100%',
    top: '0'
  }))

  const thirdSubmenuStyle = computed(() => ({
    left: '100%',
    top: '0'
  }))

  // 监听visible变化
  watch(
    () => props.visible,
    newVisible => {
      if (!newVisible) {
        showSubmenu.value = null
      }
    }
  )

  const handleCreateDiv = () => {
    emit('create-div')
    emit('close')
  }

  const handleSplit = (direction, count = 2) => {
    emit('split-div', {
      component: props.targetComponent,
      direction: direction, // 'horizontal' | 'vertical'
      count: count // 拆分个数，默认2个
    })
    emit('close')
  }

  const handleAddRow = () => {
    emit('add-form-row', {
      component: props.targetComponent
    })
    emit('close')
  }

  const handleSplitColumn = cols => {
    emit('split-form-column', {
      component: props.targetComponent,
      columns: cols
    })
    emit('close')
  }

  const handleDeleteColumn = () => {
    emit('delete-form-column', {
      column: props.targetComponent.column,
      row: props.targetComponent.row,
      formContainer: props.targetComponent.formContainer
    })
    emit('close')
  }
</script>

<style scoped>
  .context-menu {
    position: fixed;
    z-index: 9999;
    background: #ffffff;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 4px 0;
    min-width: 120px;
  }

  .context-menu-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 14px;
    color: #606266;
    transition: background-color 0.2s;
    position: relative;
  }

  .context-menu-item:hover {
    background-color: #f5f7fa;
    color: #409eff;
  }

  .submenu-item {
    justify-content: space-between;
  }

  .submenu {
    position: absolute;
    background: #ffffff;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 4px 0;
    min-width: 120px;
    z-index: 10000;
  }

  .menu-icon {
    margin-right: 8px;
    font-size: 16px;
  }

  .arrow-icon {
    font-size: 12px;
    color: #909399;
  }

  .column-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 2px;
    padding: 4px;
    max-width: 240px;
  }

  .column-item {
    padding: 6px 8px;
    text-align: center;
    border-radius: 3px;
    font-size: 12px;
    color: #606266;
    transition: background-color 0.2s;
    cursor: pointer;
  }

  .column-item:hover {
    background-color: #f5f7fa;
    color: #409eff;
  }

  .column-submenu {
    min-width: auto;
  }

  .split-count-submenu {
    min-width: 80px;
  }

  .delete-item {
    color: #f56c6c;
  }

  .delete-item:hover {
    background-color: #fef0f0;
    color: #f56c6c;
  }
</style>
