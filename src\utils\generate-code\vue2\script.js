/**
 * Vue2 Script生成器
 */

// 生成Vue2的script部分
export function generateScript(schema) {
  const { canvas, components } = schema
  const imports = generateImports(schema)
  const data = generateData(canvas, components)
  const computed = generateComputed(canvas, components)
  const methods = generateMethods(components)
  const mounted = generateMounted(components)

  return `<script>
${imports}

export default {
  name: 'GeneratedDashboard',
  data() {
    return {
${data}
    }
  },
  computed: {
${computed}
  },
  methods: {
${methods}
  },
  mounted() {
${mounted}
  }
}
</script>`
}

// 生成导入语句
function generateImports(schema) {
  const dependencies = analyzeDependencies(schema)

  let imports = []

  // 第三方库
  if (dependencies.libraries.axios) {
    imports.push(`import axios from 'axios'`)
  }
  if (dependencies.libraries.dayjs) {
    imports.push(`import dayjs from 'dayjs'`)
  }
  if (dependencies.libraries.echarts) {
    imports.push(`import * as echarts from 'echarts'`)
  }

  // Element UI 组件
  const elComponents = []
  if (dependencies.elementUI.Button) elComponents.push('Button')
  if (dependencies.elementUI.Input) elComponents.push('Input')
  if (dependencies.elementUI.Select) elComponents.push('Select')
  if (dependencies.elementUI.Option) elComponents.push('Option')
  if (dependencies.elementUI.DatePicker) elComponents.push('DatePicker')
  if (dependencies.elementUI.Message) elComponents.push('Message')
  if (dependencies.elementUI.Form) elComponents.push('Form')
  if (dependencies.elementUI.FormItem) elComponents.push('FormItem')
  if (dependencies.elementUI.Row) elComponents.push('Row')
  if (dependencies.elementUI.Col) elComponents.push('Col')

  if (elComponents.length > 0) {
    imports.push(`import { ${elComponents.join(', ')} } from 'element-ui'`)
  }

  return imports.join('\n')
}

// 分析依赖
function analyzeDependencies(schema) {
  const dependencies = {
    libraries: {
      axios: false,
      dayjs: false,
      echarts: false
    },
    elementUI: {
      Button: false,
      Input: false,
      Select: false,
      Option: false,
      DatePicker: false,
      Message: false,
      Form: false,
      FormItem: false,
      Row: false,
      Col: false
    }
  }

  // 分析组件
  analyzeComponents(schema.components, dependencies)

  // 如果需要axios，则需要Message来显示错误
  if (dependencies.libraries.axios) {
    dependencies.elementUI.Message = true
  }

  return dependencies
}

// 分析组件依赖
function analyzeComponents(components, dependencies) {
  if (!components || !Array.isArray(components)) {
    return
  }

  components.forEach(component => {
    const { type, children } = component

    switch (type) {
      case 'button':
        dependencies.elementUI.Button = true
        dependencies.elementUI.Message = true // 按钮点击可能显示消息
        break
      case 'input':
        dependencies.elementUI.Input = true
        break
      case 'select':
        dependencies.elementUI.Select = true
        dependencies.elementUI.Option = true
        break
      case 'date-picker':
      case 'datepicker':
        dependencies.elementUI.DatePicker = true
        break
      case 'form-container': {
        dependencies.elementUI.Form = true
        dependencies.elementUI.FormItem = true
        dependencies.elementUI.Row = true
        dependencies.elementUI.Col = true

        // 分析表单子组件
        const formChildren = getFormChildrenForAnalysis(component)
        formChildren.forEach(child => {
          switch (child.type) {
            case 'input':
              dependencies.elementUI.Input = true
              break
            case 'select':
              dependencies.elementUI.Select = true
              dependencies.elementUI.Option = true
              break
            case 'datepicker':
              dependencies.elementUI.DatePicker = true
              break
            case 'button':
              dependencies.elementUI.Button = true
              dependencies.elementUI.Message = true
              break
          }
        })
        break
      }
      case 'bar-chart':
      case 'line-chart':
      case 'pie-chart':
        dependencies.libraries.echarts = true
        break
    }

    // 检查是否需要数据获取
    if (checkIfNeedsFetchDataForComponent(component)) {
      dependencies.libraries.axios = true
    }

    // 递归处理子组件
    if (children && children.length > 0) {
      analyzeComponents(children, dependencies)
    }
  })
}

// 获取表单子组件用于依赖分析
function getFormChildrenForAnalysis(component) {
  const { children, config } = component
  let formChildren = []

  // 方式1：直接在children中
  if (children && children.length > 0) {
    formChildren = [...children]
  }

  // 方式2：在config.children中
  if (config?.children && config.children.length > 0) {
    formChildren = [...formChildren, ...config.children]
  }

  // 方式3：在config.rows的行列结构中
  if (config?.rows && config.rows.length > 0) {
    config.rows.forEach(row => {
      if (row.columns && row.columns.length > 0) {
        row.columns.forEach(column => {
          if (column.children && column.children.length > 0) {
            formChildren = [...formChildren, ...column.children]
          }
        })
      }
      if (row.children && row.children.length > 0) {
        formChildren = [...formChildren, ...row.children]
      }
    })
  }

  return formChildren
}

// 检查单个组件是否需要数据获取
function checkIfNeedsFetchDataForComponent(component) {
  // 只有当明确配置了数据源或API URL时才需要数据获取方法
  if (component.config?.dataSource || component.config?.apiUrl) {
    return true
  }
  return false
}

// 检查是否需要数据获取方法
function checkIfNeedsFetchData(components) {
  if (!components || !Array.isArray(components)) {
    return false
  }

  return components.some(component => {
    // 检查当前组件是否需要数据获取
    if (checkIfNeedsFetchDataForComponent(component)) {
      return true
    }

    // 递归检查子组件
    if (component.children && component.children.length > 0) {
      return checkIfNeedsFetchData(component.children)
    }

    return false
  })
}

// 生成data部分
function generateData(canvas, components) {
  const canvasData = `      // 画布配置
      canvasWidth: ${canvas.width},
      canvasHeight: ${canvas.height},`

  const componentsData = generateComponentsData(components)

  return `${canvasData}
${componentsData}`
}

// 生成组件数据
function generateComponentsData(components, indent = '      ') {
  if (!components || !Array.isArray(components)) {
    return ''
  }

  let dataLines = []

  components.forEach(component => {
    const { id, type, children } = component

    // 生成组件值数据
    if (type === 'input') {
      dataLines.push(`${indent}${id}Value: '',`)
    } else if (type === 'select') {
      dataLines.push(`${indent}${id}Value: '',`)
      dataLines.push(`${indent}${id}Options: [
${indent}  { label: '选项1', value: 'option1' },
${indent}  { label: '选项2', value: 'option2' }
${indent}],`)
    } else if (type === 'date-picker' || type === 'datepicker') {
      dataLines.push(`${indent}${id}Value: null,`)
    } else if (type === 'form-container') {
      dataLines.push(`${indent}${id}Form: {},`)

      // 获取表单子组件：支持多种结构
      const formChildren = getFormChildrenForScript(component)

      // 为表单子组件生成数据
      if (formChildren.length > 0) {
        formChildren.forEach(child => {
          const childType = child.type
          // 获取绑定的变量名
          const bindVarName = getBindVariableNameForScript(child, component)
          const varName = bindVarName || `${child.id}Value`

          if (childType === 'input') {
            // 设置默认值
            const defaultValue = getVariableDefaultValue(child, component) || ''
            dataLines.push(`${indent}${varName}: '${defaultValue}',`)
          } else if (childType === 'select') {
            const defaultValue = getVariableDefaultValue(child, component) || ''
            dataLines.push(`${indent}${varName}: '${defaultValue}',`)
            dataLines.push(`${indent}${child.id}Options: [
${indent}  { label: '选项1', value: 'option1' },
${indent}  { label: '选项2', value: 'option2' }
${indent}],`)
          } else if (childType === 'datepicker') {
            const defaultValue = getVariableDefaultValue(child, component)
            dataLines.push(
              `${indent}${varName}: ${defaultValue ? `'${defaultValue}'` : 'null'},`
            )
          }
        })
      }
    }

    // 递归处理子组件
    if (children && children.length > 0) {
      const childData = generateComponentsData(children, indent)
      if (childData) {
        dataLines.push(childData)
      }
    }
  })

  return dataLines.join('\n')
}

// 生成计算属性
function generateComputed(canvas, components) {
  const canvasStyle = canvas.isResponsive
    ? `    canvasStyle() {
      return {
        width: '100vw',
        height: '100vh',
        position: 'relative',
        backgroundColor: '#ffffff'
      }
    },`
    : `    canvasStyle() {
      return {
        width: this.canvasWidth + 'px',
        height: this.canvasHeight + 'px',
        position: 'relative',
        backgroundColor: '#ffffff'
      }
    },`

  const componentStyles = generateComponentStyles(components)

  return `${canvasStyle}
${componentStyles}`
}

// 生成组件样式计算属性
function generateComponentStyles(components, indent = '    ') {
  if (!components || !Array.isArray(components)) {
    return ''
  }

  let styleLines = []

  components.forEach(component => {
    const { id, position, size, config, children } = component

    styleLines.push(`${indent}${id}Style() {
${indent}  return {
${indent}    position: 'absolute',
${indent}    left: '${position.x}px',
${indent}    top: '${position.y}px',
${indent}    width: '${size.width}px',
${indent}    height: '${size.height}px',${generateConfigStyles(config, indent + '    ')}
${indent}  }
${indent}},`)

    // 递归处理子组件
    if (children && children.length > 0) {
      const childStyles = generateComponentStyles(children, indent)
      if (childStyles) {
        styleLines.push(childStyles)
      }
    }
  })

  return styleLines.join('\n')
}

// 生成配置样式
function generateConfigStyles(config, indent) {
  if (!config) return ''

  let styles = []

  if (config.backgroundColor) {
    styles.push(`${indent}backgroundColor: '${config.backgroundColor}',`)
  }
  if (config.borderColor) {
    styles.push(`${indent}borderColor: '${config.borderColor}',`)
  }
  if (config.borderWidth) {
    styles.push(`${indent}borderWidth: '${config.borderWidth}px',`)
  }
  if (config.borderStyle) {
    styles.push(`${indent}borderStyle: '${config.borderStyle}',`)
  }
  if (config.borderRadius) {
    styles.push(`${indent}borderRadius: '${config.borderRadius}px',`)
  }

  return styles.length > 0 ? '\n' + styles.join('\n') : ''
}

// 生成方法
function generateMethods(components) {
  let methodLines = []

  // 检查是否需要数据获取方法
  const needsFetchData = checkIfNeedsFetchData(components)
  if (needsFetchData) {
    methodLines.push(`    // 数据获取方法
    async fetchData(url) {
      try {
        const response = await axios.get(url)
        return response.data
      } catch (error) {
        Message.error('数据获取失败: ' + error.message)
        return null
      }
    },`)
  }

  // 生成组件事件处理方法
  const componentMethods = generateComponentMethods(components)
  if (componentMethods) {
    methodLines.push(componentMethods)
  }

  return methodLines.join('\n')
}

// 生成组件方法
function generateComponentMethods(components, indent = '    ') {
  if (!components || !Array.isArray(components)) {
    return ''
  }

  let methodLines = []

  components.forEach(component => {
    const { id, type, children, config } = component

    // 生成按钮点击事件
    if (type === 'button') {
      // 获取自定义事件名
      const eventName =
        config?.action?.eventName || `handle${capitalize(id)}Click`

      // 检查是否需要async关键字
      const needsAsync =
        config?.action?.eventCode && containsAwait(config.action.eventCode)
      const asyncKeyword = needsAsync ? 'async ' : ''

      methodLines.push(`${indent}${asyncKeyword}${eventName}() {`)

      if (config?.action?.eventCode) {
        // 添加自定义事件代码
        const eventCode = config.action.eventCode
        const codeLines = eventCode.split('\n')
        codeLines.forEach(line => {
          methodLines.push(`${indent}  ${line}`)
        })
      } else {
        // 默认事件处理
        methodLines.push(`${indent}  console.log('${eventName} 被触发')`)
        methodLines.push(`${indent}  this.$message.success('操作成功')`)
      }

      methodLines.push(`${indent}},`)
    }

    // 处理表单容器中的按钮
    if (type === 'form-container') {
      const formChildren = getFormChildrenForScript(component)
      formChildren.forEach(child => {
        if (child.type === 'button') {
          // 获取自定义事件名
          const eventName =
            child.config?.action?.eventName ||
            `handle${capitalize(child.id)}Click`

          // 检查是否需要async关键字
          const needsAsync =
            child.config?.action?.eventCode &&
            containsAwait(child.config.action.eventCode)
          const asyncKeyword = needsAsync ? 'async ' : ''

          methodLines.push(`${indent}${asyncKeyword}${eventName}() {`)

          if (child.config?.action?.eventCode) {
            // 添加自定义事件代码
            const eventCode = child.config.action.eventCode
            const codeLines = eventCode.split('\n')
            codeLines.forEach(line => {
              methodLines.push(`${indent}  ${line}`)
            })
          } else {
            // 默认事件处理
            methodLines.push(`${indent}  console.log('${eventName} 被触发')`)
            methodLines.push(`${indent}  this.$message.success('操作成功')`)
          }

          methodLines.push(`${indent}},`)
        }
      })
    }

    // 递归处理子组件
    if (children && children.length > 0) {
      const childMethods = generateComponentMethods(children, indent)
      if (childMethods) {
        methodLines.push(childMethods)
      }
    }
  })

  return methodLines.join('\n')
}

// 检查代码中是否包含await关键字
function containsAwait(code) {
  if (!code || typeof code !== 'string') {
    return false
  }

  // 使用正则表达式检查是否包含await关键字
  // 确保await是一个完整的单词，不是其他单词的一部分
  const awaitRegex = /\bawait\b/
  return awaitRegex.test(code)
}

// 生成mounted生命周期
function generateMounted(components) {
  let mountedLines = []

  // 初始化图表
  const chartInit = generateChartInit(components)
  if (chartInit) {
    mountedLines.push(chartInit)
  }

  return mountedLines.join('\n')
}

// 生成图表初始化代码
function generateChartInit(components, indent = '    ') {
  if (!components || !Array.isArray(components)) {
    return ''
  }

  let initLines = []

  components.forEach(component => {
    const { id, type, children } = component

    if (['bar-chart', 'line-chart', 'pie-chart'].includes(type)) {
      initLines.push(`${indent}// 初始化 ${id}
${indent}this.init${capitalize(id)}()`)
    }

    // 递归处理子组件
    if (children && children.length > 0) {
      const childInit = generateChartInit(children, indent)
      if (childInit) {
        initLines.push(childInit)
      }
    }
  })

  // 生成图表初始化方法
  if (initLines.length > 0) {
    const chartMethods = generateChartMethods(components)
    return `${initLines.join('\n')}
    
${chartMethods}`
  }

  return ''
}

// 生成图表方法
function generateChartMethods(components, indent = '    ') {
  if (!components || !Array.isArray(components)) {
    return ''
  }

  let methodLines = []

  components.forEach(component => {
    const { id, type, config, children } = component

    if (['bar-chart', 'line-chart', 'pie-chart'].includes(type)) {
      const title = config?.title || `${type}图表`
      methodLines.push(`${indent}// 初始化${title}
${indent}init${capitalize(id)}() {
${indent}  this.$nextTick(() => {
${indent}    const chartDom = this.$refs.${id}Chart
${indent}    if (chartDom) {
${indent}      const myChart = echarts.init(chartDom)
${indent}      const option = ${generateChartOption(type, config, indent + '        ')}
${indent}      myChart.setOption(option)
${indent}      
${indent}      // 响应式调整
${indent}      window.addEventListener('resize', () => {
${indent}        myChart.resize()
${indent}      })
${indent}    }
${indent}  })
${indent}},`)
    }

    // 递归处理子组件
    if (children && children.length > 0) {
      const childMethods = generateChartMethods(children, indent)
      if (childMethods) {
        methodLines.push(childMethods)
      }
    }
  })

  return methodLines.join('\n')
}

// 生成图表配置
function generateChartOption(type, config, indent) {
  const title = config?.title || '图表'

  if (type === 'bar-chart') {
    return `{
${indent}title: {
${indent}  text: '${title}'
${indent}},
${indent}tooltip: {},
${indent}xAxis: {
${indent}  data: ['一月', '二月', '三月', '四月', '五月', '六月']
${indent}},
${indent}yAxis: {},
${indent}series: [{
${indent}  name: '销量',
${indent}  type: 'bar',
${indent}  data: [5, 20, 36, 10, 10, 20]
${indent}}]
${indent.slice(8)}}`
  } else if (type === 'line-chart') {
    return `{
${indent}title: {
${indent}  text: '${title}'
${indent}},
${indent}tooltip: {},
${indent}xAxis: {
${indent}  data: ['一月', '二月', '三月', '四月', '五月', '六月']
${indent}},
${indent}yAxis: {},
${indent}series: [{
${indent}  name: '数据',
${indent}  type: 'line',
${indent}  data: [5, 20, 36, 10, 10, 20]
${indent}}]
${indent.slice(8)}}`
  } else if (type === 'pie-chart') {
    return `{
${indent}title: {
${indent}  text: '${title}'
${indent}},
${indent}tooltip: {},
${indent}series: [{
${indent}  name: '数据',
${indent}  type: 'pie',
${indent}  data: [
${indent}    { value: 335, name: '直接访问' },
${indent}    { value: 310, name: '邮件营销' },
${indent}    { value: 234, name: '联盟广告' },
${indent}    { value: 135, name: '视频广告' },
${indent}    { value: 1548, name: '搜索引擎' }
${indent}  ]
${indent}}]
${indent.slice(8)}}`
  }

  return '{}'
}

// 获取表单子组件的统一方法（用于Script）
function getFormChildrenForScript(component) {
  const { children, config } = component
  let formChildren = []

  // 方式1：直接在children中
  if (children && children.length > 0) {
    formChildren = [...children]
  }

  // 方式2：在config.children中
  if (config?.children && config.children.length > 0) {
    formChildren = [...formChildren, ...config.children]
  }

  // 方式3：在config.rows的行列结构中
  if (config?.rows && config.rows.length > 0) {
    config.rows.forEach(row => {
      if (row.columns && row.columns.length > 0) {
        row.columns.forEach(column => {
          if (column.children && column.children.length > 0) {
            formChildren = [...formChildren, ...column.children]
          }
        })
      }

      // 有些行可能直接有children
      if (row.children && row.children.length > 0) {
        formChildren = [...formChildren, ...row.children]
      }
    })
  }

  return formChildren
}

// 获取绑定的变量名（用于Script）
function getBindVariableNameForScript(child, formComponent) {
  const bindVariableId = child.config?.bindVariable
  if (!bindVariableId || !formComponent?.config?.variables) {
    return null
  }

  const variable = formComponent.config.variables.find(
    v => v.id === bindVariableId
  )
  return variable ? variable.name : null
}

// 获取变量的默认值
function getVariableDefaultValue(child, formComponent) {
  const bindVariableId = child.config?.bindVariable
  if (!bindVariableId || !formComponent?.config?.variables) {
    return null
  }

  const variable = formComponent.config.variables.find(
    v => v.id === bindVariableId
  )
  return variable ? variable.defaultValue : null
}

// 首字母大写
function capitalize(str) {
  return str.charAt(0).toUpperCase() + str.slice(1)
}
