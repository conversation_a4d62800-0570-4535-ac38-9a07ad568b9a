/**
 * Vue2模板生成器
 */

// 生成Vue2的模板部分
export function generateTemplate(schema) {
  const { canvas, components } = schema

  return `<template>
  <div class="bi-dashboard" :style="canvasStyle">
${generateComponentsTemplate(components, '    ')}
  </div>
</template>`
}

// 生成组件模板
function generateComponentsTemplate(components, indent = '') {
  if (!components || !Array.isArray(components)) {
    return ''
  }

  return components
    .map(component => {
      const { id, type, position, size, config, children } = component
      const componentTemplate = generateSingleComponentTemplate(
        component,
        indent
      )

      if (children && children.length > 0) {
        const childrenTemplate = generateComponentsTemplate(
          children,
          indent + '  '
        )
        return componentTemplate.replace(
          '</div>',
          `${childrenTemplate}\n${indent}</div>`
        )
      }

      return componentTemplate
    })
    .join('\n')
}

// 生成单个组件的模板
function generateSingleComponentTemplate(component, indent = '') {
  const { id, type, position, size, config } = component

  switch (type) {
    case 'div-container':
      return generateDivContainerTemplate(component, indent)
    case 'bar-chart':
    case 'line-chart':
    case 'pie-chart':
      return generateChartTemplate(component, indent)
    case 'button':
      return generateButtonTemplate(component, indent)
    case 'input':
      return generateInputTemplate(component, indent)
    case 'select':
      return generateSelectTemplate(component, indent)
    case 'date-picker':
    case 'datepicker':
      return generateDatePickerTemplate(component, indent)
    case 'form-container':
      return generateFormContainerTemplate(component, indent)
    default:
      return generateDivContainerTemplate(component, indent)
  }
}

// 容器组件模板
function generateDivContainerTemplate(component, indent) {
  const { id, config } = component
  const styleBinding = `${id}Style`

  return `${indent}<div 
${indent}  :id="${id}" 
${indent}  class="component-container" 
${indent}  :style="${styleBinding}"
${indent}>`
}

// 图表组件模板
function generateChartTemplate(component, indent) {
  const { id, type } = component
  const chartRef = `${id}Chart`

  return `${indent}<div 
${indent}  :id="${id}" 
${indent}  ref="${chartRef}"
${indent}  class="chart-container" 
${indent}  :style="${id}Style"
${indent}></div>`
}

// 按钮组件模板
function generateButtonTemplate(component, indent) {
  const { id, config } = component
  const text = config?.text || '按钮'

  return `${indent}<el-button 
${indent}  :id="${id}"
${indent}  :style="${id}Style"
${indent}  @click="handle${capitalize(id)}Click"
${indent}>${text}</el-button>`
}

// 输入框组件模板
function generateInputTemplate(component, indent) {
  const { id, config } = component
  const placeholder = config?.placeholder || '请输入'

  return `${indent}<el-input 
${indent}  :id="${id}"
${indent}  v-model="${id}Value"
${indent}  :style="${id}Style"
${indent}  placeholder="${placeholder}"
${indent}></el-input>`
}

// 选择器组件模板
function generateSelectTemplate(component, indent) {
  const { id, config } = component
  const placeholder = config?.placeholder || '请选择'

  return `${indent}<el-select 
${indent}  :id="${id}"
${indent}  v-model="${id}Value"
${indent}  :style="${id}Style"
${indent}  placeholder="${placeholder}"
${indent}>
${indent}  <el-option 
${indent}    v-for="item in ${id}Options" 
${indent}    :key="item.value" 
${indent}    :label="item.label" 
${indent}    :value="item.value">
${indent}  </el-option>
${indent}</el-select>`
}

// 日期选择器组件模板
function generateDatePickerTemplate(component, indent) {
  const { id, config } = component
  const placeholder = config?.placeholder || '选择日期'

  return `${indent}<el-date-picker 
${indent}  :id="${id}"
${indent}  v-model="${id}Value"
${indent}  :style="${id}Style"
${indent}  type="date"
${indent}  placeholder="${placeholder}">
${indent}</el-date-picker>`
}

// 表单容器组件模板
function generateFormContainerTemplate(component, indent) {
  const { id, config, children } = component
  const styleBinding = `${id}Style`

  let template = `${indent}<div 
${indent}  :id="${id}" 
${indent}  class="form-container" 
${indent}  :style="${styleBinding}"
${indent}>`

  // 添加标题
  if (config?.showTitle && config?.titleText) {
    template += `\n${indent}  <div class="form-title">${config.titleText}</div>`
  }

  // 获取所有表单组件：支持多种数据结构
  const formChildren = getFormChildren(component)

  // 如果有子组件，生成表单内容
  if (formChildren.length > 0) {
    template += `\n${indent}  <el-form :model="${id}Form" class="form-content">`

    // 如果有rows结构，按行列布局生成
    if (config?.rows && config.rows.length > 0) {
      config.rows.forEach(row => {
        template += `\n${indent}    <el-row :gutter="16" class="form-row">`

        if (row.columns && row.columns.length > 0) {
          row.columns.forEach(column => {
            const colSpan = Math.round((column.width / 100) * 24) || 8
            template += `\n${indent}      <el-col :span="${colSpan}">`

            if (column.children && column.children.length > 0) {
              column.children.forEach(child => {
                const childTemplate = generateFormChildTemplate(
                  child,
                  indent + '        ',
                  component
                )
                template += `\n${childTemplate}`
              })
            }

            template += `\n${indent}      </el-col>`
          })
        }

        template += `\n${indent}    </el-row>`
      })
    } else {
      // 简单布局
      formChildren.forEach(child => {
        const childTemplate = generateFormChildTemplate(
          child,
          indent + '    ',
          component
        )
        template += `\n${childTemplate}`
      })
    }

    template += `\n${indent}  </el-form>`
  } else {
    // 如果没有子组件，添加占位内容
    template += `\n${indent}  <div class="form-placeholder">暂无表单项</div>`
  }

  template += `\n${indent}</div>`
  return template
}

// 表单子组件模板
function generateFormChildTemplate(child, indent, formComponent) {
  const { id, type, config } = child

  // 获取绑定的变量名
  const bindVarName = getBindVariableName(child, formComponent)
  const modelValue = bindVarName || `${id}Value`

  switch (type) {
    case 'input':
      return `${indent}<el-form-item label="${config?.label || ''}">
${indent}  <el-input 
${indent}    v-model="${modelValue}"
${indent}    placeholder="${config?.placeholder || '请输入'}"
${indent}  ></el-input>
${indent}</el-form-item>`
    case 'select':
      return `${indent}<el-form-item label="${config?.label || ''}">
${indent}  <el-select 
${indent}    v-model="${modelValue}"
${indent}    placeholder="${config?.placeholder || '请选择'}"
${indent}  >
${indent}    <el-option 
${indent}      v-for="item in ${id}Options" 
${indent}      :key="item.value" 
${indent}      :label="item.label" 
${indent}      :value="item.value">
${indent}    </el-option>
${indent}  </el-select>
${indent}</el-form-item>`
    case 'datepicker':
      return `${indent}<el-form-item label="${config?.label || ''}">
${indent}  <el-date-picker 
${indent}    v-model="${modelValue}"
${indent}    type="date"
${indent}    placeholder="${config?.placeholder || '选择日期'}"
${indent}  ></el-date-picker>
${indent}</el-form-item>`
    case 'button':
      // 获取自定义事件名
      const eventName =
        config?.action?.eventName || `handle${capitalize(id)}Click`
      return `${indent}<el-form-item>
${indent}  <el-button 
${indent}    type="${config?.type || 'primary'}"
${indent}    @click="${eventName}"
${indent}  >${config?.text || '按钮'}</el-button>
${indent}</el-form-item>`
    default:
      return `${indent}<div><!-- 未知组件类型: ${type} --></div>`
  }
}

// 获取表单子组件的统一方法
function getFormChildren(component) {
  const { children, config } = component
  let formChildren = []

  // 方式1：直接在children中
  if (children && children.length > 0) {
    formChildren = [...children]
  }

  // 方式2：在config.children中
  if (config?.children && config.children.length > 0) {
    formChildren = [...formChildren, ...config.children]
  }

  // 方式3：在config.rows的行列结构中
  if (config?.rows && config.rows.length > 0) {
    config.rows.forEach(row => {
      if (row.columns && row.columns.length > 0) {
        row.columns.forEach(column => {
          if (column.children && column.children.length > 0) {
            formChildren = [...formChildren, ...column.children]
          }
        })
      }

      // 有些行可能直接有children
      if (row.children && row.children.length > 0) {
        formChildren = [...formChildren, ...row.children]
      }
    })
  }

  return formChildren
}

// 获取绑定的变量名
function getBindVariableName(child, formComponent) {
  const bindVariableId = child.config?.bindVariable
  if (!bindVariableId || !formComponent?.config?.variables) {
    return null
  }

  const variable = formComponent.config.variables.find(
    v => v.id === bindVariableId
  )
  return variable ? variable.name : null
}

// 首字母大写
function capitalize(str) {
  return str.charAt(0).toUpperCase() + str.slice(1)
}
