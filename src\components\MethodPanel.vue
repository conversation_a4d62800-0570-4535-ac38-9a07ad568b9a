<template>
  <div class="method-panel">
    <div class="panel-header">
      <h3>方法管理</h3>
      <ElButton type="primary" size="small" @click="openMethodsEditor">
        <ElIcon><EditPen /></ElIcon>
        方法管理
      </ElButton>
    </div>

    <div class="panel-content">
      <div v-if="methodList.length === 0" class="empty-state">
        <p>暂无方法</p>
        <p class="empty-tip">点击"方法管理"按钮开始编写方法</p>
      </div>

      <div v-else class="method-list">
        <div
          v-for="method in methodList"
          :key="method.name"
          class="method-item"
        >
          <div class="method-badge">
            <span class="method-tag">函数</span>
          </div>
          <div class="method-name">
            {{ method.name }}({{ method.params || '' }})
          </div>
          <div class="method-description">
            {{ method.description || '无描述' }}
          </div>
        </div>
      </div>
    </div>

    <!-- 方法代码编辑对话框 -->
    <ElDialog
      v-model="editorDialogVisible"
      title="方法代码编辑"
      width="90%"
      :before-close="handleEditorClose"
      class="methods-editor-dialog"
    >
      <div class="editor-toolbar">
        <div class="toolbar-left">
          <span class="editor-info"
            >编写JavaScript方法，支持语法高亮和智能提示</span
          >
        </div>
        <div class="toolbar-right">
          <ElButton size="small" @click="formatCode"> 格式化代码 </ElButton>
          <ElButton size="small" @click="insertTemplate"> 插入模板 </ElButton>
        </div>
      </div>

      <div class="editor-container">
        <MonacoEditor
          ref="monacoEditor"
          v-model="methodsCode"
          language="javascript"
          theme="vs-dark"
          height="500px"
          :options="editorOptions"
          @change="onCodeChange"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <div class="footer-left">
            <span class="method-count"
              >已识别 {{ methodList.length }} 个方法</span
            >
          </div>
          <div class="footer-right">
            <ElButton @click="cancelEditor">取消</ElButton>
            <ElButton type="primary" @click="saveMethodsCode">保存</ElButton>
          </div>
        </div>
      </template>
    </ElDialog>

    <!-- 模板选择对话框 -->
    <ElDialog
      v-model="templateDialogVisible"
      title="选择方法模板"
      width="600px"
    >
      <div class="template-list">
        <div
          v-for="template in methodTemplates"
          :key="template.name"
          class="template-item"
          @click="insertMethodTemplate(template.code)"
        >
          <div class="template-name">{{ template.name }}</div>
          <div class="template-description">{{ template.description }}</div>
        </div>
      </div>
      <template #footer>
        <ElButton @click="templateDialogVisible = false">关闭</ElButton>
      </template>
    </ElDialog>
  </div>
</template>

<script setup>
  import { ref, computed, watch } from 'vue'
  import {
    ElButton,
    ElIcon,
    ElDialog,
    ElMessage,
    ElMessageBox
  } from 'element-plus'
  import { EditPen } from '@element-plus/icons-vue'
  import MonacoEditor from './MonacoEditor.vue'

  const props = defineProps({
    methods: {
      type: String,
      default: ''
    }
  })

  const emit = defineEmits(['update-methods'])

  const methodsCode = ref(props.methods)
  const editorDialogVisible = ref(false)
  const templateDialogVisible = ref(false)
  const monacoEditor = ref(null)
  const originalCode = ref('')

  const editorOptions = {
    fontSize: 14,
    tabSize: 2,
    insertSpaces: true,
    wordWrap: 'on',
    lineNumbers: 'on',
    minimap: { enabled: true },
    scrollBeyondLastLine: false,
    folding: true,
    contextmenu: true,
    suggest: {
      insertMode: 'replace',
      filterGraceful: true,
      showKeywords: true,
      showSnippets: true,
      showFunctions: true,
      showVariables: true
    }
  }

  // 方法模板
  const methodTemplates = ref([
    {
      name: '基础方法',
      description: '创建一个基础的JavaScript方法',
      code: `function methodName() {
  // 方法内容
  console.log('方法执行')
}`
    },
    {
      name: '异步方法',
      description: '创建一个异步方法，支持async/await',
      code: `async function asyncMethodName() {
  try {
    // 异步操作
    const result = await someAsyncOperation()
    return result
  } catch (error) {
    console.error('操作失败:', error)
    throw error
  }
}`
    },
    {
      name: 'API请求方法',
      description: '创建一个API请求方法',
      code: `async function fetchData(url, options = {}) {
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    })
    
    if (!response.ok) {
      throw new Error(\`HTTP error! status: \${response.status}\`)
    }
    
    const data = await response.json()
    return data
  } catch (error) {
    console.error('请求失败:', error)
    throw error
  }
}`
    },
    {
      name: '表单验证方法',
      description: '创建一个表单验证方法',
      code: `function validateForm(formData) {
  const errors = {}
  
  // 验证必填字段
  if (!formData.name || formData.name.trim() === '') {
    errors.name = '姓名不能为空'
  }
  
  if (!formData.email || formData.email.trim() === '') {
    errors.email = '邮箱不能为空'
  } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {
    errors.email = '邮箱格式不正确'
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}`
    },
    {
      name: '事件处理方法',
      description: '创建一个事件处理方法',
      code: `function handleButtonClick(event) {
  // 阻止默认行为
  event.preventDefault()
  
  // 获取数据
  const data = {
    timestamp: Date.now(),
    target: event.target
  }
  
  // 处理逻辑
  console.log('按钮被点击:', data)
  
  // 显示消息
  ElMessage.success('操作成功')
}`
    }
  ])

  // 解析方法代码，提取方法列表
  const methodList = computed(() => {
    const methods = []
    if (!methodsCode.value) return methods

    // 匹配函数声明和箭头函数
    const functionRegex =
      /(?:function\s+(\w+)\s*\(([^)]*)\)|const\s+(\w+)\s*=\s*(?:async\s+)?\(([^)]*)\)\s*=>|(\w+)\s*:\s*(?:async\s+)?function\s*\(([^)]*)\))/g
    const commentRegex = /\/\*\*?\s*(.*?)\s*\*?\*\//

    let match

    while ((match = functionRegex.exec(methodsCode.value)) !== null) {
      const name = match[1] || match[3] || match[5]
      const params = match[2] || match[4] || match[6] || ''

      // 查找方法前的注释
      const methodStartIndex = methodsCode.value.indexOf(match[0])
      const linesBeforeMethod = methodsCode.value
        .substring(0, methodStartIndex)
        .split('\n')
      let description = ''

      // 查找最近的注释
      for (let i = linesBeforeMethod.length - 1; i >= 0; i--) {
        const line = linesBeforeMethod[i].trim()
        if (line.startsWith('//')) {
          description = line.replace(/^\/\/\s*/, '')
          break
        } else if (line.includes('/**') || line.includes('/*')) {
          const commentMatch = commentRegex.exec(line)
          if (commentMatch) {
            description = commentMatch[1]
          }
          break
        } else if (line !== '') {
          break
        }
      }

      methods.push({
        name,
        params,
        description: description || '无描述'
      })
    }

    return methods
  })

  // 监听props变化
  watch(
    () => props.methods,
    newValue => {
      methodsCode.value = newValue
    }
  )

  const openMethodsEditor = () => {
    originalCode.value = methodsCode.value
    editorDialogVisible.value = true
  }

  const handleEditorClose = () => {
    if (methodsCode.value !== originalCode.value) {
      ElMessageBox.confirm('代码已修改但未保存，确定要关闭吗？', '确认关闭', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          methodsCode.value = originalCode.value
          editorDialogVisible.value = false
        })
        .catch(() => {
          // 用户取消，不关闭对话框
        })
    } else {
      editorDialogVisible.value = false
    }
  }

  const cancelEditor = () => {
    methodsCode.value = originalCode.value
    editorDialogVisible.value = false
  }

  const saveMethodsCode = () => {
    emit('update-methods', methodsCode.value)
    editorDialogVisible.value = false
    ElMessage.success('方法代码已保存')
  }

  const formatCode = () => {
    if (monacoEditor.value) {
      monacoEditor.value.formatDocument()
    }
  }

  const insertTemplate = () => {
    templateDialogVisible.value = true
  }

  const insertMethodTemplate = templateCode => {
    if (monacoEditor.value) {
      const editor = monacoEditor.value.getEditor()
      if (editor) {
        const position = editor.getPosition()
        const range = {
          startLineNumber: position.lineNumber,
          startColumn: position.column,
          endLineNumber: position.lineNumber,
          endColumn: position.column
        }

        const insertText = (methodsCode.value ? '\n\n' : '') + templateCode
        editor.executeEdits('insert-template', [
          {
            range: range,
            text: insertText
          }
        ])

        // 更新代码
        methodsCode.value = editor.getValue()
      }
    }
    templateDialogVisible.value = false
  }

  const onCodeChange = value => {
    methodsCode.value = value
  }

  // 获取方法列表供其他组件使用
  const getMethodOptions = () => {
    return methodList.value.map(method => ({
      label: `${method.name}(${method.params}) - ${method.description}`,
      value: method.name
    }))
  }

  // 暴露方法供父组件使用
  defineExpose({
    getMethodOptions
  })
</script>

<style scoped>
  .method-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .panel-header {
    padding: 16px;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
  }

  .panel-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }

  .panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 16px;
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #909399;
  }

  .empty-state p {
    margin: 8px 0;
  }

  .empty-tip {
    font-size: 12px;
    color: #c0c4cc;
  }

  .method-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 8px 0;
  }

  .method-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: #fff;
    transition: all 0.2s ease;
    width: 100%;
    box-sizing: border-box;
  }

  .method-item:hover {
    border-color: #409eff;
    background: #f0f9ff;
  }

  .method-badge {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }

  .method-name {
    font-weight: 500;
    color: #303133;
    margin-bottom: 8px;
    font-family: 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.4;
    word-break: break-word;
  }

  .method-description {
    font-size: 12px;
    color: #909399;
    margin-bottom: 0;
    line-height: 1.3;
    word-break: break-word;
  }

  .method-tag {
    background: #409eff;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 500;
  }

  .editor-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e4e7ed;
    margin-bottom: 16px;
  }

  .toolbar-left .editor-info {
    color: #606266;
    font-size: 14px;
  }

  .toolbar-right {
    display: flex;
    gap: 8px;
  }

  .editor-container {
    margin-bottom: 16px;
  }

  .dialog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .footer-left .method-count {
    color: #606266;
    font-size: 14px;
  }

  .footer-right {
    display: flex;
    gap: 8px;
  }

  .template-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 400px;
    overflow-y: auto;
  }

  .template-item {
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .template-item:hover {
    border-color: #409eff;
    background: #f0f9ff;
  }

  .template-name {
    font-weight: 500;
    color: #303133;
    margin-bottom: 4px;
  }

  .template-description {
    font-size: 12px;
    color: #909399;
  }

  :deep(.methods-editor-dialog) {
    .el-dialog__body {
      padding: 20px;
    }
  }
</style>
