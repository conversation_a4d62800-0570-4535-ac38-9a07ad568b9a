/**
 * Vue3 Script生成器
 */

// 生成Vue3的script部分
export function generateScript(schema) {
  const {
    canvas,
    components,
    variables,
    methods: schemaMethods,
    mounted
  } = schema
  const imports = generateImports(schema)
  const reactive = generateReactiveData(canvas, components, variables)
  const computed = generateComputed(canvas, components)
  const methods = generateMethods(components, schemaMethods)
  const onMounted = generateOnMounted(components, mounted)

  return `<script setup>
${imports}

// 响应式数据
${reactive}

// 计算属性
${computed}

// 方法
${methods}

// 生命周期
${onMounted}
</script>`
}

// 生成导入语句
function generateImports(schema) {
  const dependencies = analyzeDependencies(schema)

  let imports = []

  // Vue 组合式API
  const vueApis = []
  if (dependencies.vue.ref) vueApis.push('ref')
  if (dependencies.vue.reactive) vueApis.push('reactive')
  if (dependencies.vue.computed) vueApis.push('computed')
  if (dependencies.vue.onMounted) vueApis.push('onMounted')
  if (dependencies.vue.nextTick) vueApis.push('nextTick')

  if (vueApis.length > 0) {
    imports.push(`import { ${vueApis.join(', ')} } from 'vue'`)
  }

  // 第三方库
  if (dependencies.libraries.axios) {
    imports.push(`import axios from 'axios'`)
  }
  if (dependencies.libraries.dayjs) {
    imports.push(`import dayjs from 'dayjs'`)
  }
  if (dependencies.libraries.echarts) {
    imports.push(`import * as echarts from 'echarts'`)
  }

  // Element Plus 组件
  const elComponents = []
  if (dependencies.elementPlus.ElButton) elComponents.push('ElButton')
  if (dependencies.elementPlus.ElInput) elComponents.push('ElInput')
  if (dependencies.elementPlus.ElSelect) elComponents.push('ElSelect')
  if (dependencies.elementPlus.ElOption) elComponents.push('ElOption')
  if (dependencies.elementPlus.ElDatePicker) elComponents.push('ElDatePicker')
  if (dependencies.elementPlus.ElMessage) elComponents.push('ElMessage')
  if (dependencies.elementPlus.ElForm) elComponents.push('ElForm')
  if (dependencies.elementPlus.ElFormItem) elComponents.push('ElFormItem')
  if (dependencies.elementPlus.ElRow) elComponents.push('ElRow')
  if (dependencies.elementPlus.ElCol) elComponents.push('ElCol')

  if (elComponents.length > 0) {
    imports.push(`import { ${elComponents.join(', ')} } from 'element-plus'`)
  }

  return imports.join('\n')
}

// 分析依赖
function analyzeDependencies(schema) {
  const dependencies = {
    vue: {
      ref: false,
      reactive: false,
      computed: true, // 总是需要computed用于样式
      onMounted: false,
      nextTick: false
    },
    libraries: {
      axios: false,
      dayjs: false,
      echarts: false
    },
    elementPlus: {
      ElButton: false,
      ElInput: false,
      ElSelect: false,
      ElOption: false,
      ElDatePicker: false,
      ElMessage: false,
      ElForm: false,
      ElFormItem: false,
      ElRow: false,
      ElCol: false
    }
  }

  // 分析组件
  analyzeComponents(schema.components, dependencies)

  // 如果需要axios，则需要ElMessage来显示错误
  if (dependencies.libraries.axios) {
    dependencies.elementPlus.ElMessage = true
  }

  // 如果有图表，需要onMounted和nextTick
  if (dependencies.libraries.echarts) {
    dependencies.vue.onMounted = true
    dependencies.vue.nextTick = true
  }

  return dependencies
}

// 分析组件依赖
function analyzeComponents(components, dependencies) {
  if (!components || !Array.isArray(components)) {
    return
  }

  components.forEach(component => {
    const { type, children } = component

    switch (type) {
      case 'button':
        dependencies.elementPlus.ElButton = true
        dependencies.elementPlus.ElMessage = true // 按钮点击可能显示消息
        break
      case 'input':
        dependencies.vue.ref = true
        dependencies.elementPlus.ElInput = true
        break
      case 'select':
        dependencies.vue.ref = true
        dependencies.elementPlus.ElSelect = true
        dependencies.elementPlus.ElOption = true
        break
      case 'date-picker':
      case 'datepicker':
        dependencies.vue.ref = true
        dependencies.elementPlus.ElDatePicker = true
        break
      case 'form-container': {
        dependencies.vue.reactive = true
        dependencies.elementPlus.ElForm = true
        dependencies.elementPlus.ElFormItem = true
        dependencies.elementPlus.ElRow = true
        dependencies.elementPlus.ElCol = true

        // 分析表单子组件
        const formChildren = getFormChildrenForAnalysis(component)
        formChildren.forEach(child => {
          switch (child.type) {
            case 'input':
              dependencies.vue.ref = true
              dependencies.elementPlus.ElInput = true
              break
            case 'select':
              dependencies.vue.ref = true
              dependencies.elementPlus.ElSelect = true
              dependencies.elementPlus.ElOption = true
              break
            case 'datepicker':
              dependencies.vue.ref = true
              dependencies.elementPlus.ElDatePicker = true
              break
            case 'button':
              dependencies.elementPlus.ElButton = true
              dependencies.elementPlus.ElMessage = true
              break
          }
        })
        break
      }
      case 'bar-chart':
      case 'line-chart':
      case 'pie-chart':
        dependencies.libraries.echarts = true
        break
    }

    // 检查是否需要数据获取
    if (checkIfNeedsFetchDataForComponent(component)) {
      dependencies.libraries.axios = true
    }

    // 递归处理子组件
    if (children && children.length > 0) {
      analyzeComponents(children, dependencies)
    }
  })
}

// 获取表单子组件用于依赖分析
function getFormChildrenForAnalysis(component) {
  const { children, config } = component
  let formChildren = []

  // 方式1：直接在children中
  if (children && children.length > 0) {
    formChildren = [...children]
  }

  // 方式2：在config.children中
  if (config?.children && config.children.length > 0) {
    formChildren = [...formChildren, ...config.children]
  }

  // 方式3：在config.rows的行列结构中
  if (config?.rows && config.rows.length > 0) {
    config.rows.forEach(row => {
      if (row.columns && row.columns.length > 0) {
        row.columns.forEach(column => {
          if (column.children && column.children.length > 0) {
            formChildren = [...formChildren, ...column.children]
          }
        })
      }
      if (row.children && row.children.length > 0) {
        formChildren = [...formChildren, ...row.children]
      }
    })
  }

  return formChildren
}

// 检查单个组件是否需要数据获取
function checkIfNeedsFetchDataForComponent(component) {
  // 只有当明确配置了数据源或API URL时才需要数据获取方法
  if (component.config?.dataSource || component.config?.apiUrl) {
    return true
  }
  return false
}

// 生成响应式数据
function generateReactiveData(canvas, components, variables) {
  const canvasData = `const canvasConfig = reactive({
  width: ${canvas.width},
  height: ${canvas.height}
})`

  // 生成全局变量
  const globalVariables = generateGlobalVariables(variables)

  const componentsData = generateComponentsReactiveData(components)

  return `${canvasData}

${globalVariables}

${componentsData}`
}

// 生成全局变量
function generateGlobalVariables(variables) {
  if (!variables || Object.keys(variables).length === 0) {
    return ''
  }

  let variableLines = []

  Object.entries(variables).forEach(([key, value]) => {
    const variableType = typeof value

    if (variableType === 'object' && value !== null) {
      // 对象类型使用reactive
      if (Array.isArray(value)) {
        variableLines.push(`const ${key} = ref(${JSON.stringify(value)})`)
      } else {
        variableLines.push(
          `const ${key} = reactive(${JSON.stringify(value, null, 2)})`
        )
      }
    } else {
      // 基础类型使用ref
      const jsonValue = JSON.stringify(value)
      variableLines.push(`const ${key} = ref(${jsonValue})`)
    }
  })

  return variableLines.join('\n')
}

// 生成组件响应式数据
function generateComponentsReactiveData(components, indent = '') {
  if (!components || !Array.isArray(components)) {
    return ''
  }

  let dataLines = []

  components.forEach(component => {
    const { id, type, children } = component

    // 生成组件值数据
    if (type === 'input') {
      dataLines.push(`const ${id}Value = ref('')`)
    } else if (type === 'select') {
      dataLines.push(`const ${id}Value = ref('')`)
      dataLines.push(`const ${id}Options = ref([
  { label: '选项1', value: 'option1' },
  { label: '选项2', value: 'option2' }
])`)
    } else if (type === 'date-picker' || type === 'datepicker') {
      dataLines.push(`const ${id}Value = ref(null)`)
    } else if (type === 'form-container') {
      dataLines.push(`const ${id}Form = reactive({})`)

      // 获取表单子组件：支持多种结构
      const formChildren = getFormChildrenForScript(component)

      // 为表单子组件生成数据
      if (formChildren.length > 0) {
        formChildren.forEach(child => {
          const childType = child.type
          // 获取绑定的变量名
          const bindVarName = getBindVariableNameForScript(child, component)
          const varName = bindVarName || `${child.id}Value`

          if (childType === 'input') {
            // 设置默认值
            const defaultValue = getVariableDefaultValue(child, component) || ''
            dataLines.push(`const ${varName} = ref('${defaultValue}')`)
          } else if (childType === 'select') {
            const defaultValue = getVariableDefaultValue(child, component) || ''
            dataLines.push(`const ${varName} = ref('${defaultValue}')`)
            dataLines.push(`const ${child.id}Options = ref([
  { label: '选项1', value: 'option1' },
  { label: '选项2', value: 'option2' }
])`)
          } else if (childType === 'datepicker') {
            const defaultValue = getVariableDefaultValue(child, component)
            dataLines.push(
              `const ${varName} = ref(${defaultValue ? `'${defaultValue}'` : 'null'})`
            )
          }
        })
      }
    }

    // 递归处理子组件
    if (children && children.length > 0) {
      const childData = generateComponentsReactiveData(children, indent)
      if (childData) {
        dataLines.push(childData)
      }
    }
  })

  return dataLines.join('\n')
}

// 生成计算属性
function generateComputed(canvas, components) {
  const canvasStyle = canvas.isResponsive
    ? `const canvasStyle = computed(() => ({
  width: '100vw',
  height: '100vh',
  position: 'relative',
  backgroundColor: '#ffffff'
}))`
    : `const canvasStyle = computed(() => ({
  width: canvasConfig.width + 'px',
  height: canvasConfig.height + 'px',
  position: 'relative',
  backgroundColor: '#ffffff'
}))`

  const componentStyles = generateComponentStyles(components)

  return `${canvasStyle}

${componentStyles}`
}

// 生成组件样式计算属性
function generateComponentStyles(components, indent = '') {
  if (!components || !Array.isArray(components)) {
    return ''
  }

  let styleLines = []

  components.forEach(component => {
    const { id, position, size, config, children, type } = component

    const configStyles = generateConfigStyles(config, '  ')
    const comma = configStyles ? ',' : ''

    // 表单容器使用相对布局
    if (type === 'form-container') {
      styleLines.push(`const ${id}Style = computed(() => ({
  position: 'absolute',
  left: '${position.x}px',
  top: '${position.y}px',
  width: '${size.width}px',
  height: '${size.height}px'${comma}${configStyles}
}))`)
    } else {
      // 其他组件使用绝对定位
      styleLines.push(`const ${id}Style = computed(() => ({
  position: 'absolute',
  left: '${position.x}px',
  top: '${position.y}px',
  width: '${size.width}px',
  height: '${size.height}px'${comma}${configStyles}
}))`)
    }

    // 递归处理子组件 - 但跳过表单容器的子组件，因为它们不需要位置样式
    if (children && children.length > 0 && type !== 'form-container') {
      const childStyles = generateComponentStyles(children, indent)
      if (childStyles) {
        styleLines.push(childStyles)
      }
    }
  })

  return styleLines.join('\n\n')
}

// 生成配置样式
function generateConfigStyles(config, indent) {
  if (!config) return ''

  let styles = []

  if (config.backgroundColor) {
    styles.push(`${indent}backgroundColor: '${config.backgroundColor}',`)
  }
  if (config.borderColor) {
    styles.push(`${indent}borderColor: '${config.borderColor}',`)
  }
  if (config.borderWidth) {
    styles.push(`${indent}borderWidth: '${config.borderWidth}px',`)
  }
  if (config.borderStyle) {
    styles.push(`${indent}borderStyle: '${config.borderStyle}',`)
  }
  if (config.borderRadius) {
    styles.push(`${indent}borderRadius: '${config.borderRadius}px',`)
  }

  return styles.length > 0 ? '\n' + styles.join('\n') : ''
}

// 生成方法
function generateMethods(components, schemaMethods) {
  let methodLines = []

  // 添加schema中定义的方法
  if (schemaMethods && schemaMethods.trim()) {
    methodLines.push(`// 用户定义的方法
${schemaMethods}`)
  }

  // 检查是否需要数据获取方法
  const needsFetchData = checkIfNeedsFetchData(components)

  // 仅在需要时添加数据获取方法
  if (needsFetchData) {
    methodLines.push(`// 数据获取方法
const fetchData = async (url) => {
  try {
    const response = await axios.get(url)
    return response.data
  } catch (error) {
    ElMessage.error('数据获取失败: ' + error.message)
    return null
  }
}`)
  }

  // 生成组件事件处理方法
  const componentMethods = generateComponentMethods(components)
  if (componentMethods) {
    methodLines.push(componentMethods)
  }

  return methodLines.join('\n\n')
}

// 检查是否需要fetchData方法
function checkIfNeedsFetchData(components) {
  if (!components || !Array.isArray(components)) {
    return false
  }

  return components.some(component => {
    // 检查当前组件是否需要数据获取
    if (checkIfNeedsFetchDataForComponent(component)) {
      return true
    }

    // 递归检查子组件
    if (component.children && component.children.length > 0) {
      return checkIfNeedsFetchData(component.children)
    }

    return false
  })
}

// 生成组件方法
function generateComponentMethods(components, indent = '') {
  if (!components || !Array.isArray(components)) {
    return ''
  }

  let methodLines = []

  components.forEach(component => {
    const { id, type, children, config } = component

    // 生成按钮点击事件
    if (type === 'button') {
      // 获取自定义事件名
      const eventName =
        config?.action?.eventName || `handle${capitalize(id)}Click`

      // 检查是否需要async关键字
      const needsAsync =
        config?.action?.eventCode && containsAwait(config.action.eventCode)
      const asyncKeyword = needsAsync ? 'async ' : ''

      methodLines.push(`const ${eventName} = ${asyncKeyword}() => {`)

      if (config?.action?.eventCode) {
        // 添加自定义事件代码
        const eventCode = config.action.eventCode
        const codeLines = eventCode.split('\n')
        codeLines.forEach(line => {
          methodLines.push(`  ${line}`)
        })
      } else {
        // 默认事件处理
        methodLines.push(`  console.log('${eventName} 被触发')`)
        methodLines.push(`  ElMessage.success('操作成功')`)
      }

      methodLines.push(`}`)
    }

    // 处理表单容器中的按钮
    if (type === 'form-container') {
      const formChildren = getFormChildrenForScript(component)
      formChildren.forEach(child => {
        if (child.type === 'button') {
          // 获取自定义事件名
          const eventName =
            child.config?.action?.eventName ||
            `handle${capitalize(child.id)}Click`

          // 检查是否需要async关键字
          const needsAsync =
            child.config?.action?.eventCode &&
            containsAwait(child.config.action.eventCode)
          const asyncKeyword = needsAsync ? 'async ' : ''

          methodLines.push(`const ${eventName} = ${asyncKeyword}() => {`)

          if (child.config?.action?.eventCode) {
            // 添加自定义事件代码
            const eventCode = child.config.action.eventCode
            const codeLines = eventCode.split('\n')
            codeLines.forEach(line => {
              methodLines.push(`  ${line}`)
            })
          } else {
            // 默认事件处理
            methodLines.push(`  console.log('${eventName} 被触发')`)
            methodLines.push(`  ElMessage.success('操作成功')`)
          }

          methodLines.push(`}`)
        }
      })
    }

    // 递归处理子组件
    if (children && children.length > 0) {
      const childMethods = generateComponentMethods(children, indent)
      if (childMethods) {
        methodLines.push(childMethods)
      }
    }
  })

  return methodLines.join('\n\n')
}

// 检查代码中是否包含await关键字
function containsAwait(code) {
  if (!code || typeof code !== 'string') {
    return false
  }

  // 使用正则表达式检查是否包含await关键字
  // 确保await是一个完整的单词，不是其他单词的一部分
  const awaitRegex = /\bawait\b/
  return awaitRegex.test(code)
}

// 生成onMounted生命周期
function generateOnMounted(components, schemaMounted) {
  let mountedLines = []
  let onMountedContent = []

  // 添加用户定义的mounted代码
  if (schemaMounted && schemaMounted.trim()) {
    onMountedContent.push(`  // 用户定义的初始化代码
  ${schemaMounted.split('\n').join('\n  ')}`)
  }

  // 初始化图表
  const chartInit = generateChartInit(components)
  if (chartInit) {
    if (onMountedContent.length > 0) {
      onMountedContent.push('')
    }
    onMountedContent.push(chartInit)
  }

  // 如果有内容需要放在onMounted中
  if (onMountedContent.length > 0) {
    const needsAsync =
      chartInit || (schemaMounted && schemaMounted.includes('await'))
    const asyncKeyword = needsAsync ? 'async ' : ''

    mountedLines.push(`onMounted(${asyncKeyword}() => {
${onMountedContent.join('\n')}
})`)
  }

  // 生成图表初始化方法
  const chartMethods = generateChartMethods(components)
  if (chartMethods) {
    mountedLines.push(chartMethods)
  }

  return mountedLines.join('\n\n')
}

// 生成图表初始化代码
function generateChartInit(components, indent = '  ') {
  if (!components || !Array.isArray(components)) {
    return ''
  }

  let initLines = []

  components.forEach(component => {
    const { id, type, children } = component

    if (['bar-chart', 'line-chart', 'pie-chart'].includes(type)) {
      initLines.push(`${indent}// 初始化 ${id}
${indent}init${capitalize(id)}()`)
    }

    // 递归处理子组件
    if (children && children.length > 0) {
      const childInit = generateChartInit(children, indent)
      if (childInit) {
        initLines.push(childInit)
      }
    }
  })

  return initLines.join('\n')
}

// 生成图表方法
function generateChartMethods(components, indent = '') {
  if (!components || !Array.isArray(components)) {
    return ''
  }

  let methodLines = []

  components.forEach(component => {
    const { id, type, config, children } = component

    if (['bar-chart', 'line-chart', 'pie-chart'].includes(type)) {
      const title = config?.title || `${type}图表`
      methodLines.push(`// 初始化${title}
const init${capitalize(id)} = () => {
  nextTick(() => {
    const chartDom = ${id}Chart.value
    if (chartDom) {
      const myChart = echarts.init(chartDom)
      const option = ${generateChartOption(type, config, '      ')}
      myChart.setOption(option)
      
      // 响应式调整
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    }
  })
}`)
    }

    // 递归处理子组件
    if (children && children.length > 0) {
      const childMethods = generateChartMethods(children, indent)
      if (childMethods) {
        methodLines.push(childMethods)
      }
    }
  })

  return methodLines.join('\n\n')
}

// 生成图表配置
function generateChartOption(type, config, indent) {
  const title = config?.title || '图表'

  if (type === 'bar-chart') {
    return `{
${indent}title: {
${indent}  text: '${title}'
${indent}},
${indent}tooltip: {},
${indent}xAxis: {
${indent}  data: ['一月', '二月', '三月', '四月', '五月', '六月']
${indent}},
${indent}yAxis: {},
${indent}series: [{
${indent}  name: '销量',
${indent}  type: 'bar',
${indent}  data: [5, 20, 36, 10, 10, 20]
${indent}}]
${indent.slice(6)}}`
  } else if (type === 'line-chart') {
    return `{
${indent}title: {
${indent}  text: '${title}'
${indent}},
${indent}tooltip: {},
${indent}xAxis: {
${indent}  data: ['一月', '二月', '三月', '四月', '五月', '六月']
${indent}},
${indent}yAxis: {},
${indent}series: [{
${indent}  name: '数据',
${indent}  type: 'line',
${indent}  data: [5, 20, 36, 10, 10, 20]
${indent}}]
${indent.slice(6)}}`
  } else if (type === 'pie-chart') {
    return `{
${indent}title: {
${indent}  text: '${title}'
${indent}},
${indent}tooltip: {},
${indent}series: [{
${indent}  name: '数据',
${indent}  type: 'pie',
${indent}  data: [
${indent}    { value: 335, name: '直接访问' },
${indent}    { value: 310, name: '邮件营销' },
${indent}    { value: 234, name: '联盟广告' },
${indent}    { value: 135, name: '视频广告' },
${indent}    { value: 1548, name: '搜索引擎' }
${indent}  ]
${indent}}]
${indent.slice(6)}}`
  }

  return '{}'
}

// 获取表单子组件的统一方法（用于Script）
function getFormChildrenForScript(component) {
  const { children, config } = component
  let formChildren = []

  // 方式1：直接在children中
  if (children && children.length > 0) {
    formChildren = [...children]
  }

  // 方式2：在config.children中
  if (config?.children && config.children.length > 0) {
    formChildren = [...formChildren, ...config.children]
  }

  // 方式3：在config.rows的行列结构中
  if (config?.rows && config.rows.length > 0) {
    config.rows.forEach(row => {
      if (row.columns && row.columns.length > 0) {
        row.columns.forEach(column => {
          if (column.children && column.children.length > 0) {
            formChildren = [...formChildren, ...column.children]
          }
        })
      }

      // 有些行可能直接有children
      if (row.children && row.children.length > 0) {
        formChildren = [...formChildren, ...row.children]
      }
    })
  }

  return formChildren
}

// 获取绑定的变量名（用于Script）
function getBindVariableNameForScript(child, formComponent) {
  const bindVariableId = child.config?.bindVariable
  if (!bindVariableId || !formComponent?.config?.variables) {
    return null
  }

  const variable = formComponent.config.variables.find(
    v => v.id === bindVariableId
  )
  return variable ? variable.name : null
}

// 获取变量的默认值
function getVariableDefaultValue(child, formComponent) {
  const bindVariableId = child.config?.bindVariable
  if (!bindVariableId || !formComponent?.config?.variables) {
    return null
  }

  const variable = formComponent.config.variables.find(
    v => v.id === bindVariableId
  )
  return variable ? variable.defaultValue : null
}

// 首字母大写
function capitalize(str) {
  return str.charAt(0).toUpperCase() + str.slice(1)
}
