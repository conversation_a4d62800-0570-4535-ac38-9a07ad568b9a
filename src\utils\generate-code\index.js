import { generateVue2Code } from './vue2/generator.js'
import { generateVue3Code } from './vue3/generator.js'

/**
 * 根据类型生成代码
 * @param {Object} schema - 组件schema
 * @param {string} type - 代码类型: 'vue2' | 'vue3'
 * @returns {string} 生成的Vue代码
 */
export function generateCode(schema, type = 'vue3') {
  if (type === 'vue2') {
    return generateVue2Code(schema)
  } else if (type === 'vue3') {
    return generateVue3Code(schema)
  } else {
    throw new Error(`不支持的代码类型: ${type}`)
  }
}

/**
 * 下载生成的代码文件
 * @param {string} code - 生成的代码
 * @param {string} filename - 文件名
 */
export function downloadCode(code, filename = 'generated-component.vue') {
  const blob = new window.Blob([code], { type: 'text/plain;charset=utf-8' })
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}
