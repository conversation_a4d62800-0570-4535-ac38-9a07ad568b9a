<template>
  <div class="div-container-panel">
    <div class="panel-section">
      <h4 class="section-title">外观设置</h4>

      <div class="form-item">
        <label>背景颜色</label>
        <el-color-picker
          v-model="config.backgroundColor"
          size="small"
          @change="updateProperty('config.backgroundColor', $event)"
        />
      </div>

      <div class="form-item">
        <label>边框颜色</label>
        <el-color-picker
          v-model="config.borderColor"
          size="small"
          @change="updateProperty('config.borderColor', $event)"
        />
      </div>

      <div class="form-item">
        <label>边框宽度 (px)</label>
        <el-input-number
          v-model="config.borderWidth"
          :min="0"
          :max="10"
          :step="1"
          size="small"
          @change="updateProperty('config.borderWidth', $event)"
        />
      </div>

      <div class="form-item">
        <label>边框样式</label>
        <el-select
          v-model="config.borderStyle"
          size="small"
          @change="updateProperty('config.borderStyle', $event)"
        >
          <el-option label="实线" value="solid" />
          <el-option label="虚线" value="dashed" />
          <el-option label="点线" value="dotted" />
          <el-option label="无边框" value="none" />
        </el-select>
      </div>

      <div class="form-item">
        <label>圆角 (px)</label>
        <el-input-number
          v-model="config.borderRadius"
          :min="0"
          :max="50"
          :step="1"
          size="small"
          @change="updateProperty('config.borderRadius', $event)"
        />
      </div>

      <div class="form-item">
        <label>内边距 (px)</label>
        <el-input-number
          v-model="config.padding"
          :min="0"
          :max="50"
          :step="1"
          size="small"
          @change="updateProperty('config.padding', $event)"
        />
      </div>
    </div>

    <div class="panel-section">
      <h4 class="section-title">标题栏设置</h4>

      <div class="form-item">
        <label>显示标题栏</label>
        <el-switch
          v-model="config.showTitleBar"
          @change="updateProperty('config.showTitleBar', $event)"
        />
      </div>

      <template v-if="config.showTitleBar">
        <div class="form-item">
          <label>标题内容</label>
          <el-input
            v-model="config.titleText"
            placeholder="请输入标题内容"
            size="small"
            @input="updateProperty('config.titleText', $event)"
          />
        </div>

        <div class="form-item">
          <label>标题高度 (px)</label>
          <el-input-number
            v-model="config.titleHeight"
            :min="20"
            :max="60"
            :step="1"
            size="small"
            @change="updateProperty('config.titleHeight', $event)"
          />
        </div>

        <div class="form-item">
          <label>标题背景色</label>
          <el-color-picker
            v-model="config.titleBackgroundColor"
            size="small"
            @change="updateProperty('config.titleBackgroundColor', $event)"
          />
        </div>

        <div class="form-item">
          <label>标题文字颜色</label>
          <el-color-picker
            v-model="config.titleTextColor"
            size="small"
            @change="updateProperty('config.titleTextColor', $event)"
          />
        </div>

        <div class="form-item">
          <label>标题字体大小 (px)</label>
          <el-input-number
            v-model="config.titleFontSize"
            :min="10"
            :max="24"
            :step="1"
            size="small"
            @change="updateProperty('config.titleFontSize', $event)"
          />
        </div>

        <div class="form-item">
          <label>标题对齐方式</label>
          <el-select
            v-model="config.titleAlign"
            size="small"
            @change="updateProperty('config.titleAlign', $event)"
          >
            <el-option label="左对齐" value="left" />
            <el-option label="居中" value="center" />
            <el-option label="右对齐" value="right" />
          </el-select>
        </div>
      </template>
    </div>

    <div class="panel-section" v-if="config.isContainer">
      <h4 class="section-title">拆分设置</h4>

      <div class="form-item">
        <label>子容器边距 (px)</label>
        <el-input-number
          v-model="config.parentPadding"
          :min="0"
          :max="50"
          :step="1"
          size="small"
          @change="updateProperty('config.parentPadding', $event)"
        />
        <div class="form-item-desc">子容器与父容器边界的距离</div>
      </div>
    </div>

    <div class="panel-section">
      <h4 class="section-title">内容设置</h4>

      <div class="form-item">
        <label>显示内容</label>
        <el-input
          v-model="config.content"
          type="textarea"
          :rows="3"
          placeholder="请输入显示内容"
          size="small"
          @input="updateProperty('config.content', $event)"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
  import { computed } from 'vue'
  import {
    ElInputNumber,
    ElColorPicker,
    ElSelect,
    ElOption,
    ElInput,
    ElSwitch
  } from 'element-plus'

  const props = defineProps({
    selectedComponent: {
      type: Object,
      required: true
    }
  })

  const emit = defineEmits(['update-property'])

  const config = computed(() => props.selectedComponent?.config || {})

  const updateProperty = (property, value) => {
    emit('update-property', property, value)
  }
</script>

<style scoped>
  .div-container-panel {
    padding: 16px;
  }

  .panel-section {
    margin-bottom: 24px;
  }

  .section-title {
    font-size: 14px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 12px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }

  .form-item {
    margin-bottom: 12px;
  }

  .form-item label {
    display: block;
    font-size: 13px;
    color: #606266;
    margin-bottom: 4px;
    font-weight: 500;
  }

  .form-item .el-input-number,
  .form-item .el-select,
  .form-item .el-input {
    width: 100%;
  }

  .form-item .el-color-picker {
    width: 100%;
  }

  .form-item-desc {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
</style>
