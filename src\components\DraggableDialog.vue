<template>
  <div v-if="visible" class="dialog-overlay" @mousedown="handleOverlayClick">
    <div
      ref="dialogRef"
      class="draggable-dialog"
      :style="dialogStyle"
      @mousedown.stop
    >
      <!-- 标题栏 -->
      <div class="dialog-header" @mousedown="startDrag">
        <div class="dialog-title">
          <slot name="title">{{ title }}</slot>
        </div>
        <div class="dialog-actions">
          <button class="dialog-close-btn" @click="handleClose">
            <ElIcon><Close /></ElIcon>
          </button>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="dialog-body">
        <slot></slot>
      </div>

      <!-- 底部按钮区域 -->
      <div v-if="$slots.footer" class="dialog-footer">
        <slot name="footer"></slot>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, watch, nextTick } from 'vue'
  import { ElIcon } from 'element-plus'
  import { Close } from '@element-plus/icons-vue'

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '弹窗标题'
    },
    width: {
      type: [String, Number],
      default: 600
    },
    height: {
      type: [String, Number],
      default: 'auto'
    },
    closeOnClickOutside: {
      type: Boolean,
      default: true
    }
  })

  const emit = defineEmits(['update:visible', 'close'])

  // 拖拽相关状态
  const dialogRef = ref(null)
  const isDragging = ref(false)
  const dragStartPos = ref({ x: 0, y: 0 })
  const dialogStartPos = ref({ x: 0, y: 0 })
  const dialogPosition = ref({ x: 0, y: 0 })

  // 计算弹窗样式
  const dialogStyle = computed(() => {
    const width =
      typeof props.width === 'number' ? `${props.width}px` : props.width
    const height =
      typeof props.height === 'number' ? `${props.height}px` : props.height

    return {
      width,
      height,
      transform: `translate(${dialogPosition.value.x}px, ${dialogPosition.value.y}px)`
    }
  })

  // 监听弹窗显示状态，重置位置
  watch(
    () => props.visible,
    newVisible => {
      if (newVisible) {
        nextTick(() => {
          centerDialog()
        })
      }
    }
  )

  // 居中弹窗
  const centerDialog = () => {
    if (!dialogRef.value) return

    const dialog = dialogRef.value
    const rect = dialog.getBoundingClientRect()
    const windowWidth = window.innerWidth
    const windowHeight = window.innerHeight

    dialogPosition.value = {
      x: (windowWidth - rect.width) / 2 - rect.left,
      y: (windowHeight - rect.height) / 2 - rect.top
    }
  }

  // 开始拖拽
  const startDrag = event => {
    isDragging.value = true
    dragStartPos.value = { x: event.clientX, y: event.clientY }
    dialogStartPos.value = { ...dialogPosition.value }

    document.addEventListener('mousemove', handleDragMove)
    document.addEventListener('mouseup', handleDragEnd)
    document.body.style.userSelect = 'none'
  }

  // 拖拽移动
  const handleDragMove = event => {
    if (!isDragging.value) return

    const deltaX = event.clientX - dragStartPos.value.x
    const deltaY = event.clientY - dragStartPos.value.y

    dialogPosition.value = {
      x: dialogStartPos.value.x + deltaX,
      y: dialogStartPos.value.y + deltaY
    }
  }

  // 拖拽结束
  const handleDragEnd = () => {
    isDragging.value = false
    document.removeEventListener('mousemove', handleDragMove)
    document.removeEventListener('mouseup', handleDragEnd)
    document.body.style.userSelect = ''
  }

  // 处理遮罩层点击
  const handleOverlayClick = event => {
    if (props.closeOnClickOutside && event.target === event.currentTarget) {
      handleClose()
    }
  }

  // 关闭弹窗
  const handleClose = () => {
    emit('update:visible', false)
    emit('close')
  }
</script>

<style scoped>
  .dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .draggable-dialog {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    max-width: 90vw;
    max-height: 90vh;
    min-width: 400px;
    min-height: 200px;
  }

  .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #e4e7ed;
    cursor: move;
    user-select: none;
    background: #f5f7fa;
    border-radius: 8px 8px 0 0;
  }

  .dialog-header:hover {
    background: #ecf5ff;
  }

  .dialog-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .dialog-actions {
    display: flex;
    align-items: center;
  }

  .dialog-close-btn {
    width: 24px;
    height: 24px;
    border: none;
    background: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    color: #909399;
    transition: all 0.2s;
  }

  .dialog-close-btn:hover {
    background: #f56c6c;
    color: white;
  }

  .dialog-body {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
  }

  .dialog-footer {
    padding: 16px 20px;
    border-top: 1px solid #e4e7ed;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  /* 拖拽时的样式 */
  .draggable-dialog.dragging {
    transition: none;
  }
</style>
