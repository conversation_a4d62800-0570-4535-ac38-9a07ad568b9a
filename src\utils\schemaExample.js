// 示例：层级嵌套的schema结构
export const hierarchicalSchemaExample = {
  canvas: {
    width: 1920,
    height: 1080
  },
  version: '2.0',
  variables: {
    // 销售数据示例 - 用于柱状图和折线图
    salesData: [
      { month: '1月', sales: 120, profit: 80, region: '北区' },
      { month: '2月', sales: 200, profit: 130, region: '北区' },
      { month: '3月', sales: 150, profit: 90, region: '北区' },
      { month: '4月', sales: 180, profit: 110, region: '北区' },
      { month: '5月', sales: 220, profit: 140, region: '北区' },
      { month: '1月', sales: 100, profit: 60, region: '南区' },
      { month: '2月', sales: 180, profit: 120, region: '南区' },
      { month: '3月', sales: 130, profit: 80, region: '南区' },
      { month: '4月', sales: 160, profit: 100, region: '南区' },
      { month: '5月', sales: 200, profit: 130, region: '南区' }
    ],
    // 产品分布数据示例 - 用于饼图
    productData: [
      { name: '产品A', value: 335, category: '电子产品' },
      { name: '产品B', value: 310, category: '家居用品' },
      { name: '产品C', value: 234, category: '服装配饰' },
      { name: '产品D', value: 135, category: '食品饮料' },
      { name: '产品E', value: 148, category: '运动户外' }
    ],
    // 用户增长数据示例 - 用于折线图
    userGrowthData: [
      { date: '2024-01', users: 1200, newUsers: 200 },
      { date: '2024-02', users: 1350, newUsers: 150 },
      { date: '2024-03', users: 1500, newUsers: 150 },
      { date: '2024-04', users: 1680, newUsers: 180 },
      { date: '2024-05', users: 1850, newUsers: 170 },
      { date: '2024-06', users: 2020, newUsers: 170 }
    ]
  },
  components: [
    {
      id: 'container-1',
      type: 'div-container',
      name: '主容器',
      position: { x: 50, y: 50 },
      size: { width: 800, height: 600 },
      config: {
        backgroundColor: '#ffffff',
        borderColor: '#e4e7ed',
        borderWidth: 1,
        borderStyle: 'solid',
        borderRadius: 4,
        padding: 16,
        showTitleBar: true,
        titleText: '主容器',
        titleHeight: 32,
        titleBackgroundColor: '#f5f5f5',
        titleTextColor: '#303133'
      },
      children: [
        {
          id: 'container-1-left',
          type: 'div-container',
          name: '左侧容器',
          position: { x: 66, y: 98 },
          size: { width: 384, height: 536 },
          config: {
            backgroundColor: '#f8f9fa',
            borderColor: '#e4e7ed',
            borderWidth: 1,
            borderStyle: 'solid',
            borderRadius: 4,
            padding: 8
          },
          children: [
            {
              id: 'chart-1',
              type: 'pie-chart',
              name: '饼图',
              position: { x: 74, y: 106 },
              size: { width: 368, height: 250 },
              config: {
                title: '销售数据饼图',
                showTitle: true,
                showLegend: true,
                showTooltip: true,
                colors: {
                  theme: 'default',
                  customColors: ['#5470c6', '#91cc75', '#fac858', '#ee6666']
                }
              }
            }
          ]
        },
        {
          id: 'container-1-right',
          type: 'div-container',
          name: '右侧容器',
          position: { x: 458, y: 98 },
          size: { width: 384, height: 536 },
          config: {
            backgroundColor: '#f8f9fa',
            borderColor: '#e4e7ed',
            borderWidth: 1,
            borderStyle: 'solid',
            borderRadius: 4,
            padding: 8
          },
          children: [
            {
              id: 'chart-2',
              type: 'bar-chart',
              name: '柱状图',
              position: { x: 466, y: 106 },
              size: { width: 368, height: 250 },
              config: {
                title: '月度销售柱状图',
                showTitle: true,
                showLegend: true,
                showTooltip: true,
                colors: {
                  theme: 'default',
                  customColors: ['#5470c6']
                },
                barConfig: {
                  type: 'basic',
                  barWidth: 20,
                  showValue: true
                }
              }
            },
            {
              id: 'chart-3',
              type: 'line-chart',
              name: '折线图',
              position: { x: 466, y: 366 },
              size: { width: 368, height: 250 },
              config: {
                title: '趋势折线图',
                showTitle: true,
                showLegend: true,
                showTooltip: true,
                colors: {
                  theme: 'default',
                  customColors: ['#91cc75']
                }
              }
            }
          ]
        }
      ]
    },
    {
      id: 'standalone-chart',
      type: 'bar-chart',
      name: '独立图表',
      position: { x: 900, y: 50 },
      size: { width: 400, height: 300 },
      config: {
        title: '独立柱状图',
        showTitle: true,
        showLegend: true,
        showTooltip: true,
        colors: {
          theme: 'default',
          customColors: ['#fac858']
        }
      },
      children: []
    }
  ]
}

// 测试转换功能的函数
export function testSchemaTransformation() {
  import('./schemaTransform.js').then(({ transformToFlatSchema }) => {
    console.log('=== 层级Schema示例 ===')
    console.log(JSON.stringify(hierarchicalSchemaExample, null, 2))

    console.log('\n=== 转换为平铺Schema ===')
    const flatResult = transformToFlatSchema(
      hierarchicalSchemaExample.components
    )
    console.log(
      JSON.stringify(
        { ...hierarchicalSchemaExample, components: flatResult },
        null,
        2
      )
    )
  })
}
