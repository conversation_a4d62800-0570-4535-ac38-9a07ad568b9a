<template>
  <div class="sidebar-panel">
    <!-- 图标菜单 -->
    <div class="icon-menu" :class="{ collapsed: isCollapsed }">
      <div
        class="menu-item"
        :class="{ active: activePanel === 'components' }"
        @click="selectPanel('components')"
        title="组件库"
      >
        <ElIcon><Grid /></ElIcon>
        <span v-if="!isCollapsed">组件</span>
      </div>
      <div
        class="menu-item"
        :class="{ active: activePanel === 'variables' }"
        @click="selectPanel('variables')"
        title="变量管理"
      >
        <ElIcon><Memo /></ElIcon>
        <span v-if="!isCollapsed">变量</span>
      </div>
      <div
        class="menu-item"
        :class="{ active: activePanel === 'methods' }"
        @click="selectPanel('methods')"
        title="方法管理"
      >
        <ElIcon><SetUp /></ElIcon>
        <span v-if="!isCollapsed">方法</span>
      </div>
      <div
        class="menu-item"
        :class="{ active: activePanel === 'lifecycle' }"
        @click="selectPanel('lifecycle')"
        title="生命周期"
      >
        <ElIcon><Clock /></ElIcon>
        <span v-if="!isCollapsed">生命周期</span>
      </div>
      <div
        class="menu-item"
        :class="{ active: activePanel === 'styles' }"
        @click="selectPanel('styles')"
        title="全局样式"
      >
        <ElIcon><Brush /></ElIcon>
        <span v-if="!isCollapsed">样式</span>
      </div>

      <!-- 折叠按钮 -->
      <div class="collapse-btn" @click="toggleCollapse">
        <ElIcon><ArrowLeft v-if="!isCollapsed" /><ArrowRight v-else /></ElIcon>
      </div>
    </div>

    <!-- 面板内容 -->
    <div v-if="!isCollapsed" class="panel-content">
      <!-- 组件库面板 -->
      <ComponentPanel
        v-if="activePanel === 'components'"
        @drag-start="handleDragStart"
      />

      <!-- 变量管理面板 -->
      <VariablePanel
        v-if="activePanel === 'variables'"
        :variables="props.globalVariables"
        @update-variables="handleUpdateVariables"
      />

      <!-- 方法管理面板 -->
      <MethodPanel
        v-if="activePanel === 'methods'"
        :methods="props.globalMethods"
        @update-methods="handleUpdateMethods"
      />

      <!-- 生命周期面板 -->
      <LifecyclePanel
        v-if="activePanel === 'lifecycle'"
        :mounted="props.globalMounted"
        @update-mounted="handleUpdateMounted"
      />

      <!-- 全局样式面板 -->
      <StylePanel
        v-if="activePanel === 'styles'"
        :styles="props.globalStyles"
        @update-styles="handleUpdateStyles"
      />
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import { ElIcon } from 'element-plus'
  import {
    Grid,
    Memo,
    SetUp,
    Clock,
    Brush,
    ArrowLeft,
    ArrowRight
  } from '@element-plus/icons-vue'
  import ComponentPanel from './ComponentPanel.vue'
  import VariablePanel from './VariablePanel.vue'
  import MethodPanel from './MethodPanel.vue'
  import LifecyclePanel from './LifecyclePanel.vue'
  import StylePanel from './StylePanel.vue'

  const props = defineProps({
    globalVariables: {
      type: Object,
      default: () => ({})
    },
    globalMethods: {
      type: String,
      default: ''
    },
    globalMounted: {
      type: String,
      default: ''
    },
    globalStyles: {
      type: String,
      default: ''
    }
  })

  const emit = defineEmits([
    'drag-start',
    'update-variables',
    'update-methods',
    'update-mounted',
    'update-styles'
  ])

  const isCollapsed = ref(false)
  const activePanel = ref('components')

  const selectPanel = panel => {
    activePanel.value = panel
    if (isCollapsed.value) {
      isCollapsed.value = false
    }
  }

  const toggleCollapse = () => {
    isCollapsed.value = !isCollapsed.value
  }

  const handleDragStart = component => {
    emit('drag-start', component)
  }

  const handleUpdateVariables = variables => {
    emit('update-variables', variables)
  }

  const handleUpdateMethods = methods => {
    emit('update-methods', methods)
  }

  const handleUpdateMounted = mounted => {
    emit('update-mounted', mounted)
  }

  const handleUpdateStyles = styles => {
    emit('update-styles', styles)
  }
</script>

<style scoped>
  .sidebar-panel {
    display: flex;
    height: 100%;
    background: #f8f9fa;
    border-right: 1px solid #e4e7ed;
  }

  .icon-menu {
    width: 60px;
    background: #ffffff;
    border-right: 1px solid #e4e7ed;
    display: flex;
    flex-direction: column;
    padding: 8px 0;
    transition: width 0.3s ease;
  }

  .icon-menu.collapsed {
    width: 60px;
  }

  .menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 6px;
    margin: 2px 6px;
    gap: 4px;
    font-size: 12px;
    color: #666;
  }

  .menu-item:hover {
    background: #f0f9ff;
    color: #409eff;
  }

  .menu-item.active {
    background: #409eff;
    color: #ffffff;
  }

  .menu-item span {
    font-size: 10px;
    text-align: center;
    white-space: nowrap;
  }

  .collapse-btn {
    margin-top: auto;
    padding: 8px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    color: #666;
    transition: color 0.2s ease;
  }

  .collapse-btn:hover {
    color: #409eff;
  }

  .panel-content {
    width: 320px;
    overflow-y: auto;
    background: #f8f9fa;
    flex-shrink: 0;
  }
</style>
