<template>
  <div class="monaco-editor-container">
    <div ref="editorContainer" class="editor-container"></div>
  </div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'

  const props = defineProps({
    modelValue: {
      type: String,
      default: ''
    },
    language: {
      type: String,
      default: 'javascript'
    },
    theme: {
      type: String,
      default: 'vs-dark'
    },
    options: {
      type: Object,
      default: () => ({})
    },
    height: {
      type: [String, Number],
      default: '400px'
    },
    width: {
      type: [String, Number],
      default: '100%'
    },
    readonly: {
      type: Boolean,
      default: false
    }
  })

  const emit = defineEmits(['update:modelValue', 'change', 'ready'])

  const editorContainer = ref(null)
  let editor = null
  let monaco = null

  const defaultOptions = {
    automaticLayout: true,
    fontSize: 14,
    tabSize: 2,
    insertSpaces: true,
    wordWrap: 'on',
    lineNumbers: 'on',
    minimap: { enabled: false },
    scrollBeyondLastLine: false,
    folding: true,
    foldingHighlight: true,
    foldingStrategy: 'indentation',
    showFoldingControls: 'always',
    unfoldOnClickAfterEndOfLine: false,
    contextmenu: true,
    mouseWheelZoom: true,
    smoothScrolling: true,
    cursorBlinking: 'blink',
    cursorSmoothCaretAnimation: true,
    renderWhitespace: 'boundary',
    renderControlCharacters: false,
    fontLigatures: true,
    suggest: {
      insertMode: 'replace',
      filterGraceful: true,
      showKeywords: true,
      showSnippets: true,
      showFunctions: true,
      showVariables: true
    },
    quickSuggestions: {
      other: true,
      comments: false,
      strings: false
    },
    parameterHints: {
      enabled: true
    },
    bracketPairColorization: {
      enabled: true
    },
    guides: {
      bracketPairs: 'active',
      bracketPairsHorizontal: 'active',
      highlightActiveBracketPair: true,
      indentation: true
    }
  }

  // 初始化Monaco Editor
  const initMonaco = async () => {
    try {
      // 动态导入Monaco Editor
      const monacoModule = await import('monaco-editor')
      monaco = monacoModule.default || monacoModule

      // 配置Monaco Editor的基础路径
      if (window.MonacoEnvironment === undefined) {
        window.MonacoEnvironment = {
          getWorkerUrl: function (moduleId, label) {
            if (label === 'json') {
              return '/monaco-editor/esm/vs/language/json/json.worker.js'
            }
            if (label === 'css' || label === 'scss' || label === 'less') {
              return '/monaco-editor/esm/vs/language/css/css.worker.js'
            }
            if (
              label === 'html' ||
              label === 'handlebars' ||
              label === 'razor'
            ) {
              return '/monaco-editor/esm/vs/language/html/html.worker.js'
            }
            if (label === 'typescript' || label === 'javascript') {
              return '/monaco-editor/esm/vs/language/typescript/ts.worker.js'
            }
            return '/monaco-editor/esm/vs/editor/editor.worker.js'
          }
        }
      }

      // 创建编辑器
      await nextTick()

      if (editorContainer.value) {
        const mergedOptions = {
          ...defaultOptions,
          ...props.options,
          value: props.modelValue,
          language: props.language,
          theme: props.theme,
          readOnly: props.readonly
        }

        editor = monaco.editor.create(editorContainer.value, mergedOptions)

        // 监听内容变化
        editor.onDidChangeModelContent(() => {
          const value = editor.getValue()
          emit('update:modelValue', value)
          emit('change', value)
        })

        // 设置编辑器尺寸
        updateEditorSize()

        emit('ready', editor)
      }
    } catch (error) {
      console.error('Monaco Editor 初始化失败:', error)
    }
  }

  // 更新编辑器尺寸
  const updateEditorSize = () => {
    if (editor && editorContainer.value) {
      const container = editorContainer.value
      const height =
        typeof props.height === 'number' ? `${props.height}px` : props.height
      const width =
        typeof props.width === 'number' ? `${props.width}px` : props.width

      container.style.height = height
      container.style.width = width

      editor.layout()
    }
  }

  // 监听modelValue变化
  watch(
    () => props.modelValue,
    newValue => {
      if (editor && editor.getValue() !== newValue) {
        editor.setValue(newValue || '')
      }
    }
  )

  // 监听语言变化
  watch(
    () => props.language,
    newLanguage => {
      if (editor && monaco) {
        const model = editor.getModel()
        if (model) {
          monaco.editor.setModelLanguage(model, newLanguage)
        }
      }
    }
  )

  // 监听主题变化
  watch(
    () => props.theme,
    newTheme => {
      if (monaco) {
        monaco.editor.setTheme(newTheme)
      }
    }
  )

  // 监听尺寸变化
  watch([() => props.height, () => props.width], () => {
    updateEditorSize()
  })

  // 监听只读状态变化
  watch(
    () => props.readonly,
    readonly => {
      if (editor) {
        editor.updateOptions({ readOnly: readonly })
      }
    }
  )

  // 暴露方法供父组件使用
  const getValue = () => {
    return editor ? editor.getValue() : ''
  }

  const setValue = value => {
    if (editor) {
      editor.setValue(value || '')
    }
  }

  const focus = () => {
    if (editor) {
      editor.focus()
    }
  }

  const getEditor = () => {
    return editor
  }

  const formatDocument = () => {
    if (editor) {
      editor.getAction('editor.action.formatDocument').run()
    }
  }

  defineExpose({
    getValue,
    setValue,
    focus,
    getEditor,
    formatDocument
  })

  onMounted(() => {
    initMonaco()
  })

  onUnmounted(() => {
    if (editor) {
      editor.dispose()
    }
  })
</script>

<style scoped>
  .monaco-editor-container {
    width: 100%;
    height: 100%;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;
  }

  .editor-container {
    width: 100%;
    height: 100%;
  }
</style>
